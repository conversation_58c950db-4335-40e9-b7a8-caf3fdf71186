package com.yiwise.dialogflow.schedule;

import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.service.BotRecommendService;
import com.yiwise.middleware.redis.service.RedisOpsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class BotRecommendScheduler {

    @Resource
    private RedisOpsService redisService;

    @Resource
    private BotRecommendService botRecommendService;

    @Scheduled(cron = "0 0 0 ? * 6")
    public void execute() {
        String key = RedisKeyCenter.getBotRecommendLockKey();
        if (redisService.setIfAbsent(key, "", TimeUnit.MINUTES.toSeconds(1))) {
            try {
                LocalDateTime now = LocalDateTime.now();
                botRecommendService.textParsingByRangeTime(now.minusDays(7), now, 2000L);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                redisService.delete(key);
            }
        }
    }
}