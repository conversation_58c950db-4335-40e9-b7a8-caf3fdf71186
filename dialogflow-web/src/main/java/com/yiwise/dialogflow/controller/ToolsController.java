package com.yiwise.dialogflow.controller;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.dto.response.MagicBotTemplateInfo;
import com.yiwise.dialogflow.engine.resource.PatternCache;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.entity.bo.SourceRefBO;
import com.yiwise.dialogflow.entity.enums.IntentRefTypeEnum;
import com.yiwise.dialogflow.entity.enums.SourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.TemplateVariableConfigTypeEnum;
import com.yiwise.dialogflow.entity.enums.VariableTypeEnum;
import com.yiwise.dialogflow.entity.po.SourceRefPO;
import com.yiwise.dialogflow.entity.po.VariablePO;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.intent.IntentCorpusExportService;
import com.yiwise.dialogflow.service.intent.IntentCorpusService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.yiwise.dialogflow.service.AnswerAudioMappingService;
import com.yiwise.dialogflow.service.MaintainToolsService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("apiBot/v3/tools")
public class ToolsController {

    private static final Logger log = LoggerFactory.getLogger(ToolsController.class);
    @Resource
    private MaintainToolsService maintainToolsService;

    @Resource
    private AnswerAudioMappingService answerAudioMappingService;

    @Resource
    private IntentCorpusService intentCorpusService;

    @Resource
    private IntentCorpusExportService intentCorpusExportService;

    @Resource
    private SourceRefService sourceRefService;

    @Resource
    private VariableService variableService;

    @Resource
    private BotService botService;


    @NoLogin
    @InnerOnly
    @GetMapping("resetAnswerLabelIfDuplicate")
    public ResultObject resetAnswerLabelIfDuplicate(Long botId) {
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        maintainToolsService.resetAnswerLabelIfDuplicate(botId);
        return ResultObject.success(true);
    }

    @NoLogin
    @InnerOnly
    @GetMapping("resetAnswerText")
    public ResultObject resetAnswerText(Long botId) {
        answerAudioMappingService.maintainText();
        return ResultObject.success(true);
    }

    @NoLogin
    @InnerOnly
    @GetMapping("getIntentCorpusListByBotId")
    public ResultObject getIntentCorpusListByBotId(@NotNull(message = "botId不能为空") Long botId) {
        return ResultObject.success(intentCorpusService.findByBotId(botId));
    }

    @NoLogin
    @InnerOnly
    @GetMapping("fixIntentBuildInCorpus")
    public ResultObject fixIntentBuildInCorpus(@NotNull(message = "botId不能为空") Long botId) {
        intentCorpusService.fixIntentBuildInCorpus(botId);
        return ResultObject.success();
    }

    @NoLogin
    @InnerOnly
    @GetMapping("getPatternCacheUseInfo")
    public ResultObject<Map<String, Long>> getPatternCacheUseInfo() {
        return ResultObject.success(PatternCache.getCacheInfo());
    }

    @NoLogin
    @InnerOnly
    @GetMapping("exportAllIntentCorpus")
    public ResultObject<String> exportAllIntentCorpus() {
        return ResultObject.success(intentCorpusExportService.exportAllAsCsv());
    }

    @NoLogin
    @InnerOnly
    @GetMapping("fixTemplateVariableRef")
    public ResultObject<String> fixTemplateVariableRef(@NotNull Long botId) {
        List<SourceRefPO> refList = sourceRefService.getListBySourceTypeAndRefType(botId, SourceTypeEnum.VARIABLE, IntentRefTypeEnum.TEMPLATE_VARIABLE);
        if (CollectionUtils.isEmpty(refList)) {
            return ResultObject.success("引用为空, 不需要处理");
        }

        List<String> sourceIdList = refList.stream().map(SourceRefPO::getSourceId).distinct().collect(Collectors.toList());
        sourceRefService.deleteBySourceIdList(botId, sourceIdList, IntentRefTypeEnum.TEMPLATE_VARIABLE);

        List<VariablePO> varList = variableService.getListByBotId(botId);


        Map<String, String> varName2IdMap = MyCollectionUtils.listToConvertMap(varList, VariablePO::getName, VariablePO::getId);
        for (VariablePO variable : varList) {
            if (VariableTypeEnum.isTemplateVariable(variable.getType())) {
                Set<String> dependVariableIdSet = processDependVar(variable, varName2IdMap);
                if (CollectionUtils.isNotEmpty(dependVariableIdSet)) {
                    updateDependResourceRef(variable, dependVariableIdSet);
                }
            }
        }

        return ResultObject.success("ok");
    }

    @NoLogin
    @InnerOnly
    @GetMapping("fixAllTemplateVariableRef")
    public ResultObject<String> fixAllTemplateVariableRef() {
        List<MagicBotTemplateInfo> botList = botService.getMagicBotTemplateByEasyCallVersion(null);

        for (MagicBotTemplateInfo templateInfo : botList) {
            try {
                fixTemplateVariableRef(templateInfo.getBotId());
            } catch (Exception e) {
                log.warn("[LogHub_Warn]处理异常", e);
            }
        }
        return ResultObject.success("ok");
    }

    private Set<String> processDependVar(VariablePO variable, Map<String, String> varName2IdMap) {
        Set<String> dependVariableIdSet = new HashSet<>();
        Set<String> dependVariableNameSet = new HashSet<>();

        if (VariableTypeEnum.isTemplateVariable(variable.getType())) {
            if (TemplateVariableConfigTypeEnum.PROMPT.equals(variable.getTemplateVariableConfigType())) {
                if (StringUtils.isNotBlank(variable.getPrompt())) {
                    dependVariableNameSet.addAll(new AnswerPlaceholderSplitter(variable.getPrompt(), false).getPlaceholderSet());
                }
                if (StringUtils.isNotBlank(variable.getTemplateSentence())) {
                    dependVariableNameSet.addAll(new AnswerPlaceholderSplitter(variable.getTemplateSentence(), false).getPlaceholderSet());
                }
            }

            if (CollectionUtils.isNotEmpty(dependVariableNameSet)) {
                for (String dependVarName : dependVariableNameSet) {
                    if (varName2IdMap.containsKey(dependVarName)) {
                        dependVariableIdSet.add(varName2IdMap.get(dependVarName));
                    }
                }
            }
        }
        return dependVariableIdSet;
    }


    private void updateDependResourceRef(VariablePO templateVariable, Set<String> dependVariableIdSet) {
        // 先删除, 再添加
        if (CollectionUtils.isNotEmpty(dependVariableIdSet)) {
            sourceRefService.saveSourceRef(buildParam(templateVariable, dependVariableIdSet));
        }
    }

    private SourceRefBO buildParam(VariablePO templateVariable, Set<String> sourceIdSet) {
        SourceRefBO sourceRefBO = new SourceRefBO();
        sourceRefBO.setBotId(templateVariable.getBotId());
        sourceRefBO.setSourceType(SourceTypeEnum.VARIABLE);
        sourceRefBO.setSourceIdSet(sourceIdSet);
        sourceRefBO.setRefId(templateVariable.getId());
        sourceRefBO.setRefLabel(templateVariable.getName());
        sourceRefBO.setRefType(IntentRefTypeEnum.TEMPLATE_VARIABLE);
        return sourceRefBO;
    }

}
