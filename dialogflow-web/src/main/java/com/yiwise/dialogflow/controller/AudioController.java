package com.yiwise.dialogflow.controller;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.po.AudioPO;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.helper.AddOssPrefixSerializer;
import com.yiwise.dialogflow.service.remote.UserService;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/2/17
 */
@RestController
@RequestMapping("/apiBot/v3/audio")
public class AudioController {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private UserService userService;

    @GetMapping("/list")
    public ResultObject<PageResultObject<Map<String, Object>>> list(
            @RequestParam(required = false) Long botId,
            @RequestParam(required = false) Long recordUserId,
            @RequestParam(required = false) Integer samplesPerSec,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize,
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") @RequestParam(required = false) LocalDateTime createTimeGte) {

        Query query = new Query();
        if (Objects.nonNull(botId)) {
            query.addCriteria(Criteria.where("botId").is(botId));
        }
        if (Objects.nonNull(recordUserId)) {
            query.addCriteria(Criteria.where("recordUserId").is(recordUserId));
        }
        if (Objects.nonNull(samplesPerSec)) {
            query.addCriteria(Criteria.where("samplesPerSec").is(samplesPerSec));
        }
        if (Objects.nonNull(createTimeGte)) {
            query.addCriteria(Criteria.where("createTime").gte(createTimeGte));
        }
        long count = mongoTemplate.count(query, AudioPO.COLLECTION_NAME);

        query.with(PageRequest.of(pageNum - 1, pageSize));
        query.with(Sort.by(Sort.Order.desc("createTime")));

        List<AudioPO> audioList = mongoTemplate.find(query, AudioPO.class, AudioPO.COLLECTION_NAME);
        List<Map<String, Object>> resultList = new ArrayList<>(audioList.size());
        for (AudioPO audio : audioList) {
            Map<String, Object> map = new HashMap<>(7);
            map.put("botId", audio.getBotId());
            map.put("recordUserId", audio.getRecordUserId());
            map.put("recordUserName", Optional.ofNullable(audio.getRecordUserId()).map(userService::getUserById).map(UserPO::getName).orElse(""));
            map.put("text", audio.getText());
            map.put("samplesPerSec", audio.getSamplesPerSec());
            map.put("fullUrl", AddOssPrefixSerializer.getAddOssPrefixUrl(audio.getUrl()));
            map.put("createTime", audio.getCreateTime());
            resultList.add(map);
        }
        return ResultObject.success(PageResultObject.of(resultList, pageNum, pageSize, (int) count));
    }
}