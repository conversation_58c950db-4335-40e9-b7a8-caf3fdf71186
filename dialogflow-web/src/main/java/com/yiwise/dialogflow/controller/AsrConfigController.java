package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.aop.TenantIsolation;
import com.yiwise.dialogflow.entity.bo.AsrMetaData;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrUpdateVO;
import com.yiwise.dialogflow.service.BotConfigService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Validated
@RestController
@RequestMapping("apiBot/v3/config/asr")
public class AsrConfigController {

    @Resource
    private BotConfigService botConfigService;

    @GetMapping("getByBotId")
    public ResultObject<AsrMetaData> getByBotId(Long botId) {
        return ResultObject.success(botConfigService.getAsrMetaData(botId));
    }

    @PostMapping("update")
    @TenantIsolation("#asrUpdateVO.botId")
    public ResultObject<String> update(@RequestBody AsrUpdateVO asrUpdateVO) {
        asrUpdateVO.setCurrentUserId(SecurityUtils.getUserId());
        asrUpdateVO.setTenantId(SecurityUtils.getTenantId());
        botConfigService.updateAsrMetaData(asrUpdateVO);
        return ResultObject.success("请求成功");
    }
}
