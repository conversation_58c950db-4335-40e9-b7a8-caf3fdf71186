package com.yiwise.dialogflow.controller.botgenerate;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.po.botgenerate.BotGenerateTemplatePO;
import com.yiwise.dialogflow.entity.query.botgenerate.BotGenerateTemplateQueryVO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotGenerateResultVO;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotGenerateTemplateVO;
import com.yiwise.dialogflow.service.botgenerate.BotGenerateTemplateService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * bot生成/表单模板
 */
@Validated
@RestController
@RequestMapping("apiBot/v3/botGenerate/template")
public class BotGenerateTemplateController {

    @Resource
    private BotGenerateTemplateService botGenerateTemplateService;

    /**
     * 查询表单模板列表
     * @param condition 查询条件
     * @return 模板分页列表
     */
    @PostMapping("list")
    public ResultObject<PageResultObject<BotGenerateTemplateVO>> list(@RequestBody BotGenerateTemplateQueryVO condition) {
        return ResultObject.success(botGenerateTemplateService.queryByCondition(condition));
    }

    /**
     * 查询所有创建者列表
     * @return 创建人列表
     */
    @GetMapping("getAllCreateUserList")
    public ResultObject<List<IdNamePair<Long, String>>> getAllCreateUserList() {
        return ResultObject.success(botGenerateTemplateService.getAllCreateUserInfoList());
    }

    /**
     * 创建表单模板
     * @param template 模板信息
     * @return 模板信息
     */
    @PostMapping("create")
    public ResultObject<BotGenerateTemplatePO> create(@RequestBody BotGenerateTemplatePO template) {
        return ResultObject.success(botGenerateTemplateService.create(template, getUserId()));
    }

    /**
     * 更新表单模板
     * @param template 模板信息
     * @return 模板信息
     */
    @PostMapping("update")
    public ResultObject<BotGenerateTemplatePO> update(@RequestBody BotGenerateTemplatePO template) {
        return ResultObject.success(botGenerateTemplateService.update(template, getUserId()));
    }

    /**
     * 删除表单模板
     * @param templateId 模板id
     * @return 模板信息
     */
    @PostMapping("deleteById")
    public ResultObject deleteById(@NotBlank(message = "templateId不能为空") String templateId) {
        botGenerateTemplateService.deleteById(templateId, getUserId());
        return ResultObject.success();
    }

    /**
     * 由模板生成bot, 列表页点击生成按钮
     * @param templateId 模板id
     * @param ignoreWarning 是否忽略告警项
     * @return 生成结果
     */
    @PostMapping("generate")
    public ResultObject<BotGenerateResultVO> generate(@NotBlank(message = "templateId不能为空") String templateId,
                                                     Boolean ignoreWarning) {
        return ResultObject.success(botGenerateTemplateService.generate(templateId, getUserId(), ignoreWarning));
    }

    /**
     * 保存模板并生成bot
     * @param template 模板信息
     * @return 生成结果
     */
    @PostMapping("updateAndGenerate")
    public ResultObject<BotGenerateResultVO> updateAndGenerate(@RequestBody BotGenerateTemplateVO template) {
        return ResultObject.success(botGenerateTemplateService.updateAndGenerate(template, getUserId()));
    }

    /**
     * 复制模板
     * @param templateId 模板id
     * @return 生成结果
     */
    @PostMapping("copy")
    public ResultObject<BotGenerateTemplatePO> copy(@NotBlank(message = "templateId不能为空") String templateId) {
        return ResultObject.success(botGenerateTemplateService.copy(templateId, getUserId()));
    }

    /**
     * 根据id查看模板祥祥
     * @param templateId 模板id
     * @return
     */
    @GetMapping("getById")
    public ResultObject<BotGenerateTemplateVO> getById(@NotBlank(message = "templateId不能为空") String templateId) {
        return ResultObject.success(botGenerateTemplateService.getVOById(templateId).orElse(null));
    }

    private Long getUserId() {
        return SecurityUtils.getUserId();
    }

}
