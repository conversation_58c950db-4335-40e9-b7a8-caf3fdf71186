package com.yiwise.dialogflow.controller;


import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.aop.TenantIsolation;
import com.yiwise.dialogflow.entity.vo.BotTextSearchReplaceRequestVO;
import com.yiwise.dialogflow.entity.vo.MutiBotTextSearchReplaceRequestVO;
import com.yiwise.dialogflow.entity.vo.MutiBotTextSearchReplaceResultVO;
import com.yiwise.dialogflow.service.TextSearchService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 查找替换
 */
@RestController
@RequestMapping("/apiBot/v3/textSearch")
public class TextSearchController {
    @Resource
    private TextSearchService textSearchService;

    /**
     * 文本查找
     * uri:/apiBot/v3/textSearch/search
     *
     * @param botTextSearchReplaceRequestVO
     * @return
     */
    @PostMapping(value = "/search")
    public ResultObject<List<MutiBotTextSearchReplaceResultVO>> search(@RequestBody BotTextSearchReplaceRequestVO botTextSearchReplaceRequestVO) {
        return ResultObject.success(textSearchService.search(botTextSearchReplaceRequestVO));
    }

    /**
     * 文本替换
     * uri:/apiBot/v3/textSearch/replace
     */
    @PostMapping(value = "/replace")
    @TenantIsolation("#mutiBotTextSearchReplaceRequestVO.botTextSearchReplaceRequestVOList.![botId]")
    public ResultObject<String> replace(@RequestBody MutiBotTextSearchReplaceRequestVO mutiBotTextSearchReplaceRequestVO) {
        mutiBotTextSearchReplaceRequestVO.setUserId(SecurityUtils.getUserId());
        textSearchService.replace(mutiBotTextSearchReplaceRequestVO);
        return ResultObject.success("替换成功");
    }

    /**
     * 话术批量保存
     */
    @PostMapping(value = "/save")
    @TenantIsolation("#mutiBotTextSearchReplaceRequestVO.botTextSearchReplaceRequestVOList.![botId]")
    public ResultObject<String> save(@RequestBody MutiBotTextSearchReplaceRequestVO mutiBotTextSearchReplaceRequestVO) {
        mutiBotTextSearchReplaceRequestVO.setUserId(SecurityUtils.getUserId());
        textSearchService.batchUpdate(mutiBotTextSearchReplaceRequestVO);
        return ResultObject.success("替换成功");
    }

    /**
     * 删除
     * uri:/apiBot/v3/textSearch/delete
     *
     * @param mutiBotTextSearchReplaceRequestVO
     * @return
     */
    @PostMapping(value = "/delete")
    @TenantIsolation("#mutiBotTextSearchReplaceRequestVO.botTextSearchReplaceRequestVOList.![botId]")
    public ResultObject<String> delete(@RequestBody MutiBotTextSearchReplaceRequestVO mutiBotTextSearchReplaceRequestVO) {
        textSearchService.delete(mutiBotTextSearchReplaceRequestVO);
        return ResultObject.success("删除成功");
    }
}
