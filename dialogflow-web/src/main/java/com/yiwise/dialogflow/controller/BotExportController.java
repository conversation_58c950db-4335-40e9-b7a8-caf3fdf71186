package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.DialogFlowExtraRuleConditionNodePO;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.service.BotExportService;
import com.yiwise.dialogflow.service.BotImportService;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.FolderService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 话术导出
 *
 * <AUTHOR>
 * @date 2022/9/1
 */
@Validated
@RestController
@RequestMapping("/apiBot/v3/botExport")
public class BotExportController {

    private static final Logger log = LoggerFactory.getLogger(BotExportController.class);
    @Resource
    private BotExportService botExportService;

    @Resource
    private BotImportService botImportService;

    @Resource
    private FolderService folderService;

    @Resource
    private BotService botService;

    /**
     * 导出话术文本
     *
     * @param botId botId
     * @return txt文件链接
     */
    @GetMapping("/exportText")
    public ResultObject<String> exportText(Long botId) {
        exportLog(botId, "文本");
        return ResultObject.success(botExportService.exportText(botId, SecurityUtils.getUserId()));
    }

    private void exportLog(Long botId, String msg) {
        try {
            BotPO bot = botService.getById(botId);
            if (Objects.nonNull(bot) ) {
                UserPO user = SecurityUtils.getUserInfo();
                if (Objects.nonNull(user)) {
                    log.debug("[LogHub] 用户:{} 正在导出话术:{}{}, {}", user.getName(), botId, bot.getName(), msg);
                }
            }
        } catch (Exception e) {
            log.warn("导出日志失败", e);
        }
    }

    private List<Long> obtainBotIdList(BotBatchExportRequestVO request) {
        List<Long> botIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getFolderIdList())) {
            List<Long> finalFolderIdList = new ArrayList<>(request.getFolderIdList());
            request.getFolderIdList().stream().map(folderService::subFolderIdList).forEach(finalFolderIdList::addAll);
            botIdList.addAll(folderService.getBotIdListByFolderIdList(finalFolderIdList));
        }
        if (CollectionUtils.isNotEmpty(request.getBotIdList())) {
            botIdList.addAll(request.getBotIdList());
        }
        if (CollectionUtils.isEmpty(botIdList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "机器人ID列表不能为空");
        }
        return botIdList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 批量导出话术文本
     *
     * @param request botId列表
     * @return 压缩文件链接
     */
    @PostMapping("/batchExportText")
    public ResultObject<String> batchExportText(@RequestBody @Valid BotBatchExportRequestVO request) {
        return ResultObject.success(botExportService.batchExportText(obtainBotIdList(request), SecurityUtils.getUserId()));
    }

    /**
     * 导出word格式话术
     *
     * @param botId botId
     * @return word文档链接
     */
    @GetMapping("/exportWord")
    public ResultObject<String> exportWord(Long botId) {
        exportLog(botId, "word");
        return ResultObject.success(botExportService.exportWord(botId, SecurityUtils.getUserId(), true));
    }

    /**
     * 导出轻量化模板活动配置-word格式话术
     *
     * @param botId botId
     * @param activityId activityId
     * @return word文档链接
     */
    @GetMapping("/exportMagicTemplateWord")
    public ResultObject<String> exportMagicTemplateWord(@NotNull Long botId, @NotNull Long activityId) {
        exportLog(botId, "magicTemplateWord");
        return ResultObject.success(botExportService.exportMagicTemplateWord(botId, activityId));
    }

    /**
     * 批量导出word格式话术
     *
     * @param request botId列表
     * @return 压缩文件链接
     */
    @PostMapping("/batchExportWord")
    public ResultObject<String> batchExportWord(@RequestBody @Valid BotBatchExportRequestVO request) {
        return ResultObject.success(botExportService.batchExportWord(obtainBotIdList(request), SecurityUtils.getUserId()));
    }

    /**
     * 导出XMind格式话术
     *
     * @param botId botId
     * @return XMind文件链接
     */
    @GetMapping("/exportXMind")
    public ResultObject<String> exportXMind(Long botId) {
        exportLog(botId, "xmind");
        return ResultObject.success(botExportService.exportXMind(botId, SecurityUtils.getUserId()));
    }

    /**
     * 批量导出XMind格式话术
     *
     * @param request botId列表
     * @return 压缩文件链接
     */
    @PostMapping("/batchExportXMind")
    public ResultObject<String> batchExportXMind(@RequestBody @Valid BotBatchExportRequestVO request) {
        return ResultObject.success(botExportService.batchExportXMind(obtainBotIdList(request), SecurityUtils.getUserId()));
    }

    /**
     * 导出Excel格式话术
     *
     * @param botId botId
     * @return Excel文件链接
     */
    @GetMapping("/exportExcel")
    public ResultObject<String> exportExcel(Long botId) {
        exportLog(botId, "excel");
        return ResultObject.success(botExportService.exportExcel(botId, SecurityUtils.getUserId(), true));
    }

    /**
     * 批量导出Excel格式话术
     *
     * @param request botId列表
     * @return 压缩文件链接
     */
    @PostMapping("/batchExportExcel")
    public ResultObject<String> batchExportExcel(@RequestBody @Valid BotBatchExportRequestVO request) {
        return ResultObject.success(botExportService.batchExportExcel(obtainBotIdList(request), SecurityUtils.getUserId()));
    }

    /**
     * 导出部分话术Excel
     *
     * @param excelVO 选中的节点信息
     * @return Excel文件链接
     */
    @PostMapping("/exportPartExcel")
    public ResultObject<String> exportPartExcel(@RequestBody @Valid BotExportPartExcelVO excelVO) {
        return ResultObject.success(botExportService.exportPartExcel(excelVO, SecurityUtils.getUserId()));
    }

    /**
     * 导出部分话术默认选中的节点列表
     *
     * @param botId botId
     * @return 节点列表
     */
    @GetMapping("/defaultSelectedNodeList")
    public ResultObject<List<DialogFlowExtraRuleConditionNodePO>> defaultSelectedNodeList(Long botId){
        return ResultObject.success(botExportService.defaultSelectedNodeList(botId));
    }

    /**
     * 导出bot全部的资源(节点配置, 问答知识, 音频等等)
     * @param botId botId
     * @return oss文件链接
     */
    @GetMapping("exportFullResource")
    public ResultObject<String> exportFullResource(Long botId) {
        return ResultObject.success(botExportService.exportFullResource(botId, SecurityUtils.getUserId()));
    }

    @NoLogin
    @PostMapping("/importFromJsonFile")
    public ResultObject importFromJsonFile(@RequestParam MultipartFile file) {
        botImportService.importFromJsonFile(file);
        return ResultObject.success("请求成功");
    }

    @NoLogin
    @PostMapping("/importFromJson")
    public ResultObject importFromJson(@RequestBody BotAutoImportVO botAutoImportVO) {
        botImportService.importFromJson(botAutoImportVO);
        return ResultObject.success("请求成功");
    }
}
