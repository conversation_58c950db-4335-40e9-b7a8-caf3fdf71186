package com.yiwise.dialogflow.controller.botgenerate;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.po.botgenerate.BotGenerateRecordPO;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotGenerateFailCallbackVO;
import com.yiwise.dialogflow.service.botgenerate.BotGenerateRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * bot生成记录
 */
@Validated
@RestController
@RequestMapping("apiBot/v3/botGenerate/record")
public class BotGenerateRecordController {

    @Resource
    private BotGenerateRecordService botGenerateRecordService;

    /**
     * 根据id获取生成记录(内部使用, 不会进行登录鉴权)
     * @param generateRecordId 生成记录id
     * @return 生成记录
     */
    @NoLogin
    @GetMapping("innerGetById")
    public ResultObject<BotGenerateRecordPO> innerGetById(@NotBlank(message = "generateRecordId不能为空") String generateRecordId) {
        return ResultObject.success(botGenerateRecordService.getById(generateRecordId).orElse(null));
    }

    /**
     * 根据id获取生成记录
     * @param generateRecordId 生成记录id
     * @return 生成记录
     */
    @GetMapping("getById")
    public ResultObject<BotGenerateRecordPO> getById(@NotBlank(message = "generateRecordId不能为空") String generateRecordId) {
        return ResultObject.success(botGenerateRecordService.getById(generateRecordId).orElse(null));
    }

    /**
     * 失败原因回调
     */
    @NoLogin
    @PostMapping("/failCallback")
    public ResultObject<Void> failCallback(@RequestBody BotGenerateFailCallbackVO callback) {
        botGenerateRecordService.failCallback(callback);
        return ResultObject.success(null);
    }
}
