package com.yiwise.dialogflow.controller.analyze;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.po.semantic.analysis.SemanticAnalysisConditionPO;
import com.yiwise.dialogflow.entity.vo.analyze.*;
import com.yiwise.dialogflow.service.analyze.AnalyzeTaskService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 会话分析任务
 *
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/apiBot/v3/analyzeTask")
public class AnalyzeTaskController {

    @Resource
    private AnalyzeTaskService analyzeTaskService;

    /**
     * 创建任务
     *
     * @param request form
     */
    @PostMapping("/create")
    public ResultObject<Void> create(@RequestBody @Valid AnalyzeTaskCreateVO request) {
        analyzeTaskService.create(request, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 修改任务
     *
     * @param request form
     */
    @PostMapping("/update")
    public ResultObject<Void> update(@RequestBody @Valid AnalyzeTaskUpdateVO request) {
        analyzeTaskService.update(request, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 删除任务
     *
     * @param request form
     */
    @PostMapping("/delete")
    public ResultObject<Void> delete(@RequestBody @Valid AnalyzeTaskDeleteVO request) {
        analyzeTaskService.delete(request);
        return ResultObject.success(null);
    }

    /**
     * 分页查询任务
     *
     * @param request form
     * @return 任务列表
     */
    @GetMapping("/list")
    public ResultObject<PageResultObject<AnalyzeTaskVO>> list(AnalyzeTaskQueryVO request) {
        return ResultObject.success(analyzeTaskService.list(request));
    }

    /**
     * 预览请求参数
     *
     * @param taskId 任务
     * @return body
     */
    @GetMapping(value = "/preview", produces = MediaType.APPLICATION_JSON_VALUE)
    public String preview(@RequestParam Long taskId) {
        return analyzeTaskService.preview(taskId);
    }

    /**
     * 任务调度
     */
    @NoLogin
    @PostMapping("/dispatch")
    public ResultObject<Void> dispatch() {
        analyzeTaskService.asyncDispatch();
        return ResultObject.success(null);
    }

    /**
     * 接收算法回调消息
     *
     * @param request form
     */
    @NoLogin
    @PostMapping("/callback")
    public ResultObject<Void> callback(@RequestBody String request) {
        analyzeTaskService.callback(request);
        return ResultObject.success(null);
    }

    /**
     * 查询语料数量
     *
     * @param condition form
     * @return 语料数量
     */
    @PostMapping("/corpusSize")
    public ResultObject<Long> corpusSize(@RequestBody SemanticAnalysisConditionPO condition) {
        return ResultObject.success(analyzeTaskService.corpusSize(condition));
    }
}
