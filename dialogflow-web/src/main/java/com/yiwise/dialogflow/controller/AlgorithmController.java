package com.yiwise.dialogflow.controller;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.aop.TenantIsolation;
import com.yiwise.dialogflow.entity.bo.algorithm.ModelTrainKey;
import com.yiwise.dialogflow.entity.enums.AlgorithmTrainTypeEnum;
import com.yiwise.dialogflow.entity.po.TrainResultPO;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.service.llm.EmbeddingService;
import com.yiwise.dialogflow.service.train.TrainService;
import com.yiwise.dialogflow.service.train.TrainSyncService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/31
 */
@Slf4j
@RestController
@RequestMapping("/apiBot/v3/algorithm")
public class AlgorithmController {


    @Resource
    private TrainSyncService trainSyncService;

    @Resource
    private TrainService trainService;

    @Resource
    private EmbeddingService ragTextService;

    // @formatter:off
    /**
     * @api {get} /apiDialogFlow/algorithm/domainList              获取领域模型列表
     * @apiName  domainList
     * @apiGroup algorithm
     *
     * @apiSuccessExample Response 200 Example
     * {
     *      "code": 200,
     *      "data": [{
     *          "name": "xxx",
     *          "desc": "xxx"
     *      }],
     *      "requestId": "BHLNIQPG",
     *      "resultMsg": "执行成功",
     *      "errorStackTrace": null
     * }
     *
     */
    // @formatter:on
    @NoLogin
    @GetMapping("domainList")
    public ResultObject<List<NameDescVO>> domainList() {
        return ResultObject.success(trainSyncService.domainList());
    }

    // @formatter:off
    /**
     * @api {get} /apiDialogFlow/algorithm/redisKeyList              获取RedisKey
     * @apiName  redisKeyList
     * @apiGroup algorithm
     *
     * @apiSuccessExample Response 200 Example
     * {
     *      "code": 200,
     *      "data": [{
     *          "environment": "AICC-DAILY",
     *          "tenantId": 0,
     *          "robotId": 8888,
     *          "trainType": "INTENT",
     *          "snapshotType": "PUBLISHED",
     *          "patch": "patch1"
     *      }],
     *      "requestId": "BHLNIQPG",
     *      "resultMsg": "执行成功",
     *      "errorStackTrace": null
     * }
     *
     */
    // @formatter:on
    @NoLogin
    @GetMapping("redisKeyList")
    public ResultObject<List<TrainDataVO>> redisKeyList() {
        return ResultObject.success(trainSyncService.redisKeyList());
    }

    // @formatter:off
    /**
     * @api {get} /apiDialogFlow/algorithm/list              获取训练语料
     * @apiName  list
     * @apiGroup algorithm
     * @apiParamExample {json}
     * {
     *      # Redis中训练信号格式：
     *      key：TrainDataSignal:TID_%s:RID_%s:%s:%s     # 从左到右 tenantID robotId snapshotType trainType 示例：TrainDataSignal:TID_1:RID_200:PUBLISHED:KNOWLEDGE
     *      value： AlgorithmTypeEnum                    # 算法模型种类 BERT DCNN 示例：BERT
     * }
     *
     * @apiParam {Long} tenantId                        # 公司ID
     * @apiParam {Long} robotId                         # 机器人ID
     * @apiParam {String} trainType                     # 训练类型 KNOWLEDGE-知识 INTENT-意图 ENTITY-实体 CHAT_SUBJECT-闲聊 KNOWLEDGE_GRAPH_ATTR-知识图谱属性
     * @apiParam {String} snapshotType                  # 发布版本（KNOWLEDGE仅有PUBLISHED和CALLABLE） TEST-对话流测试 PUBLISHED-对话流发布 CALLABLE-机器人发布（语音客服）
     *
     * @apiSuccessExample Response 200 Example
     * {
     *      "code": 200,
     *      "data": {
     *          "env": "TANYI_PROD",                                # 环境 LOCAL-本地 DAILY-日常 TEST-测试 PRE-预发 PROD-生产 TANYI_DAILY-打通版日常 TANYI_PROD-打通版生产
     *          "tenantId": 1,                                      # 公司ID
     *          "robotId": 1,                                       # 机器人ID
     *          "algorithmType": "BERT",                            # 算法模型种类 BERT DCNN
     *          "trainType": "KNOWLEDGE",                           # 训练类型 KNOWLEDGE-知识 INTENT-意图 ENTITY-实体 CHAT_SUBJECT-闲聊 KNOWLEDGE_GRAPH_ATTR-知识图谱属性
     *          "snapshotType": "PUBLISHED",                        # 发布版本（KNOWLEDGE仅有PUBLISHED和CALLABLE） TEST-对话流测试 PUBLISHED-对话流发布 CALLABLE-机器人发布（语音客服）
     *          "content": [{
     *              "type": "IN_LIB",                               # 语料类型 IN_LIB-库内问法 USER_SAY-用户问法
     *              "relationId": "5d09b054a70ab82b065f23cb",       # 关联ID，意图ID/知识ID/实体ID/词槽ID others-废弃语料库 unknown-未知意图
     *              "relationName": "意图名称",                       # 关联名称，意图名称/知识名称/实体名称/词槽名称 others-废弃语料库 unknown-未知意图
     *              "text": "水果"                                   # 语料
     *          }],
     *          "synonymList": [{                                   # 同义词库
     *              "name": "上海",                                  # 词库名称
     *              "synonymList": [                                # 同义词列表
     *                  "魔都"
     *              ]
     *          }],
     *          "modelId": "5d09b054a70ab82b065f23cb"               # 模型ID
     *      },
     *      "requestId": "BHLNIQPG",
     *      "resultMsg": "执行成功",
     *      "errorStackTrace": null
     * }
     *
     */
    // @formatter:on
    @NoLogin
    @GetMapping("list")
    public ResultObject list(@NotNull Long tenantId,
                            @NotNull String robotId,
                            @NotNull AlgorithmTrainTypeEnum trainType) {
        return ResultObject.success(trainSyncService.queryTrainData(tenantId, robotId, trainType));
    }

    // @formatter:off
    /**
     * @api {get} /apiDialogFlow/algorithm/history      训练历史
     * @apiName  history
     * @apiGroup algorithm
     *
     * @apiParam {String} trainType                     # 训练类型 QC_TAG_STAFF-质检客服标签 QC_TAG_CUSTOMER-质检客户标签
     *
     * @apiSuccessExample Response 200 Example
     * {
     *      "code": 200,
     *      "data": [{
     *          "tenantId": 1,
     *          "trainType": "QC_TAG_STAFF",            # 训练类型 QC_TAG_STAFF-质检客服标签 QC_TAG_CUSTOMER-质检客户标签
     *          "success": true,                        # 上一次是否成功
     *          "finishTime": "2020-12-02 00:00:00",    # 上次成功时间
     *          "createUserName": "xxx"                 # 操作人
     *      }],
     *      "requestId": "BHLNIQPG",
     *      "resultMsg": "执行成功",
     *      "errorStackTrace": null
     * }
     *
     */
    // @formatter:on
    @GetMapping("/history")
    public ResultObject<List<TrainStatusVO>> history(AlgorithmTrainTypeEnum trainType) {
        return ResultObject.success(trainService.history(SecurityUtils.getTenantId(), trainType));
    }

    @PostMapping("/train")
    @TenantIsolation("#botId")
    public ResultObject<String> train(@NotNull(message = "botId不能为空") Long botId, @RequestParam(required = false) String desc) {
        trainService.train(botId, SecurityUtils.getUserId(), desc, SystemEnum.CRM);
        return ResultObject.success("请求成功");
    }

    /**
     * 批量训练
     * @param request 请求参数
     * @return 批量训练结果
     */
    @PostMapping("batchTrain")
    public ResultObject<BatchTrainResultVO> batchTrain(@RequestBody BatchTrainRequestVO request) {
        request.setUserId(SecurityUtils.getUserId());
        return ResultObject.success(trainService.batchTrain(request, SecurityUtils.getTenantId()));
    }

    /**
     * 预批量训练, 查询哪些可以训练, 哪些被过滤掉了
     * @param request 请求参数
     * @return 预批量训练结果
     */
    @PostMapping("preBatchTrain")
    public ResultObject<BatchTrainResultVO> preBatchTrain(@RequestBody BatchTrainRequestVO request) {
        request.setUserId(SecurityUtils.getUserId());
        return ResultObject.success(trainService.preBatchTrain(request, SecurityUtils.getTenantId()));
    }

    // @formatter:off
    /**
     * @api {get} /apiDialogFlow/algorithm/enableTrain  获取是否可以进行训练
     * @apiName  enableTrain
     * @apiGroup algorithm
     *
     * @apiParam {String} trainType                     # 训练类型 QC_TAG_STAFF-质检客服标签 QC_TAG_CUSTOMER-质检客户标签
     *
     * @apiSuccessExample Response 200 Example
     * {
     *      "code": 200,
     *      "data": {
     *          "tenantId": 1,
     *          "trainType": "QC_TAG",                  # 训练类型 QC_TAG_STAFF-质检客服标签 QC_TAG_CUSTOMER-质检客户标签
     *          "enableTrainButton": false,             # 按钮是否可以点击
     *          "isTraining": true,                     # 是否正在训练中
     *          "success": true,                        # 上一次是否成功
     *          "finishTime": "2020-12-02 00:00:00"     # 上次成功时间
     *      },
     *      "requestId": "BHLNIQPG",
     *      "resultMsg": "执行成功",
     *      "errorStackTrace": null
     * }
     *
     */
    // @formatter:on
    @GetMapping("/enableTrain")
    public ResultObject<TrainStatusVO> enableTrain(@RequestParam(required = false, defaultValue = "0") Long botId, AlgorithmTrainTypeEnum trainType) {
        return ResultObject.success(trainService.enableTrain(ModelTrainKey.of(0L, botId, trainType)));
    }

    @GetMapping("/lastSuccess")
    public ResultObject<TrainResultPO> lastSuccess(@RequestParam(required = false, defaultValue = "0") Long botId, AlgorithmTrainTypeEnum trainType) {
        return ResultObject.success(trainService.lastSuccess(ModelTrainKey.of(0L, botId, trainType)));
    }

    // @formatter:off
    /**
     * @api {get} /apiDialogFlow/algorithm/callback              算法回调训练结果
     * @apiName  callback
     * @apiGroup algorithm
     *
     * @apiParam {Long} tenantId                        # 公司ID
     * @apiParam {String} finishTime                    # 完成时间
     * @apiParam {Boolean} success                      # 是否成功
     * @apiParam {String} trainType                     # 训练类型 QC_TAG_STAFF-质检客服标签 QC_TAG_CUSTOMER-质检客户标签
     *
     * @apiSuccessExample Response 200 Example
     * {
     *      "code": 200,
     *      "data": "成功获取训练结果",
     *      "requestId": "BHLNIQPG",
     *      "resultMsg": "执行成功",
     *      "errorStackTrace": null
     * }
     *
     */
    // @formatter:on
    @NoLogin
    @GetMapping("/callback")
    public ResultObject<String> callback(TrainStatusVO trainResultPO) {
        log.info("[LogHub_Warn]接收到模型训练结果回调, botId={}, trainType={}, modelId={}, success={}, finishTime={}, operate={}",
                trainResultPO.getRobotId(), trainResultPO.getTrainType(), trainResultPO.getModelId(),
                trainResultPO.getSuccess(), trainResultPO.getFinishTime(), trainResultPO.getOperate());
        trainService.callback(trainResultPO);
        return ResultObject.success("成功获取训练结果");
    }

    @NoLogin
    @PostMapping("/ragUploadCallback/{botId}/{ragTextUploadRecordId}")
    public ResultObject<String> ragUploadCallback(@PathVariable Long botId,
                                                 @PathVariable String ragTextUploadRecordId,
                                                 @RequestBody BaseCallbackResultVO result) {
        log.info("接收到rag上传结果回调, botId:{}, ragTextUploadRecordId:{}, result:{}",botId, ragTextUploadRecordId, JsonUtils.object2String(result));

        ragTextService.updateResult(ragTextUploadRecordId, result);
        return ResultObject.success("成功获取训练结果");
    }
}
