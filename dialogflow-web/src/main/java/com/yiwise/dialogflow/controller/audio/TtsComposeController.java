package com.yiwise.dialogflow.controller.audio;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.OssKeyCenter;
import com.yiwise.dialogflow.entity.vo.OneTimeTtsRequestVO;
import com.yiwise.dialogflow.entity.vo.audio.request.TtsComposeRequestVO;
import com.yiwise.dialogflow.entity.vo.audio.TtsComposeResultVO;
import com.yiwise.dialogflow.service.TtsJobService;
import com.yiwise.dialogflow.service.remote.TtsComposeService;
import com.yiwise.dialogflow.thread.DynamicDataSourceApplicationExecutorHolder;
import org.bson.types.ObjectId;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("apiBot/v3/tts")
public class TtsComposeController {

    @Resource
    private TtsComposeService ttsComposeService;

    @Resource
    private TtsJobService ttsJobService;


    @GetMapping("test")
    public ResultObject<TtsComposeResultVO> test(String answerText, String voice) {

        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();

        CompletableFuture<TtsComposeResultVO> result = new CompletableFuture<>();
        DynamicDataSourceApplicationExecutorHolder.execute("tts", () -> {
            RequestContextHolder.setRequestAttributes(attributes);
            TtsComposeRequestVO requestVO = new TtsComposeRequestVO();
            requestVO.setSpeed(5.0f);
            requestVO.setVolume(5.0f);
            requestVO.setAnswerText(answerText);
            requestVO.setVoice(voice);
            requestVO.setFilePath(OssKeyCenter.getBotTtsAudioPath(0L, new ObjectId().toString()));
            result.complete(ttsComposeService.composeTextByConfig(requestVO));
        });
        try {
            return ResultObject.success(result.get());
        } catch (Exception e) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, e);
        }
    }

    @PostMapping("demo")
    public ResultObject<TtsComposeResultVO> demo(@RequestBody OneTimeTtsRequestVO request) {
        TtsComposeRequestVO composeRequest = new TtsComposeRequestVO();
        composeRequest.setSpeed(request.getTtsSpeed());
        composeRequest.setVolume(request.getTtsVolume());
        composeRequest.setAnswerText(request.getText());
        composeRequest.setVoice(request.getTtsVoice());
        composeRequest.setFilePath(OssKeyCenter.getBotTtsAudioPath(0L, new ObjectId().toString()));
        // todo 加缓存逻辑
        return ResultObject.success(ttsComposeService.composeTextByConfig(composeRequest));
    }

}
