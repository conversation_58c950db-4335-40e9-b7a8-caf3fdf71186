package com.yiwise.dialogflow.controller.stats;


import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.api.dto.request.StepAggregationStatsRequest;
import com.yiwise.dialogflow.api.dto.response.stats.DataReportStepDataPO;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.entity.query.*;
import com.yiwise.dialogflow.entity.vo.IntentRuleActionVO;
import com.yiwise.dialogflow.entity.vo.IntentRuleVO;
import com.yiwise.dialogflow.entity.vo.KnowledgeQueryVO;
import com.yiwise.dialogflow.entity.vo.stats.*;
import com.yiwise.dialogflow.service.KnowledgeService;
import com.yiwise.dialogflow.service.StepService;
import com.yiwise.dialogflow.service.intent.IntentRuleActionService;
import com.yiwise.dialogflow.service.intent.IntentRuleService;
import com.yiwise.dialogflow.service.stats.BotReceptionStatsService;
import com.yiwise.dialogflow.service.stats.BotStatsExportService;
import com.yiwise.dialogflow.service.stats.BotStatsFacadeService;
import com.yiwise.dialogflow.service.stats.StepAggregationStatsService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * bot数据统计
 */
@Validated
@RestController
@RequestMapping("apiBot/v3/stats")
public class BotStatsController {
    @Resource
    private BotStatsFacadeService botStatsFacadeService;

    @Resource
    private IntentRuleActionService intentRuleActionService;

    @Resource
    private IntentRuleService intentRuleService;

    @Resource
    private StepService stepService;

    @Resource
    private KnowledgeService knowledgeService;

    @Resource
    private BotStatsExportService botStatsExportService;

    @Resource
    private BotReceptionStatsService botReceptionStatsService;

    @Resource
    private StepAggregationStatsService stepAggregationStatsService;

    /**
     * 查询bot关联的所有任务列表
     * @param botId 机器人id
     */
    @GetMapping("getCallJobList")
    public ResultObject<List<SimpleCallJobInfoVO>> getCallJobList(@NotNull(message = "botId不能为空") Long botId) {
        BaseStatsQuery query = new BaseStatsQuery();
        query.setBotId(botId);
        query.setEnableStatsInfo(true);
        setTenantIfFromAICC(query);
        return ResultObject.success(botStatsFacadeService.queryCallJobList(query));
    }

    /**
     * 查询bot关联的所有ma流程列表
     * @param botId 机器人id
     */
    @GetMapping("getMaFlowList")
    public ResultObject<List<SimpleMaFlowInfoVO>> getMaFlowList(@NotNull(message = "botId不能为空") Long botId) {
        BaseStatsQuery query = new BaseStatsQuery();
        query.setBotId(botId);
        query.setEnableStatsInfo(true);
        setTenantIfFromAICC(query);
        return ResultObject.success(botStatsFacadeService.queryMaFlowList(query));
    }

    /**
     * 查询bot关联的所有新版外呼任务列表
     * @param botId 机器人id
     */
    @GetMapping("getNewCallJobList")
    public ResultObject<List<SimpleNewCallJobInfoVO>> getNewCallJobList(@NotNull(message = "botId不能为空") Long botId) {
        BaseStatsQuery query = new BaseStatsQuery();
        query.setBotId(botId);
        query.setEnableStatsInfo(true);
        setTenantIfFromAICC(query);
        return ResultObject.success(botStatsFacadeService.queryNewCallJobList(query));
    }

    /**
     * 导出所有统计数据
     */
    @PostMapping("export")
    public ResultObject<String> export(@RequestBody BaseStatsQuery condition) {
        setTenantIfFromAICC(condition);
        condition.setEnableStatsInfo(true);
        return ResultObject.success(botStatsExportService.export(condition));
    }

    /**
     * 查询所有流程节点的统计信息
     */
    @PostMapping("getStepNodeStats")
    public ResultObject<List<SimpleNodeStatsInfoVO>> getStepNodeStats(@RequestBody BaseStatsQuery condition) {
        setTenantIfFromAICC(condition);
        condition.setEnableStatsInfo(true);
        return ResultObject.success(botStatsFacadeService.queryAllNodeStats(condition));
    }

    /**
     * 意向等级统计
     */
    @PostMapping("getIntentLevelStats")
    public ResultObject<List<SimpleIntentLevelStatsVO>> getIntentLevelStats(@RequestBody BaseStatsQuery condition) {
        setTenantIfFromAICC(condition);
        condition.setEnableStatsInfo(true);
        return ResultObject.success(botStatsFacadeService.queryIntentLevelStatsList(condition));
    }

    /**
     * 意向动作统计
     */
    @PostMapping("getIntentActionStats")
    public ResultObject<List<SimpleIntentActionStatsVO>> getIntentActionStats(@RequestBody BaseStatsQuery condition) {
        setTenantIfFromAICC(condition);
        condition.setEnableStatsInfo(true);
        return ResultObject.success(botStatsFacadeService.queryIntentActionStatsList(condition));
    }

    /**
     * 查询所有流程统计信息
     */
    @PostMapping("getStepStats")
    public ResultObject<List<SimpleStepStatsInfoVO>> getStepStats(@RequestBody StepQueryVO condition) {
        setTenantIfFromAICC(condition);
        condition.setEnableStatsInfo(true);
        return ResultObject.success(stepService.getAllStepStatsInfo(condition));
    }


    /**
     * 查询节点跳出所有的类型
     * @return
     */
    @GetMapping("getNodeJumpOutTypeList")
    public ResultObject<List<String>> getNodeJumpOutTypeList() {
        List<String> list = Arrays.asList("一般知识", "业务知识", "主流程", "独立对话流程", "特殊语境");
        return ResultObject.success(list);
    }

    /**
     * 查询所有问答知识统计数据
     */
    @PostMapping("getKnowledgeStats")
    public ResultObject<List<SimpleKnowledgeStatsInfoVO>> getKnowledgeStats(@RequestBody KnowledgeQueryVO condition) {
        setTenantIfFromAICC(condition);
        return ResultObject.success(knowledgeService.queryAllKnowledgeStatsInfo(condition));
    }

    /**
     * 查询意图触发统计
     */
    @PostMapping("getIntentTriggerStats")
    public ResultObject<List<IntentTriggerStatsVO>> getIntentTriggerStats(@RequestBody BaseStatsQuery condition) {
        setTenantIfFromAICC(condition);
        condition.setEnableStatsInfo(true);
        return ResultObject.success(botStatsFacadeService.queryIntentTriggerStats(condition.getBotId(), condition));
    }

    /**
     * 查询任务有外呼的日期信息
     */
    @PostMapping("getJobCallDateList")
    public ResultObject<JobCallDateVO> getJobCallDateList(@RequestBody BaseStatsQuery condition) {
        setTenantIfFromAICC(condition);
        return ResultObject.success(botStatsFacadeService.queryCallDateInfo(condition));
    }

    /**
     * 查询所有意向等级规则统计信息
     */
    @PostMapping("getIntentLevelRuleStats")
    public ResultObject<List<IntentRuleVO>> getIntentLevelRuleStats(@RequestBody IntentRuleQueryVO condition) {
        setTenantIfFromAICC(condition);
        return ResultObject.success(intentRuleService.getIntentRuleListDetail(condition));
    }

    /**
     * 查询所有意向动作规则统计信息
     */
    @PostMapping("getIntentActionRuleStats")
    public ResultObject<List<IntentRuleActionVO>> getIntentActionRuleStats(@RequestBody IntentActionQuery condition) {
        setTenantIfFromAICC(condition);
        return ResultObject.success(intentRuleActionService.getIntentRuleActionDetailList(condition));
    }

    /**
     * 查询节点下各答案按进度统计挂机详情
     */
    @PostMapping("getNodeAnswerHangupDetail")
    public ResultObject<List<AnswerPlayProgressHangupStatsVO>> getNodeAnswerHangupDetail(@RequestBody NodeAnswerHangupDetailQuery condition) {
        setTenantIfFromAICC(condition);
        return ResultObject.success(botStatsFacadeService.queryAnswerPlayProgressHangupStats(condition.getBotId(), AnswerSourceEnum.STEP, condition.getStepId(), condition.getNodeId(), null, null, condition));
    }

    /**
     * 查询知识下各答案按进度统计挂机详情
     */
    @PostMapping("getKnowledgeAnswerHangupDetail")
    public ResultObject<List<AnswerPlayProgressHangupStatsVO>> getKnowledgeAnswerHangupDetail(@RequestBody KnowledgeAnswerHangupDetailQuery condition) {
        setTenantIfFromAICC(condition);
        return ResultObject.success(botStatsFacadeService.queryAnswerPlayProgressHangupStats(condition.getBotId(), AnswerSourceEnum.KNOWLEDGE, null, null, condition.getKnowledgeId(), null, condition));
    }

    /**
     * 查询特殊语境下各答案按进度统计挂机详情
     */
    @PostMapping("getSpecialAnswerHangupDetail")
    public ResultObject<List<AnswerPlayProgressHangupStatsVO>> getSpecialAnswerHangupDetail(@RequestBody SpecialAnswerHangupDetailQuery condition) {
        setTenantIfFromAICC(condition);
        return ResultObject.success(botStatsFacadeService.queryAnswerPlayProgressHangupStats(condition.getBotId(), AnswerSourceEnum.SPECIAL_ANSWER, null, null, null, condition.getSpecialAnswerConfigId(), condition));
    }

    /**
     * 查询所有bot每小时外呼量和接通量统计
     * @param startTime 开始时间, 格式: yyyy-MM-dd HH:mm:ss
     * @param endTime 结束时间, 格式: yyyy-MM-dd HH:mm:ss
     * @return
     */
    @GetMapping("getAllBotHourlyStats")
    public ResultObject<List<AllBotHourlyReceptionStatsVO>> getAllBotHourlyStats(@RequestParam @NotNull(message = "开始时间不能为空") LocalDateTime startTime,
                                                                                @RequestParam @NotNull(message = "结束时间不能为空") LocalDateTime endTime) {
        return ResultObject.success(botReceptionStatsService.queryAllBotHourlyReceptionInfo(startTime, endTime));
    }

    /**
     * 查询所有bot每日外呼量和接通量统计
     * @param startDate
     * @param endDate
     * @return
     */
    @GetMapping("getAllBotDailyStats")
    public ResultObject<List<AllBotDailyReceptionStatsVO>> getAllBotDailyStats(@RequestParam @NotNull(message = "开始时间不能为空") LocalDate startDate,
                                                                              @RequestParam @NotNull(message = "结束时间不能为空") LocalDate endDate) {
        return ResultObject.success(botReceptionStatsService.queryAllBotDailyReceptionInfo(startDate, endDate));
    }

    @NoLogin
    @InnerOnly
    @PostMapping("getStepAggregationStats")
    public ResultObject<List<DataReportStepDataPO>> getStepAggregationStats(@RequestBody StepAggregationStatsRequest condition) {
        return ResultObject.success(stepAggregationStatsService.generateDialogFlowDataReportData(condition));
    }

    private void setTenantIfFromAICC(BaseStatsQuery condition) {
        if (SystemEnum.isAICC(condition.getSystemType())) {
            condition.setTenantId(getTenantId());
        }
    }

    private Long getTenantId() {
        return SecurityUtils.getTenantId();
    }
}
