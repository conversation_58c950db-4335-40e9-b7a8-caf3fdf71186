package com.yiwise.dialogflow.chatcontroller;

import com.yiwise.base.common.utils.MyThreadUtils;
import com.yiwise.base.common.utils.PropertyLoaderUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.dto.llmchat.LLMChatResponse;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;

@RestController
@RequestMapping("apiBot/v3/test")
public class TestController {
    private static final String defaultText = "抱歉打扰您，来电邀请您参与毛戈平成都王府井百货专柜活动，会有专业彩妆师现场进行妆容讲解，您作为尊享用户参与互动就可以直接获得星品体验礼包，诚邀您本周有空到柜逛一逛！毛戈平期待您的光临，再见哈！";

    static String chatResponseJson = "{}";
    static String analyzeResponseJson = "{}";

    static final LLMChatResponse defaultResponse = new LLMChatResponse();
    static {
        PropertyLoaderUtils.addPropertyChangeListener(TestController::reload);
        reload();
        defaultResponse.setResponse(defaultText);
        defaultResponse.setIsFinished(false);
        defaultResponse.setIsResponded(true);
    }

    public static void reload() {
        if (PropertyLoaderUtils.containsProperty("llmChat.mock.response")) {
            chatResponseJson = PropertyLoaderUtils.getProperty("llmChat.mock.response");
        } else {
            chatResponseJson = "{}";
        }

        if (PropertyLoaderUtils.containsProperty("llmAnalyze.mock.response")) {
            analyzeResponseJson = PropertyLoaderUtils.getProperty("llmAnalyze.mock.response");
        } else {
            analyzeResponseJson = "{}";
        }
    }

    @NoLogin
    @RequestMapping(value = "llmChat",
            method = {RequestMethod.POST},
            produces = {MediaType.TEXT_EVENT_STREAM_VALUE})
    public Flux<LLMChatResponse> llmChat(@RequestBody Map<String, Object> param) {

        String query = param.getOrDefault("query", "default").toString();

        if ("生成失败".equals(query)) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "失败了");
        }

        Map<String, LLMChatResponse> responseMap = JsonUtils.string2MapObject(chatResponseJson, String.class, LLMChatResponse.class);
        if (!responseMap.containsKey(query)) {
            query = "default";
        }
        String finalQuery = query;

        final LLMChatResponse response = responseMap.getOrDefault(query, defaultResponse);
        String text = response.getResponse();
        AtomicInteger index = new AtomicInteger(0);
        Random random = new Random();
        return Flux.generate(
                        sink -> {
                            if (index.get() >= text.length()) {
                                sink.complete();
                            } else {
                                int length = random.nextInt(5) + 1;
                                String subText = text.substring(index.get(), Math.min(index.get() + length, text.length()));
                                index.addAndGet(length);
                                if ("生成超时".equals(finalQuery)) {
                                    MyThreadUtils.sleepMilliseconds(1000 * 2);
                                }
                                sink.next(subText);
                            }
                        })
                .delayElements(Duration.ofMillis(100))
                .map(str -> {
                    LLMChatResponse copy = MyBeanUtils.copy(response, LLMChatResponse.class);
                    copy.setResponse(String.valueOf(str));
                    return copy;
                });
    }

    @NoLogin
    @PostMapping("llmAnalyze")
    public Map<String, Object> llmAnalyze(@RequestBody Map<String, Object> param) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 0);
        result.put("info", "success");
        Map<String, String> responseMap = JsonUtils.string2MapObject(analyzeResponseJson, String.class, String.class);
        result.put("result", responseMap);
        return result;
    }

}
