package com.yiwise.dialogflow.chatcontroller;

import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.engine.service.BotChatService;
import com.yiwise.dialogflow.engine.share.CallDataInfo;
import com.yiwise.dialogflow.engine.share.IntentLevelAnalysisResult;
import com.yiwise.dialogflow.engine.share.request.ChatRequest;
import com.yiwise.dialogflow.engine.share.request.CreateSessionRequest;
import com.yiwise.dialogflow.engine.share.request.HttpRequest;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.Map;

@RestController
@RequestMapping("/apiBot/v3/chatService/")
public class AsyncChatController {

    @Resource
    private BotChatService botChatService;

    @InnerOnly
    @NoLogin
    @PostMapping("createSession")
    public Mono<ResultObject<SessionInfo>> createSession(@RequestBody CreateSessionRequest request) {
        return botChatService.createSessionAsync(request.getBotId(), request.getVersion(), request.getUsageTarget(), request.getChatMetaData())
                .map(ResultObject::success);
    }

    @InnerOnly
    @NoLogin
    @PostMapping(value = "processRequest", produces = {MediaType.TEXT_EVENT_STREAM_VALUE})
    public Flux<ResultObject<ChatResponse>> processRequest(@RequestBody ChatRequest request) {
        return botChatService.processRequestAsync(request)
                .map(ResultObject::success);
    }

    @InnerOnly
    @NoLogin
    @PostMapping(value = "llmProcessRequest", produces = {MediaType.TEXT_EVENT_STREAM_VALUE})
    public Flux<ResultObject<ChatResponse>> processRequestWithLLMAsync(@RequestBody ChatRequest request) {
        return botChatService.processRequestWithLLMAsync(request)
                .map(ResultObject::success);
    }

    @InnerOnly
    @NoLogin
    @PostMapping("analysis")
    public Mono<ResultObject<IntentLevelAnalysisResult>> analysis(@RequestBody CallDataInfo callDataInfo) {
        return botChatService.analysisAsync(callDataInfo)
                .map(ResultObject::success);
    }

    @InnerOnly
    @NoLogin
    @PostMapping("analysisInCall")
    public Mono<ResultObject<IntentLevelAnalysisResult>> analysisInCall(@RequestBody CallDataInfo callDataInfo) {
        return botChatService.analysisInCallAsync(callDataInfo)
                .map(ResultObject::success);
    }

    @InnerOnly
    @NoLogin
    @PostMapping("/httpRequest")
    public Mono<ResultObject<Map<String, String>>> httpRequest(@RequestBody HttpRequest req) {
        return botChatService.httpRequestAsync(req).map(ResultObject::success);
    }
}