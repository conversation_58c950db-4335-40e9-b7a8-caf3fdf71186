#!/bin/bash

# the service name
SERVICE_NAME="${MODULE_ARTIFACT_ID}"
JAR_NAME="${MODULE_FINAL_NAME}.jar"
PID_FILE=$SERVICE_NAME\.pid
GC_LOG="${gcLog}"
ARMS_JAR="${armsJar}"
ARMS_LICENCE_KEY="${armsLicenceKey}"
ARMS_APP_NAME="${MODULE_ARTIFACT_ID}-${armsAppSuffix}"
ARMS_OPTIONS="${armsOptions}"
JAVA_MEM_OPTS="-server -Xmx12g -Xms12g -Xmn8g -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -XX:PermSize=256m -XX:+UseConcMarkSweepGC -XX:+CMSParallelRemarkEnabled -XX:+UseCMSCompactAtFullCollection -XX:LargePageSizeInBytes=128m -XX:+UseFastAccessorMethods -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=70 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/${MODULE_FINAL_NAME}.dump "
DNS_OPTS="-Djava.net.preferIPv4Stack=true -Ddruid.mysql.usePingMethod=false"
DEBUG="${remoteDebugAddress}${jmxremotePort}"
SPRING_OPTS="-Dspring.profiles.active=${profile}"
JAVA_OPTS="$JAVA_MEM_OPTS $ARMS_OPTIONS $GC_LOG $DEBUG $DNS_OPTS $SPRING_OPTS"

cd /home/<USER>/aicc-platform-dialogflow-web/${MODULE_ARTIFACT_ID}-1.0.0-SNAPSHOT
JAVA_TOOL_OPTIONS=""
sed -i -e '/pam_loginuid.so/s/^/#/' /etc/pam.d/crond
echo "* * * * * flock -xn /tmp/mv_log_to_history_2.lock -c '/home/<USER>/aicc-platform-dialogflow-web/${MODULE_ARTIFACT_ID}-1.0.0-SNAPSHOT/bin/mv_dialogflow_log.sh ${MODULE_ARTIFACT_ID} >> /home/<USER>/aicc-platform-dialogflow-web/mv_log_to_history.log 2>&1'"  > /etc/cron.d/cron_file
echo >> /etc/cron.d/cron_file
crontab /etc/cron.d/cron_file
chmod a+x /home/<USER>/aicc-platform-dialogflow-web/${MODULE_ARTIFACT_ID}-1.0.0-SNAPSHOT/bin/mv_dialogflow_log.sh
chmod a+x /home/<USER>/aicc-platform-dialogflow-web/${MODULE_ARTIFACT_ID}-1.0.0-SNAPSHOT/bin/mv_log_to_history.sh
chmod a+x /home/<USER>/aicc-platform-dialogflow-web/${MODULE_ARTIFACT_ID}-1.0.0-SNAPSHOT/bin/backup.sh
crond &
exec java $JAVA_OPTS -jar $JAR_NAME