<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiwise.dialogflow.mapper.CallDetailSessionInfoPOMapper">

    <resultMap id="BaseResultMap" type="com.yiwise.dialogflow.entity.po.remote.CallDetailSessionInfoPO">
        <id column="call_detail_id" jdbcType="BIGINT" property="callDetailId"/>
        <result column="session_id" jdbcType="VARCHAR" property="sessionId"/>
        <result column="seq" jdbcType="INTEGER" property="seq"/>
        <result column="user_input" jdbcType="VARCHAR" property="userInput"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        call_detail_id,session_id,seq,user_input,create_time,update_time
    </sql>

    <select id="listByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM call_detail_session_info
        WHERE call_detail_id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>


</mapper>