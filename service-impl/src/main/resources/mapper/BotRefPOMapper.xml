<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 4.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiwise.dialogflow.mapper.BotRefPOMapper">
    <resultMap id="BaseResultMap" type="com.yiwise.dialogflow.entity.po.BotRefPO">
        <result column="bot_id" property="botId" jdbcType="BIGINT"/>
        <result column="dialog_flow_id" property="dialogFlowId" jdbcType="BIGINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="enable_status" property="enabledStatus" jdbcType="TINYINT"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Column_List">
        bot_id
        , dialog_flow_id, tenant_id, create_user_id, update_user_id, create_time, update_time
    </sql>

    <select id="getBotId" resultType="java.lang.Long">
        select bot_id
        from bot_ref
        where dialog_flow_id = #{dialogFlowId}
    </select>

    <select id="getDialogFlowId" resultType="java.lang.Long">
        select dialog_flow_id
        from bot_ref
        where bot_id = #{botId}
    </select>
    <select id="getTenantIdByBotId" resultType="java.lang.Long">
        select tenant_id
        from bot_ref
        where bot_id = #{botId}
    </select>

    <update id="deleteByDialogFlowId">
        update bot_ref
        set enable_status = 2
        where dialog_flow_id = #{dialogFlowId}
    </update>

    <update id="deleteByBotId">
        update bot_ref
        set enable_status = 2
        where bot_id = #{botId}
    </update>
    <update id="updateByDialogFlowIds">
        update bot_ref
        set tenant_id = #{tenantId}
        <where>
            dialog_flow_id in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </update>

    <select id="getByBotIdList" resultMap="BaseResultMap">
        select
        <include refid="Column_List"/>
        from bot_ref
        where bot_id in
        <foreach collection="botIdList" open="(" close=")" item="botId" separator=",">
            #{botId}
        </foreach>
    </select>
    <select id="getBotIdListByDialogFlowIds" resultType="java.lang.Long">
        select
        bot_id
        from bot_ref
        <where>
            and dialog_flow_id in
            <foreach collection="list" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="getByDialogFlowIdList" resultMap="BaseResultMap">
        select
        <include refid="Column_List"/>
        from bot_ref
        <where>
            and dialog_flow_id in
            <foreach collection="dialogFlowIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="getAllBindList" resultMap="BaseResultMap">
        select
        <include refid="Column_List"/>
        from bot_ref
        where tenant_id > 0
    </select>

</mapper>