<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 4.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiwise.dialogflow.mapper.VarValueTtsReplacePOMapper">
    <resultMap id="BaseResultMap" type="com.yiwise.dialogflow.entity.po.VarValueTtsReplacePO">
        <id column="var_value_tts_replace_id" property="varValueTtsReplaceId" jdbcType="BIGINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="var_name" property="varName" jdbcType="VARCHAR"/>
        <result column="var_value" property="varValue" jdbcType="BIGINT"/>
        <result column="replacement" property="replacement" jdbcType="VARCHAR"/>
        <result column="tts_provider" property="ttsProvider" jdbcType="TINYINT"
                typeHandler="com.yiwise.dialogflow.entity.enums.handler.TtsProviderEnumHandler"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="VOResultMap" type="com.yiwise.dialogflow.entity.vo.VarValueTtsReplaceVO" extends="BaseResultMap">
    </resultMap>

    <sql id="Column_List">
        var_value_tts_replace_id, tenant_id, var_name, var_value, replacement, tts_provider, create_user_id, update_user_id, create_time, update_time
    </sql>

    <sql id="Table_Name">var_value_tts_replace</sql>

    <delete id="deleteByIdList">
        delete from <include refid="Table_Name"/>
        where
        tenant_id = #{tenantId}
        and var_value_tts_replace_id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByTenantId">
        delete from <include refid="Table_Name"/>
        where
        tenant_id = #{tenantId}
    </delete>

    <select id="queryByCondition" resultMap="VOResultMap">
            select <include refid="Column_List"/>
            from <include refid="Table_Name"/>
            where tenant_id = #{query.tenantId}
            <if test="query.search != null and query.search != ''">
                and (var_value like concat('%', #{query.search}, '%') or replacement like concat('%', #{query.search}, '%'))
            </if>
            order by update_time desc
    </select>
    <select id="queryByVariableNameAndValue" resultMap="BaseResultMap">
        select <include refid="Column_List"/>
        from <include refid="Table_Name"/>
        <where>
            var_name = #{varName}
            and var_value = #{varValue}
            and tenant_id = #{tenantId}
            and tts_provider = #{providerCode}
            <if test="excludeId != null">
                and var_value_tts_replace_id != #{excludeId}
            </if>
        </where>
    </select>
    <select id="queryByTenantId" resultMap="BaseResultMap">
        select <include refid="Column_List"/>
        from <include refid="Table_Name"/>
        where tenant_id = #{tenantId}
    </select>
    <select id="queryByTenantIdAndIdList" resultMap="BaseResultMap">
        select <include refid="Column_List"/>
        from <include refid="Table_Name"/>
        where tenant_id = #{tenantId}
        and var_value_tts_replace_id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="batchUpsert">
        insert into var_value_tts_replace (tenant_id, tts_provider, var_name, var_value, replacement, update_user_id)
        values
        <foreach collection="list" item="item" separator="," >
            (#{item.tenantId}, #{item.ttsProvider, typeHandler=com.yiwise.dialogflow.entity.enums.handler.TtsProviderEnumHandler}, #{item.varName}, #{item.varValue}, #{item.replacement},  #{item.updateUserId})
        </foreach>
        on duplicate key update
        replacement = values(replacement),
        update_user_id = values(update_user_id)
    </update>

</mapper>