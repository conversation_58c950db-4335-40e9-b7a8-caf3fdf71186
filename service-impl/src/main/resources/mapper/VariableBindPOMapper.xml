<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 4.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiwise.dialogflow.mapper.VariableBindPOMapper">
    <resultMap id="BaseResultMap" type="com.yiwise.dialogflow.entity.po.VariableBindPO">
        <id column="variable_bind_id" property="variableBindId" jdbcType="BIGINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="bot_id" property="botId" jdbcType="BIGINT"/>
        <result column="variable_id" property="variableId" jdbcType="VARCHAR"/>
        <result column="customer_attribute_id" property="customerAttributeId" jdbcType="BIGINT"/>
        <result column="bind_variable_name" property="bindVariableName" jdbcType="VARCHAR"/>
        <result column="bind_attribute_name" property="bindAttributeName" jdbcType="VARCHAR"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Column_List">
        variable_bind_id, tenant_id, bot_id, variable_id, customer_attribute_id, bind_variable_name, bind_attribute_name, create_user_id, update_user_id, create_time, update_time
    </sql>

    <sql id="Table_Name">variable_bind</sql>

    <insert id="batchCreate">
        replace into <include refid="Table_Name"/>
        (tenant_id, bot_id, variable_id, customer_attribute_id, bind_variable_name, bind_attribute_name, create_user_id, update_user_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.tenantId}, #{item.botId}, #{item.variableId}, #{item.customerAttributeId}, #{item.bindVariableName}, #{item.bindAttributeName}, #{item.createUserId}, #{item.updateUserId})
        </foreach>
    </insert>

    <delete id="deleteByBotIdAndVariableId">
        delete from <include refid="Table_Name"/>
        where tenant_id = #{tenantId} and bot_id = #{botId} and variable_id = #{variableId}
    </delete>

    <select id="queryByBotId" resultMap="BaseResultMap">
        select <include refid="Column_List"/>
        from <include refid="Table_Name"/>
        where tenant_id = #{tenantId} and bot_id = #{botId}
    </select>




</mapper>