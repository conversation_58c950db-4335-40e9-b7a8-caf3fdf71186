<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 4.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiwise.dialogflow.mapper.FolderPOMapper">
    <resultMap id="BaseResultMap" type="com.yiwise.dialogflow.entity.po.FolderPO">
        <result column="folder_id" property="folderId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="system_type" property="systemType" jdbcType="TINYINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="parent_folder_id" property="parentFolderId" jdbcType="BIGINT"/>
        <result column="depth" property="depth" jdbcType="INTEGER"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="getByTenantIdAndDepth" resultMap="BaseResultMap">
        select *
        from folder
        where tenant_id = #{tenantId}
        and depth >= #{depth}
        <if test="createUserIdList != null and createUserIdList.size() > 0">
            and create_user_id in
            <foreach collection="createUserIdList" item="createUserId" open="(" separator="," close=")">
                #{createUserId}
            </foreach>
        </if>
        order by folder_id desc
    </select>

    <select id="countByDepth" resultType="java.lang.Integer">
        select count(0)
        from folder
        where tenant_id = #{tenantId}
          and depth = #{depth}
    </select>

    <select id="search" resultMap="BaseResultMap">
        select *
        from folder
        where tenant_id = #{tenantId}
        <if test="name != null and name != ''">
            and name like concat('%', #{name}, '%')
        </if>
        order by folder_id desc
    </select>

    <select id="selectCreateUserIdByTenantId" resultType="java.lang.Long">
        select distinct create_user_id
        from folder
        where tenant_id = #{tenantId}
    </select>

</mapper>