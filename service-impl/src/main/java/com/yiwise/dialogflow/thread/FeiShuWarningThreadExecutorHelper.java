package com.yiwise.dialogflow.thread;

import org.apache.commons.lang3.concurrent.BasicThreadFactory;

import java.util.concurrent.*;

public class FeiShuWarningThreadExecutorHelper {
    private static final ScheduledExecutorService monitorThread = Executors.newSingleThreadScheduledExecutor(new BasicThreadFactory.Builder().namingPattern("FEISHU_WARNING").daemon(true).build());

    private static final ThreadPoolExecutor threadPool = new ThreadPoolExecutor(5, 10, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1024), new BasicThreadFactory.Builder().namingPattern("FEISHU_WARNING-%d").build(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static final DynamicDataSourceApplicationExecutor APPLICATION_EXECUTOR = new DynamicDataSourceApplicationExecutor(monitorThread, threadPool);

    public static void startThreadPoolStatusMonitor() {
        APPLICATION_EXECUTOR.startThreadPoolStatusMonitor();
    }

    static {
        startThreadPoolStatusMonitor();
    }

    public static void execute(String taskTitle, Runnable runnable) {
        APPLICATION_EXECUTOR.execute(taskTitle, runnable);
    }

    public static <T> Future<T> submit(String taskTitle, Callable<T> callable) {
        return APPLICATION_EXECUTOR.submit(taskTitle, callable);
    }

}
