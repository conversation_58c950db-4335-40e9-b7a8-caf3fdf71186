package com.yiwise.dialogflow.thread;

import org.apache.commons.lang3.concurrent.BasicThreadFactory;

import java.util.concurrent.*;

public class DynamicDataSourceApplicationExecutorHolder {
    private static final ScheduledExecutorService monitorThread = Executors.newSingleThreadScheduledExecutor(new BasicThreadFactory.Builder().namingPattern("THREAD_STATE").daemon(true).build());
    private static final ThreadPoolExecutor threadPool = new ThreadPoolExecutor(100, 150, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1024), new BasicThreadFactory.Builder().namingPattern("AppExec-%d").build(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static final DynamicDataSourceApplicationExecutor APPLICATION_EXECUTOR = new DynamicDataSourceApplicationExecutor(monitorThread, threadPool);

    public static void startThreadPoolStatusMonitor() {
        APPLICATION_EXECUTOR.startThreadPoolStatusMonitor();
    }

    public static void execute(String taskTitle, Runnable runnable) {
        APPLICATION_EXECUTOR.execute(taskTitle, runnable);
    }
}
