package com.yiwise.dialogflow.utils;

import cn.hutool.core.util.ZipUtil;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.file.MyFileUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.OssKeyCenter;
import com.yiwise.dialogflow.common.TempFilePathKeyCenter;
import com.yiwise.dialogflow.helper.AddOssPrefixSerializer;
import com.yiwise.dialogflow.thread.DynamicDataSourceApplicationExecutorHolder;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Slf4j
public class BotExportUtils {

    private static final Pattern ILLEGAL_CHARACTER_IN_BOT_NAME_MATCH_PATTERN = Pattern.compile("[/+%]");

    private static final ObjectStorageHelper OBJECT_STORAGE_HELPER = AppContextUtils.getBean(ObjectStorageHelper.class);

    public static String normalizeFileName(String ossFileName) {
        return ILLEGAL_CHARACTER_IN_BOT_NAME_MATCH_PATTERN.matcher(ossFileName).replaceAll("_");
    }

    public static String uploadFile(Long botId, String ossFileName, Consumer<FileOutputStream> consumer) {
        File file = null;
        try {
            file = exportFile(botId, ossFileName, consumer);
            return uploadFile(botId, file.getName(), file);
        } finally {
            FileUtils.deleteQuietly(file);
        }
    }

    public static String uploadFile(Long botId, String ossFileName, File localFile) {
        String finalName = normalizeFileName(ossFileName);
        String ossKey = OssKeyCenter.getBotExportFileKey(botId, finalName);
        return uploadFile(ossKey, localFile);
    }

    public static String uploadFile(String ossKey, File localFile) {
        return AddOssPrefixSerializer.getAddOssPrefixUrl(OBJECT_STORAGE_HELPER.upload(ossKey, localFile));
    }

    public static File exportFile(Long botId, String fileName, Consumer<FileOutputStream> consumer) {
        String finalName = normalizeFileName(fileName);
        String localFilePath = TempFilePathKeyCenter.getBotExportFilePath(botId, finalName);
        return exportFile(localFilePath, consumer);
    }

    public static File exportFile(String dir, String fileName, Consumer<FileOutputStream> consumer) {
        String finalName = normalizeFileName(fileName);
        String localFilePath = dir + finalName;
        return exportFile(localFilePath, consumer);
    }

    public static File exportFile(String localFilePath, Consumer<FileOutputStream> consumer) {
        FileOutputStream fos = null;
        try {
            MyFileUtils.makeFileExist(localFilePath);
            File file = new File(localFilePath);
            fos = new FileOutputStream(file);
            consumer.accept(fos);
            return file;
        } catch (Exception e) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "导出失败", e);
        } finally {
            IOUtils.closeQuietly(fos, null);
        }
    }

    @FunctionalInterface
    public interface FileGenerator {

        File generate(String src, Consumer<FileOutputStream> consumer);
    }

    public static <S> String batchExport(List<S> srcList, String zipFileName, BiConsumer<S, FileGenerator> biFunction) {
        // 批量导出目录
        String dir = TempFilePathKeyCenter.getBotBatchExportDir();

        AtomicInteger successCount = new AtomicInteger();
        CountDownLatch latch = new CountDownLatch(srcList.size());

        for (S src : srcList) {
            DynamicDataSourceApplicationExecutorHolder.execute("批量导出-" + zipFileName, () -> {
                try {
                    biFunction.accept(src, (fileName, consumer) -> BotExportUtils.exportFile(dir, fileName, consumer));
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    log.error(e.getMessage(),e);
                } finally {
                    latch.countDown();
                }
            });
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "导出失败", e);
        }

        // 所有话术都导出失败了
        if (successCount.get() == 0) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "导出失败");
        }
        return zipAndUpload(dir, zipFileName);
    }

    private static String zipAndUpload(String dir, String zipFileName) {
        File zip = null;
        try {
            zip = ZipUtil.zip(dir);
            return uploadFile(0L, zipFileName, zip);
        } finally {
            FileUtils.deleteQuietly(zip);
            FileUtils.deleteQuietly(new File(dir));
        }
    }
}
