package com.yiwise.dialogflow.utils;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class PinyinUtils {
    private static final Logger log = LoggerFactory.getLogger(com.yiwise.base.common.text.PinyinUtils.class);
    private static final HanyuPinyinOutputFormat FORMAT = new HanyuPinyinOutputFormat();

    public PinyinUtils() {
    }

    public static void init() {
        try {
            PinyinHelper.toHanYuPinyinString("", FORMAT, "", true);
            log.info("PinyinUtil初始化成功");
        } catch (BadHanyuPinyinOutputFormatCombination var1) {
            log.error("PinyinUtil初始化失败", var1);
        }

    }

    public static String toPinyin(String chinese) {
        if (chinese != null && chinese.length() != 0) {
            try {
                List<String> result = new ArrayList<>(chinese.length());
                for(int i = 0; i < chinese.length(); ++i) {
                    char c = chinese.charAt(i);
                    String[] strings = PinyinHelper.toHanyuPinyinStringArray(c, FORMAT);
                    if (strings.length > 0) {
                        result.add(strings[0]);
                    } else {
                        result.add(String.valueOf(c));
                    }
                }

                return String.join(" ", result);
            } catch (BadHanyuPinyinOutputFormatCombination var6) {
                log.error("汉语转拼音失败", var6);
                return "";
            }
        } else {
            return chinese;
        }
    }

    public static void main(String[] args) {
        System.out.println(toPinyin("好的，那您待会查收下短信，如果您没收到短信，可以在短信拦截中查看下，感谢您的支持，祝您生活愉快，再见！"));
        long start = System.currentTimeMillis();

        for(int i = 0; i < 100; ++i) {
            toPinyin("好的，那您待会查收下短信，如果您没收到短信，可以在短信拦截中查看下，感谢您的支持，祝您生活愉快，再见！");
        }

        System.out.println(System.currentTimeMillis() - start);
    }

    static {
        FORMAT.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        FORMAT.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        FORMAT.setVCharType(HanyuPinyinVCharType.WITH_V);
        init();
    }
}
