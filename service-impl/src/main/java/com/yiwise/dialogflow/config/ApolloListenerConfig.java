package com.yiwise.dialogflow.config;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.google.common.collect.Maps;
import com.yiwise.dialogflow.entity.bo.feishu.AbstractAlertConfig;
import com.yiwise.dialogflow.service.feishu.FeishuAlertService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/11/2
 */
@Slf4j
@DependsOn("feishuAlertServiceImpl")
@Configuration
@EnableApolloConfig
public class ApolloListenerConfig {

    @Resource
    private FeishuAlertService feishuAlertService;

    @ApolloConfigChangeListener(value = "alertConfig")
    public void onAlertConfigChange(ConfigChangeEvent changeEvent) {
        log.info("Changes for namespace {}", changeEvent.getNamespace());
        Set<String> changedKeys = changeEvent.changedKeys();
        for (String key : changeEvent.changedKeys()) {
            ConfigChange change = changeEvent.getChange(key);
            log.info("Change - key: {}, oldValue: {}, newValue: {}, changeType: {}",
                    change.getPropertyName(), change.getOldValue(), change.getNewValue(), change.getChangeType());
        }
        Config config = ConfigService.getConfig("alertConfig");
        Set<String> propertyNames = config.getPropertyNames();
        Map<String, Properties> fieldMap = Maps.newHashMap();
        propertyNames.forEach(key -> {
            String[] split = key.split("\\.", 2);
            Properties properties = fieldMap.computeIfAbsent(split[0], (k) -> new Properties());
            properties.put(split[1], config.getProperty(key, null));
        });
        changedKeys.forEach(changedKey -> {
            String[] split = changedKey.split("\\.", 2);
            Properties properties = fieldMap.get(split[0]);
            AbstractAlertConfig alertConfig = new AbstractAlertConfig() {};
            properties.forEach((k, v) -> alertConfig.updateField((String) k, (String) v));
            feishuAlertService.registryAlertConfig(alertConfig);
        });
    }
}
