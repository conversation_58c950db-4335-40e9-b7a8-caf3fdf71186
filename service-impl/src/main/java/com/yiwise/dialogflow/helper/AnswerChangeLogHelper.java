package com.yiwise.dialogflow.helper;

import com.google.common.collect.Lists;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.enums.PostActionTypeEnum;
import com.yiwise.dialogflow.entity.po.BaseAnswerContent;
import com.yiwise.dialogflow.entity.po.KnowledgeAnswer;
import com.yiwise.dialogflow.service.operationlog.UninterruptedOperationLogService;
import com.yiwise.dialogflow.utils.AnswerConditionUtil;
import javaslang.Tuple2;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public class AnswerChangeLogHelper {

    private static UninterruptedOperationLogService uninterruptedOperationLogService = AppContextUtils.getBean(UninterruptedOperationLogService.class);

    public static List<String> compareAnswerChange(List<? extends BaseAnswerContent> oldAnswerList,
                                                   List<? extends BaseAnswerContent> newAnswerList,
                                                   String logPrefix,
                                                   DependentResourceBO resource) {
        if (Objects.isNull(oldAnswerList)) {
            oldAnswerList = Lists.newArrayList();
        }
        if (Objects.isNull(newAnswerList)) {
            newAnswerList = Lists.newArrayList();
        }
        Map<String, ? extends BaseAnswerContent> oldAnswerMap = MyCollectionUtils.listToMap(oldAnswerList, BaseAnswerContent::getUniqueId);
        Map<String, ? extends BaseAnswerContent> newAnswerMap = MyCollectionUtils.listToMap(newAnswerList, BaseAnswerContent::getUniqueId);
        List<BaseAnswerContent> addAnswerList = new ArrayList<>();
        List<BaseAnswerContent> deleteAnswerList = new ArrayList<>();
        List<Tuple2<BaseAnswerContent, BaseAnswerContent>> updateAnswerList = new ArrayList<>();
        newAnswerList.forEach(answer -> {
            if (oldAnswerMap.containsKey(answer.getUniqueId())) {
                updateAnswerList.add(new Tuple2<>(oldAnswerMap.get(answer.getUniqueId()), answer));
            } else {
                addAnswerList.add(answer);
            }
        });
        oldAnswerList.forEach(oldAnswer -> {
            if (!newAnswerMap.containsKey(oldAnswer.getUniqueId())) {
                deleteAnswerList.add(oldAnswer);
            }
        });

        List<String> changeLogList = new ArrayList<>();
        addAnswerList.forEach(newAnswer -> {
            changeLogList.add(String.format("%s新增话术【%s】", logPrefix, newAnswer.getText()));
        });
        deleteAnswerList.forEach(answer -> {
            changeLogList.add(String.format("%s删除话术【%s】", logPrefix, answer.getText()));
        });
        updateAnswerList.forEach(tuple -> {
            BaseAnswerContent oldAnswer = tuple._1;
            BaseAnswerContent newAnswer = tuple._2;
            // 比较答案内容
            if (!StringUtils.equals(oldAnswer.getText(), newAnswer.getText())) {
                changeLogList.add(String.format("%s话术【%s】修改为【%s】", logPrefix, oldAnswer.getText(), newAnswer.getText()));
            }
            // 比较回答后操作
            if (oldAnswer instanceof KnowledgeAnswer && newAnswer instanceof KnowledgeAnswer) {
                KnowledgeAnswer oldKnowledgeAnswer = (KnowledgeAnswer) oldAnswer;
                KnowledgeAnswer newKnowledgeAnswer = (KnowledgeAnswer) newAnswer;
                // 判断回答后操作是否变更
                if (!Objects.equals(oldKnowledgeAnswer.getPostAction(), newKnowledgeAnswer.getPostAction())) {
                    String oldAction = oldKnowledgeAnswer.getPostAction().getDesc();
                    String newAction = !PostActionTypeEnum.SPECIFIED_STEP.equals(newKnowledgeAnswer.getPostAction())
                            ? newKnowledgeAnswer.getPostAction().getDesc()
                            : String.format("%s:%s", newKnowledgeAnswer.getPostAction().getDesc(), resource.getStepIdNameMap().getOrDefault(newKnowledgeAnswer.getJumpStepId(), ""));
                    changeLogList.add(String.format("%s话术【%s】回答后【%s】操作修改为【%s】",
                            logPrefix, newAnswer.getText(), oldAction, newAction));
                }
                changeLogList.addAll(uninterruptedOperationLogService.compare(String.format("%s话术【%s】", logPrefix, newAnswer.getText()), oldKnowledgeAnswer, newKnowledgeAnswer, resource));
            }

            // 比较触发条件
            String oldCondition = generateConditionStr(oldAnswer, resource);
            String newCondition = generateConditionStr(newAnswer, resource);
            if (!StringUtils.equals(oldCondition, newCondition)) {
                changeLogList.add(String.format("%s话术【%s】触发条件【%s】修改为【%s】", logPrefix, newAnswer.getText(), oldCondition, newCondition));
            }
        });

        return changeLogList;
    }

    private static String generateConditionStr(BaseAnswerContent answer, DependentResourceBO resource) {
        return AnswerConditionUtil.generateConditionStrContent(answer, resource.getVariableIdNameMap(), resource.getIntentIdNameMap());
    }

}
