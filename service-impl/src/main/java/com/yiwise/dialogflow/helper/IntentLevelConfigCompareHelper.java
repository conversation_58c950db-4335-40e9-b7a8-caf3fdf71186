package com.yiwise.dialogflow.helper;

import com.yiwise.dialogflow.entity.po.IntentLevelSource;
import org.apache.commons.lang3.BooleanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class IntentLevelConfigCompareHelper {

    public static List<String> compareIntentLevelConfigChangeLog(IntentLevelSource oldConfig,
                                                                 IntentLevelSource newConfig,
                                                                 String prefix,
                                                                 Map<Integer, String> intentLevelCode2NameMap) {
        List<String> logDetailList = new ArrayList<>();
        boolean changeStatus = !Objects.equals(BooleanUtils.isTrue(oldConfig.getEnableIntentLevel()), BooleanUtils.isTrue(newConfig.getEnableIntentLevel()));
        boolean changeValue = !changeStatus && BooleanUtils.isTrue(newConfig.getEnableIntentLevel()) && !Objects.equals(oldConfig.getIntentLevelDetailCode(), newConfig.getIntentLevelDetailCode());
        String oldVal = BooleanUtils.isNotTrue(oldConfig.getEnableIntentLevel()) ? "未开启": intentLevelCode2NameMap.getOrDefault(oldConfig.getIntentLevelDetailCode(), "" + oldConfig.getIntentLevelDetailCode());
        String newVal = BooleanUtils.isNotTrue(newConfig.getEnableIntentLevel()) ? "未开启": intentLevelCode2NameMap.getOrDefault(newConfig.getIntentLevelDetailCode(), "" + newConfig.getIntentLevelDetailCode());
        if (changeStatus) {
            logDetailList.add(String.format("%s意向等级设置【%s】修改为【%s】",
                    prefix, oldVal, newVal));
        }
        if (changeValue) {
            logDetailList.add(String.format("%s意向等级设置【%s】修改为【%s】",
                    prefix, oldVal, newVal));
        }

        return logDetailList;
    }
}
