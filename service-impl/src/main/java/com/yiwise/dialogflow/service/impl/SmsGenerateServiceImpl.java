package com.yiwise.dialogflow.service.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import com.yiwise.dialogflow.entity.enums.SmsGenerateLinkTypeEnum;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.SmsGenerateResultPO;
import com.yiwise.dialogflow.entity.vo.smsGenerate.SmsGenerateCreateVO;
import com.yiwise.dialogflow.entity.vo.smsGenerate.SmsGenerateDeleteVO;
import com.yiwise.dialogflow.entity.vo.smsGenerate.SmsGenerateUpdateVO;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.entity.vo.smsGenerate.*;
import com.yiwise.dialogflow.service.BotResourceSequenceService;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.SmsGenerateService;
import com.yiwise.dialogflow.service.botgenerate.BotRewriteService;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SmsGenerateServiceImpl implements SmsGenerateService {

    @Resource(name = "longestLatencyRestTemplate")
    private RestTemplate restTemplate;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private BotResourceSequenceService botResourceSequenceService;

    @Resource
    private BotRewriteService botRewriteService;

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private BotService botService;

    private static final Pattern SMS_PLACEHOLDER_REGEX = Pattern.compile("\\$\\{短信链接\\d*}");

    private void saveLog(Long botId, Long userId, String detail) {
        operationLogService.save(botId, OperationLogTypeEnum.SMS_GENERATE, OperationLogResourceTypeEnum.SMS_GENERATE, detail, userId);
    }

    private void checkParam(SmsGenerateCreateVO request) {
        if (BooleanUtils.isFalse(request.getInsertLink())) {
            return;
        }
        if (Objects.isNull(request.getLinkType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "链接类型不能为空");
        }
        if (SmsGenerateLinkTypeEnum.CUSTOMIZE.equals(request.getLinkType()) && StringUtils.isBlank(request.getLink())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "自定义短信链接不能为空");
        }
    }

    @Override
    public List<SmsGenerateResultPO> generate(SmsGenerateCreateVO request, Long userId) {
        // 参数校验
        checkParam(request);
        // 调用算法生成短信文案
        List<String> textList = doGenerate(request);
        // 批量保存
        List<SmsGenerateResultPO> resultList = batchSave(request.getBotId(), textList);
        // 保存日志
        saveLog(request.getBotId(), userId, String.format("生成短信:【%s】", generateLogDesc(resultList)));
        return resultList;
    }

    private List<SmsGenerateResultPO> batchSave(Long botId, List<String> resultList) {
        List<Integer> seqList = botResourceSequenceService.generate(botId, SmsGenerateResultPO.COLLECTION_NAME, resultList.size());
        List<SmsGenerateResultPO> entityList = new ArrayList<>(resultList.size());
        for (int i = 0; i < resultList.size(); i++) {
            SmsGenerateResultPO entity = new SmsGenerateResultPO();
            entity.setId(null);
            entity.setBotId(botId);
            entity.setSeq(seqList.get(i));
            entity.setContent(resultList.get(i));
            entityList.add(entity);
        }
        mongoTemplate.insert(entityList, SmsGenerateResultPO.COLLECTION_NAME);
        return entityList;
    }

    private List<String> doGenerate(SmsGenerateCreateVO request) {
        Map<String, List<String>> context;
        try {
            // 生成offer信息
            context = botRewriteService.getOrGenPromptInfo(request.getBotId());
        } catch (Exception e) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "话术生成offer信息失败", e);
        }
        if (MapUtils.isEmpty(context)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "生成的offer信息为空");
        }
        if (!context.containsKey("主利益点")) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "生成的offer信息中不包含主利益点");
        }

        String url = ApplicationConstant.SMS_GENERATE_URL;
        Map<String, Object> param = new HashMap<>(3);
        param.put("context", context);
        param.put("has_link", request.getInsertLink());
        param.put("n", 5);
        param.put("temperature", 0.4);
        param.put("customerSceneId", Optional.ofNullable(botService.getById(request.getBotId())).map(BotPO::getCustomerSceneId).orElse(null));
        param.put("namespace", ApplicationConstant.ALGORITHM_SERVICE_NAMESPACE);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", "application/json");
        HttpEntity<String> requestEntity = new HttpEntity<>(JsonUtils.object2String(param), httpHeaders);

        log.info("请求短信生成接口,url={},body={}", url, JSON.toJSONString(param));
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        log.info("请求短信生成结果为:response={}", response);
        if (!HttpStatus.OK.equals(response.getStatusCode())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "调用短信生成接口失败");
        }
        SmsGenerateResult smsGenerateResult = JsonUtils.string2Object(response.getBody(), SmsGenerateResult.class);
        if (Objects.isNull(smsGenerateResult) || CollectionUtils.isEmpty(smsGenerateResult.getTextList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "短信生成结果为空");
        }
        List<String> textList = smsGenerateResult.getTextList();
        // 替换自定义链接
        if (BooleanUtils.isTrue(request.getInsertLink()) && SmsGenerateLinkTypeEnum.CUSTOMIZE.equals(request.getLinkType()) && StringUtils.isNotBlank(request.getLink())) {
            textList = textList.stream().map(s -> SMS_PLACEHOLDER_REGEX.matcher(s).replaceAll(request.getLink())).collect(Collectors.toList());
        }
        return textList;
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    private static class SmsGenerateResult {

        @JsonProperty("text_list")
        List<String> textList;
    }

    private String generateLogDesc(List<SmsGenerateResultPO> list) {
        return list.stream().map(po -> String.format("【%s】【%s】", po.getSeq(), po.getContent())).collect(Collectors.joining(","));
    }

    @Override
    public PageResultObject<SmsGenerateResultPO> list(Long botId, Integer pageNum, Integer pageSize) {
        Query query = Query.query(Criteria.where("botId").is(botId));
        long count = mongoTemplate.count(query, SmsGenerateResultPO.COLLECTION_NAME);

        List<SmsGenerateResultPO> resultList;
        if (count <= 0) {
            resultList = Collections.emptyList();
        } else {
            query.with(PageRequest.of(pageNum - 1, pageSize));
            query.with(Sort.by(Sort.Direction.DESC, "isPinned", "pinTime", "seq"));
            resultList = mongoTemplate.find(query, SmsGenerateResultPO.class, SmsGenerateResultPO.COLLECTION_NAME);
        }

        return PageResultObject.of(resultList, pageNum, pageSize, (int) count);
    }

    @Override
    public void delete(SmsGenerateDeleteVO request, Long userId) {
        Query query = Query.query(Criteria.where("botId").is(request.getBotId()));
        if (BooleanUtils.isNotTrue(request.getDeleteAll()) && CollectionUtils.isNotEmpty(request.getIdList())) {
            query.addCriteria(Criteria.where("_id").in(request.getIdList()));
        }

        List<SmsGenerateResultPO> deleteList = mongoTemplate.findAllAndRemove(query, SmsGenerateResultPO.class, SmsGenerateResultPO.COLLECTION_NAME);
        if (CollectionUtils.isNotEmpty(deleteList)) {
            saveLog(request.getBotId(), userId, String.format("删除短信:【%s】", generateLogDesc(deleteList)));
        }
    }

    @Override
    public void update(SmsGenerateUpdateVO request, Long userId) {
        SmsGenerateResultPO old = mongoTemplate.findAndModify(
                Query.query(Criteria.where("botId").is(request.getBotId()).and("_id").is(request.getId()).and("content").ne(request.getContent())),
                Update.update("content", request.getContent()), SmsGenerateResultPO.class,
                SmsGenerateResultPO.COLLECTION_NAME
        );
        if (Objects.nonNull(old)) {
            saveLog(request.getBotId(), userId, String.format("修改短信内容:【%s】【%s】修改为【%s】", old.getSeq(), old.getContent(), request.getContent()));
        }
    }

    @Override
    public void pin(SmsGeneratePinVO request, Long userId) {
        Long botId = request.getBotId();
        SmsGenerateResultPO old = mongoTemplate.findAndModify(Query.query(Criteria.where("botId").is(botId).and("_id").is(request.getId()).and("isPinned").ne(true)),
                Update.update("isPinned", true).set("pinTime", LocalDateTime.now()),
                SmsGenerateResultPO.class,
                SmsGenerateResultPO.COLLECTION_NAME);
        if (Objects.nonNull(old)) {
            saveLog(botId, userId, String.format("置顶短信:【%s】【%s】", old.getSeq(), old.getContent()));
        }
    }

    @Override
    public void unPin(SmsGeneratePinVO request, Long userId) {
        Long botId = request.getBotId();
        SmsGenerateResultPO old = mongoTemplate.findAndModify(Query.query(Criteria.where("botId").is(botId).and("_id").is(request.getId()).and("isPinned").is(true)),
                Update.update("isPinned", null).set("pinTime", null),
                SmsGenerateResultPO.class,
                SmsGenerateResultPO.COLLECTION_NAME);
        if (Objects.nonNull(old)) {
            saveLog(botId, userId, String.format("取消置顶短信:【%s】【%s】", old.getSeq(), old.getContent()));
        }
    }
}
