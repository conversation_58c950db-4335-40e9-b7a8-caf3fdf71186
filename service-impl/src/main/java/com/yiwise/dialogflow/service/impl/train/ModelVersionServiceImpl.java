package com.yiwise.dialogflow.service.impl.train;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.enums.AlgorithmTrainTypeEnum;
import com.yiwise.dialogflow.entity.po.ModelVersionPO;
import com.yiwise.dialogflow.service.train.ModelVersionService;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2020/2/21
 */
@Service
public class ModelVersionServiceImpl implements ModelVersionService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public ModelVersionPO get(String modelId) {
        ModelVersionPO modelVersionPO = mongoTemplate.findById(modelId, ModelVersionPO.class);
        if (modelVersionPO == null) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "modelId 不能为 null");
        }
        return modelVersionPO;
    }

    @Override
    public String create(Long tenantId, Long robotId, AlgorithmTrainTypeEnum type) {
        ModelVersionPO modelVersionPO = new ModelVersionPO();
        modelVersionPO.setModelId(UUID.randomUUID().toString());
        modelVersionPO.setTenantId(tenantId);
        modelVersionPO.setRobotId(robotId);
        modelVersionPO.setType(type);
        modelVersionPO.setCreateTime(LocalDateTime.now());
        modelVersionPO.setUpdateTime(LocalDateTime.now());
        mongoTemplate.save(modelVersionPO, ModelVersionPO.COLLECTION_NAME);
        return modelVersionPO.getModelId();
    }

    @Override
    public ModelVersionPO createByRefId(Long tenantId, String refId, AlgorithmTrainTypeEnum type) {
        ModelVersionPO modelVersionPO = new ModelVersionPO();
        modelVersionPO.setModelId(UUID.randomUUID().toString());
        modelVersionPO.setTenantId(tenantId);
        modelVersionPO.setRefId(refId);
        modelVersionPO.setType(type);
        modelVersionPO.setCreateTime(LocalDateTime.now());
        modelVersionPO.setUpdateTime(LocalDateTime.now());
        mongoTemplate.save(modelVersionPO, ModelVersionPO.COLLECTION_NAME);
        return modelVersionPO;
    }

    @Nullable
    @Override
    public ModelVersionPO getLast(Long tenantId, Long robotId, AlgorithmTrainTypeEnum type) {
        // 意图和知识共用同一个模型了
        if (AlgorithmTrainTypeEnum.KNOWLEDGE.equals(type)) {
            type = AlgorithmTrainTypeEnum.INTENT;
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("tenantId").is(tenantId));
        query.addCriteria(Criteria.where("robotId").is(robotId));
        query.addCriteria(Criteria.where("type").is(type.name()));
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        return mongoTemplate.findOne(query, ModelVersionPO.class);
    }

    @Override
    public ModelVersionPO getByRefId(Long tenantId, String refId, AlgorithmTrainTypeEnum type) {
        Query query = new Query();
        query.addCriteria(Criteria.where("tenantId").is(tenantId));
        query.addCriteria(Criteria.where("refId").is(refId));
        query.addCriteria(Criteria.where("type").is(type.name()));
        if (mongoTemplate.exists(query, ModelVersionPO.class)) {
            query.with(Sort.by(Sort.Direction.DESC, "createTime"));
            return mongoTemplate.findOne(query, ModelVersionPO.class);
        } else {
            return createByRefId(tenantId, refId, type);
        }
    }
}
