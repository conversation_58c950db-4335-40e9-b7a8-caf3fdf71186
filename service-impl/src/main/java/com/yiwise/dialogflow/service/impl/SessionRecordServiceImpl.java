package com.yiwise.dialogflow.service.impl;

import com.yiwise.dialogflow.entity.po.SessionRecordPO;
import com.yiwise.dialogflow.service.SessionRecordService;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Service
public class SessionRecordServiceImpl implements SessionRecordService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public SessionRecordPO create(SessionRecordPO po) {
        po.setCreateTime(LocalDateTime.now());
        po.setUpdateTime(LocalDateTime.now());
        mongoTemplate.insert(po);
        return po;
    }



}
