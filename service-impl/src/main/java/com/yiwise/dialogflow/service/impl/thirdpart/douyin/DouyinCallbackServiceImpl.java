package com.yiwise.dialogflow.service.impl.thirdpart.douyin;

import com.alibaba.fastjson.JSON;
import com.yiwise.account.api.dto.RobotInfoDTO;
import com.yiwise.account.api.dto.RobotQueryVO;
import com.yiwise.account.api.dto.TenantDTO;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.common.utils.encrypt.Md5Utils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.api.dto.request.douyin.DouyinBotInfoQueryDTO;
import com.yiwise.dialogflow.api.dto.response.douyin.DouyinBotInfoDTO;
import com.yiwise.dialogflow.api.dto.response.douyin.DouyinGradingItemDTO;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.BotRefPO;
import com.yiwise.dialogflow.entity.po.VariablePO;
import com.yiwise.dialogflow.service.BotRefService;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.VariableService;
import com.yiwise.dialogflow.service.remote.IntentLevelTagDetailService;
import com.yiwise.dialogflow.service.remote.account.FeignTenantApi;
import com.yiwise.dialogflow.service.remote.account.RobotClient;
import com.yiwise.dialogflow.service.thirdpart.douyin.DouyinCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DouyinCallbackServiceImpl implements DouyinCallbackService {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private BotService botService;

    @Resource
    private BotRefService botRefService;

    @Resource
    private VariableService variableService;

    @Resource
    private IntentLevelTagDetailService intentLevelTagDetailService;

    @Resource
    private RobotClient robotClient;

    @Resource
    private FeignTenantApi feignTenantApi;

    @Override
    public List<DouyinBotInfoDTO> queryByCondition(DouyinBotInfoQueryDTO query) {
        log.debug("query:{}", JsonUtils.object2String(query));

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(query.getScriptIdList())) {
            return Collections.emptyList();
        }
        List<Long> botIdList = new ArrayList<>();

        query.getScriptIdList().forEach(scriptId -> {
            if (StringUtils.isNotBlank(scriptId) && scriptId.startsWith(ApplicationConstant.DOUYIN_CALLBACK_SCRIPT_PREFIX)) {
                String botStr = scriptId.substring(ApplicationConstant.DOUYIN_CALLBACK_SCRIPT_PREFIX.length());
                try {
                    botIdList.add(Long.parseLong(botStr.trim()));
                } catch (Exception e) {
                    log.warn("[LogHub_Warn]抖音回调脚本解析异常, scriptId:{}", scriptId);
                }
            }
        });
        List<BotPO> botList = botService.getByIdList(botIdList);

        List<BotRefPO> refList = botRefService.getByBotIdList(botIdList);
        Map<Long, Long> refMap = MyCollectionUtils.listToConvertMap(refList, BotRefPO::getBotId, BotRefPO::getTenantId);
        Integer concurrency = getTenantAvailableCount(query.getTenantId());
        return botList.stream()
                .map(bot -> {
                    Long bindTenantId = refMap.getOrDefault(bot.getBotId(), 0L);
                    return prepareData(bot.getBotId(), bot, bindTenantId, query.getTenantId(), concurrency);
                }).collect(Collectors.toList());
    }

    @Override
    public void callback(Long botId, Long tenantId) {
        try {
            log.debug("执行抖音回调, botId:{}, tenantId:{}", botId, tenantId);
            if (tenantId == null || tenantId < 1) {
                return;
            }
            TenantDTO tenant = feignTenantApi.getInfoById(tenantId);
            if (Objects.isNull(tenant)) {
                log.debug("tenant不存在, tenantId:{}", tenantId);
                return;
            }

            if (Objects.isNull(tenant.getNewMainBrandId())
                    || (tenant.getNewMainBrandId() > 0L
                    && !ApplicationConstant.DOUYIN_CALLBACK_MAINBRAND_IDS.contains(String.format(",%s,", tenant.getNewMainBrandId())))) {
                log.debug("非抖音主品牌, tenantId:{}, mainBrandId:{}, 抖音主品牌:{}", tenantId, tenant.getNewMainBrandId(), ApplicationConstant.DOUYIN_CALLBACK_MAINBRAND_IDS);
                return;
            }

            DouyinBotInfoDTO dto = prepareData(botId,
                    botService.getById(botId),
                    botRefService.getTenantIdByBotId(botId),
                    tenantId,
                    getTenantAvailableCount(tenantId));
            doCallback(dto);
        } catch (Exception e) {
            log.warn("[LogHub_Warn]执行抖音回调异常, botId:{}, tenantId:{}", botId, tenantId, e);
        }

    }

    @Override
    public void callback(Long botId) {
        callback(botId, botRefService.getTenantIdByBotId(botId));
    }

    private DouyinBotInfoDTO prepareData(Long botId, BotPO bot, Long bindTenantId, Long tenantId, Integer concurrency) {
        DouyinBotInfoDTO dto = new DouyinBotInfoDTO();


        dto.setParams(getParams(botId));
        dto.setProvider(ApplicationConstant.DOUYIN_CALLBACK_PROVIDER);
        dto.setScript(String.format("%s%s", ApplicationConstant.DOUYIN_CALLBACK_SCRIPT_PREFIX, botId));
        dto.setConcurrency(concurrency);
        dto.setBusinessLineId(parseBusinessLineId(bot));

        if (bot != null) {
            dto.setGradings(getGradingItemList(bot.getIntentLevelTagId()));
        } else {
            dto.setGradings(Collections.emptyList());
        }

        dto.setStatus(DouyinBotInfoDTO.STATUS_DISABLE);
        if (tenantId.equals(bindTenantId)
                && bot != null
                && BooleanUtils.isTrue(bot.getPublished())) {
            dto.setStatus(DouyinBotInfoDTO.STATUS_ENABLE);
        }

        return dto;
    }

    private Integer parseBusinessLineId(BotPO bot) {
        if (Objects.isNull(bot)) {
            return null;
        }
        if (StringUtils.isBlank(bot.getDescription())) {
            log.debug("[LogHub_Warn] 抖音bot:{} 未配置业务线信息", bot.getBotId());
        }
        try {
            return Integer.parseInt(bot.getDescription());
        } catch (Exception e) {
            log.warn("[LogHub_Warn] 抖音bot:{} 业务线配置错误:{}", bot.getBotId(), bot.getDescription());
        }
        return null;
    }

    private @NotNull Map<String, String> getParams(Long botId) {
        List<VariablePO> variableList = variableService.getListByBotId(botId);
        Set<String> usedVariableNameSet = variableService.getRealUsedVariableNameSet(botId);
        Map<String, String> variableMap = new HashMap<>();
        for (VariablePO item : variableList) {
            if (usedVariableNameSet.contains(item.getName())) {
                variableMap.put(item.getName(), item.getDesc());
            }
        }
        return variableMap;
    }


    private void doCallback(DouyinBotInfoDTO botInfo) {
        if (StringUtils.isBlank(ApplicationConstant.DOUYIN_CALLBACK_URL)) {
            log.warn("[LogHub_Warn]抖音回调地址为空，不进行回调");
            return;
        }
        String url = ApplicationConstant.DOUYIN_CALLBACK_URL;
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            int timestamp = (int)(System.currentTimeMillis()/1000);
            httpHeaders.add("Content-Type", "application/json");
            httpHeaders.add("provider", ApplicationConstant.DOUYIN_CALLBACK_PROVIDER);
            httpHeaders.add("timestamp", String.valueOf(timestamp));
            httpHeaders.add("authorization", authorization(timestamp));

            HttpEntity<String> requestEntity = new HttpEntity<>(JsonUtils.object2String(botInfo), httpHeaders);
            log.info("抖音回调,url={}, header:{}, body:{}", url, JsonUtils.object2String(httpHeaders), JsonUtils.object2String(botInfo));
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
            log.info("抖音回调结果为:response={}", response);
        } catch (Exception e) {
            log.warn("[LogHub_Warn]调用抖音回调接口失败, url={}, body={}, 失败原因={}", url, JsonUtils.object2String(botInfo), e.getMessage());
        }
    }

    private String authorization(int timestamp) {
        String text = ApplicationConstant.DOUYIN_CALLBACK_PROVIDER + ApplicationConstant.DOUYIN_CAllBACK_SK + timestamp;
        String result =  Md5Utils.getMd5String(text);
        log.debug("抖音回调签名, text={}, result={}", text, result);
        return result;
    }

    private int getTenantAvailableCount(Long tenantId) {
        RobotQueryVO robotQueryVO = new RobotQueryVO();
        robotQueryVO.setTenantId(tenantId);
        robotQueryVO.setSystemEnum(SystemEnum.RECEPTION_BOT);
        robotQueryVO.setWithPage(false);
        PageResultObject<RobotInfoDTO> byTenantIdAndAiccPart = robotClient.getByTenantIdAndAiccPart(robotQueryVO);
        if (byTenantIdAndAiccPart == null) {
            return 0;
        }

        List<RobotInfoDTO> content = byTenantIdAndAiccPart.getContent();
        AtomicInteger total = new AtomicInteger();
        if (CollectionUtils.isNotEmpty(content)) {
            content = content.stream().filter(x -> {
                LocalDate now = LocalDate.now();
                return !now.isBefore(x.getStartDate()) && !now.isAfter(x.getEndDate());
            }).collect(Collectors.toList());
            content.forEach(x -> {
                if (x.getCount() != null) {
                    total.addAndGet(x.getCount());
                }
            });
        }

        return total.get();
    }

    private List<DouyinGradingItemDTO> getGradingItemList(Long intentLevelTagId) {
        return intentLevelTagDetailService.getIntentLevelTagDetailList(intentLevelTagId).stream()
                .map(detail -> {
                    DouyinGradingItemDTO item = new DouyinGradingItemDTO();
                    String name = detail.getName();
                    // 对name 进行解析, name 格式为 A(xxx), 需要解析出 A 和 xxx
                    String symbol = name.substring(0, name.indexOf("(")).trim();
                    String desc = name.substring(name.indexOf("(") + 1, name.lastIndexOf(")")).trim();
                    item.setGrading(symbol);
                    item.setGrading_key(desc);
                    return item;
                }).collect(Collectors.toList());

    }
}
