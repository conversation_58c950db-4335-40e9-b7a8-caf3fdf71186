package com.yiwise.dialogflow.service.impl.asrmodel;

import com.github.pagehelper.PageHelper;
import com.yiwise.base.common.thread.decorator.MDCDecoratorRunnable;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.enums.AsrSourceConcurrencyRecordTypeEnum;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrSourceConcurrencyRecordPO;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrVocabDetailPO;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrVocabProviderRelationPO;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.entity.query.AsrVocabDetailQuery;
import com.yiwise.dialogflow.entity.query.BotQuery;
import com.yiwise.dialogflow.entity.vo.BotVO;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrVocabDetailVO;
import com.yiwise.dialogflow.mapper.AsrVocabDetailPOMapper;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.asrmodel.AsrSourceConcurrencyRecordService;
import com.yiwise.dialogflow.service.asrmodel.AsrVocabProviderRelationService;
import com.yiwise.dialogflow.service.asrmodel.AsrVocabService;
import com.yiwise.dialogflow.service.remote.UserService;
import com.yiwise.dialogflow.thread.AsrModelThreadExecutorHelper;
import com.yiwise.dialogflow.utils.asr.AliAsrVocabApiUtil;
import com.yiwise.dialogflow.utils.asr.TencentVocabApiUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/19 09:44:30
 */
@Service
public class AsrVocabServiceImpl implements AsrVocabService {

    @Resource
    private AsrVocabDetailPOMapper asrVocabDetailPOMapper;

    @Resource
    private BotService botService;

    @Resource
    private AsrSourceConcurrencyRecordService asrSourceConcurrencyRecordService;

    @Resource
    private AsrVocabProviderRelationService asrVocabProviderRelationService;

    @Resource
    private UserService userService;

    @Override
    public List<AsrVocabDetailVO> list(AsrVocabDetailVO asrVocabDetailVO) {
        //todo
        return null;
    }

    @Override
    public PageResultObject<AsrVocabDetailVO> listWithPage(AsrVocabDetailQuery query) {
        List<BotVO> botVOList = new ArrayList<>();
        if (StringUtils.isNotEmpty(query.getBotInfo())) {
            BotQuery botQuery = new BotQuery();
            botQuery.setName(query.getBotInfo());
            botVOList = botService.queryListWithoutPage(botQuery);
            if (CollectionUtils.isNotEmpty(botVOList)) {
                query.setAsrVocabIdList(botVOList.stream().filter(botVO -> botVO.getAsrVocabId() != null).map(botVO -> botVO.getAsrVocabId()).collect(Collectors.toList()));
            } else {
                return PageResultObject.of(Collections.emptyList());
            }
        }
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<AsrVocabDetailVO> asrVocabDetailVOList = asrVocabDetailPOMapper.listByCondition(query);
        if (CollectionUtils.isNotEmpty(asrVocabDetailVOList)) {
            List<Long> sourceIdList = asrVocabDetailVOList.stream().map(AsrVocabDetailPO::getAsrVocabId).collect(Collectors.toList());
            List<AsrSourceConcurrencyRecordPO> asrSourceConcurrencyRecordPOList = asrSourceConcurrencyRecordService.countByTime(query.getStartTime(), query.getEndTime(), sourceIdList, AsrSourceConcurrencyRecordTypeEnum.ASR_VOCOB);
            Map<Long, Integer> countMap = asrSourceConcurrencyRecordPOList.stream().collect(Collectors.toMap(AsrSourceConcurrencyRecordPO::getSourceId, AsrSourceConcurrencyRecordPO::getCount));
            BotQuery botQuery = new BotQuery();
            botQuery.setAsrVocabIdList(asrVocabDetailVOList.stream().map(AsrVocabDetailVO::getAsrVocabId).collect(Collectors.toList()));
            List<BotVO> botVOS = botService.queryListWithoutPage(botQuery);
            List<Long> asrVocabIdList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(botVOS)) {
                asrVocabIdList = botVOS.stream().map(BotVO::getAsrVocabId).collect(Collectors.toList());
            }
            List<Long> finalAsrVocabIdList = asrVocabIdList;
            asrVocabDetailVOList.forEach(asrVocabDetailVO -> {
                //调用次数
                asrVocabDetailVO.setCallCount(countMap.getOrDefault(asrVocabDetailVO.getAsrVocabId(), 0));
                //是否绑定了话术
                if (CollectionUtils.isNotEmpty(finalAsrVocabIdList) && finalAsrVocabIdList.contains(asrVocabDetailVO.getAsrVocabId())) {
                    asrVocabDetailVO.setIsBind(true);
                }
                //更新人
                UserPO userPO = userService.getUserById(asrVocabDetailVO.getUpdateUserId());
                if (Objects.nonNull(userPO)) {
                    asrVocabDetailVO.setUpdateUserName(userPO.getName());
                }
            });
        }
        //删除停用超过12个月的
        AsrModelThreadExecutorHelper.execute("删除热词组", new MDCDecoratorRunnable(() -> {
            if (CollectionUtils.isNotEmpty(asrVocabDetailVOList)) {
                asrVocabDetailVOList.forEach(asrVocabDetailVO -> {
                    Duration between = Duration.between(asrVocabDetailVO.getUpdateTime(), LocalDateTime.now());
                    if (asrVocabDetailVO.getStatus() == 0 && between.toDays() >= 365) {
                        delete(asrVocabDetailVO);
                    }
                });
            }
        }));
        return PageResultObject.of(asrVocabDetailVOList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AsrVocabDetailVO asrVocabDetailVO) {
        vaildVocabContent(asrVocabDetailVO);
        asrVocabDetailVO.setUpdateUserId(asrVocabDetailVO.getCurrentUserId());
        asrVocabDetailVO.setUpdateTime(LocalDateTime.now());
        //更新
        if (Objects.nonNull(asrVocabDetailVO.getAsrVocabId())) {
            asrVocabDetailPOMapper.updateByPrimaryKeySelective(asrVocabDetailVO);
            List<AsrVocabProviderRelationPO> asrVocabProviderRelationPOList = asrVocabProviderRelationService.getByAsrVocabId(asrVocabDetailVO.getAsrVocabId());
            if (CollectionUtils.isNotEmpty(asrVocabProviderRelationPOList)) {
                asrVocabProviderRelationPOList.forEach(asrVocabProviderRelationPO -> {
                    switch (asrVocabProviderRelationPO.getProvider()) {
                        case ALI:
                            AliAsrVocabApiUtil.updateAsrVocab(asrVocabProviderRelationPO.getVocabularyId(), asrVocabDetailVO.getName(), asrVocabDetailVO.getDescription(), asrVocabDetailVO.getContent());
                            break;
                        case TENCENT:
                            TencentVocabApiUtil.update(asrVocabProviderRelationPO.getVocabularyId(), asrVocabDetailVO.getContent());
                            break;
                        default:
                            break;
                    }
                });
            }
        }
        //新增
        else {
            List<AsrVocabDetailPO> asrVocabDetailPOList = getByName(asrVocabDetailVO.getName());
            if (CollectionUtils.isNotEmpty(asrVocabDetailPOList)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "热词组名称重复");
            }
            asrVocabDetailVO.setCreateUserId(asrVocabDetailVO.getCurrentUserId());
            asrVocabDetailPOMapper.insertSelective(asrVocabDetailVO);
        }
    }

    private void vaildVocabContent(AsrVocabDetailVO asrVocabDetailVO) {
        Map<String, String> vocabMap = new HashMap<>();
        List<String> vaildContentList = new ArrayList<>();
        List<String> finalContentList = new ArrayList<>();
        List<String> contentList = asrVocabDetailVO.getContent();
        if (Objects.isNull(contentList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "热词内容不能为空");
        }
        //不合理的权重统一改为1
        contentList.forEach(content -> {
            if (Objects.isNull(content)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "热词组内容格式不正确");
            } else {
                content = content.replace(":", "：");
                String vaildContent = "";
                String[] split = content.split("：");
                if (split.length != 2) {
                    vaildContent = split[0] + "：1";
                } else {
                    String weigth = split[1];
                    if (isNumeric(weigth) && isZeroToTen(weigth)) {
                        vaildContent = content;
                    } else {
                        vaildContent = split[0] + "：1";
                    }
                }
                vaildContentList.add(vaildContent);
            }
        });
        //去重，重复的取正常值2
        vaildContentList.forEach(content -> {
            String[] split = content.split("：");
            if (vocabMap.containsKey(split[0])) {
                vocabMap.put(split[0], "2");
            } else {
                vocabMap.put(split[0], split[1]);
            }
        });
        vocabMap.forEach((k, v) -> {
            finalContentList.add(k + "：" + v);
        });
        asrVocabDetailVO.setContent(finalContentList);
    }

    /**
     * 判断是否为整数
     *
     * @param str
     * @return
     */
    private boolean isNumeric(String str) {
        for (int i = 0; i < str.length(); i++) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断是否为1-10
     *
     * @param str
     * @return
     */
    private boolean isZeroToTen(String str) {
        int parseInt = Integer.parseInt(str);
        if (parseInt >= 1 && parseInt <= 10) {
            return true;
        }
        return false;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(AsrVocabDetailVO asrVocabDetailVO) {
        if (Objects.isNull(asrVocabDetailVO.getAsrVocabId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "热词组id不能为空");
        }
        asrVocabDetailPOMapper.deleteByPrimaryKey(asrVocabDetailVO.getAsrVocabId());
        //删除对应厂商的热词组及关联信息
        List<AsrVocabProviderRelationPO> asrVocabProviderRelationPOList = asrVocabProviderRelationService.getByAsrVocabId(asrVocabDetailVO.getAsrVocabId());
        if (CollectionUtils.isNotEmpty(asrVocabProviderRelationPOList)) {
            asrVocabProviderRelationPOList.forEach(asrVocabProviderRelationPO -> {
                asrVocabProviderRelationService.delete(asrVocabProviderRelationPO);
                switch (asrVocabProviderRelationPO.getProvider()) {
                    case ALI:
                        AliAsrVocabApiUtil.deleteAsrVocab(asrVocabProviderRelationPO.getVocabularyId());
                        break;
                    case TENCENT:
                        TencentVocabApiUtil.delete(asrVocabProviderRelationPO.getVocabularyId());
                        break;
                    default:
                        break;
                }
            });
        }
    }

    @Override
    public void stop(AsrVocabDetailVO asrVocabDetailVO) {
        if (Objects.isNull(asrVocabDetailVO.getAsrVocabId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "热词组id不能为空");
        }
        asrVocabDetailVO.setStatus(0);
        asrVocabDetailVO.setUpdateTime(LocalDateTime.now());
        asrVocabDetailPOMapper.updateByPrimaryKeySelective(asrVocabDetailVO);
    }

    @Override
    public PageResultObject<BotVO> getBotList(Long asrVocabId, String botInfo, Integer pageSize, Integer pageNum) {
        BotQuery botQuery = new BotQuery();
        botQuery.setAsrVocabId(asrVocabId);
        botQuery.setName(botInfo);
        botQuery.setPageSize(pageSize);
        botQuery.setPageNum(pageNum);
        return botService.queryListWithoutWrapVO(botQuery);
    }

    @Override
    public void unbindBot(AsrVocabDetailVO asrVocabDetailVO) {
        if (Objects.isNull(asrVocabDetailVO.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "要解绑的bot不能为空");
        }
        if (Objects.isNull(asrVocabDetailVO.getAsrVocabId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "热词组id不能为空");
        }
        botService.unBindAsrVocab(asrVocabDetailVO.getBotId(), asrVocabDetailVO.getAsrVocabId());
    }

    @Override
    public void bindBot(AsrVocabDetailVO asrVocabDetailVO) {
        if (Objects.isNull(asrVocabDetailVO.getAsrVocabId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "热词组id不能为空");
        }
        if (Objects.isNull(asrVocabDetailVO.getBotIdList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "要绑定的bot不能为空");
        }
        botService.batchBindAsrVocab(asrVocabDetailVO.getBotIdList(), asrVocabDetailVO.getAsrVocabId());
    }

    @Override
    public void start(AsrVocabDetailVO asrVocabDetailVO) {
        if (Objects.isNull(asrVocabDetailVO.getAsrVocabId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "热词组id不能为空");
        }
        asrVocabDetailVO.setStatus(1);
        asrVocabDetailVO.setUpdateTime(LocalDateTime.now());
        asrVocabDetailPOMapper.updateByPrimaryKeySelective(asrVocabDetailVO);
    }

    @Override
    public AsrVocabDetailPO getById(Long asrVocabId) {
        return asrVocabDetailPOMapper.selectByPrimaryKey(asrVocabId);
    }

    @Override
    public List<AsrVocabDetailPO> getByName(String name) {
        return asrVocabDetailPOMapper.getByName(name);
    }
}