package com.yiwise.dialogflow.service.impl.asrmodel;

import com.yiwise.dialogflow.entity.po.asrmodel.AsrLanguagePO;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrProviderPO;
import com.yiwise.dialogflow.mapper.AsrLanguagePOMapper;
import com.yiwise.dialogflow.mapper.AsrProviderPOMapper;
import com.yiwise.dialogflow.service.asrmodel.AsrProviderService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/14 15:34:51
 */
@Service
public class AsrProviderServiceImpl implements AsrProviderService {

    @Resource
    private AsrProviderPOMapper asrProviderPOMapper;

    @Resource
    private AsrLanguagePOMapper asrLanguagePOMapper;

    @Override
    public List<AsrProviderPO> list() {
        return asrProviderPOMapper.selectAll();
    }

    @Override
    public AsrProviderPO get(Long asrProviderId) {
        return asrProviderPOMapper.selectByPrimaryKey(asrProviderId);
    }

    @Override
    public List<AsrLanguagePO> languageList() {
        return asrLanguagePOMapper.selectAll();
    }

    @Override
    public AsrProviderPO getByLanguageId(Long asrLanguageId) {
        AsrLanguagePO asrLanguagePO = asrLanguagePOMapper.selectByPrimaryKey(asrLanguageId);
        return asrProviderPOMapper.selectByPrimaryKey(asrLanguagePO.getDefaultProviderId());
    }
}