package com.yiwise.dialogflow.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yiwise.base.common.thread.decorator.MDCDecoratorCallable;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.dto.response.step.SimpleNode;
import com.yiwise.dialogflow.api.dto.response.step.SimpleStep;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.bo.*;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.intent.IntentCorpusPO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.po.intent.RuleActionParam;
import com.yiwise.dialogflow.entity.query.IntentQuery;
import com.yiwise.dialogflow.entity.query.StepQueryVO;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.entity.vo.node.SimpleNodeInfoVO;
import com.yiwise.dialogflow.entity.vo.stats.SimpleStepStatsInfoVO;
import com.yiwise.dialogflow.entity.vo.step.SimpleStepInfoVO;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.StepSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.StepSyncVO;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.entitycollect.EntityService;
import com.yiwise.dialogflow.service.impl.intent.IntentServiceImpl;
import com.yiwise.dialogflow.service.intent.IntentCorpusService;
import com.yiwise.dialogflow.service.intent.IntentService;
import com.yiwise.dialogflow.service.llm.LlmStepConfigService;
import com.yiwise.dialogflow.service.operationlog.StepOperationLogService;
import com.yiwise.dialogflow.service.stats.BotStatsFacadeService;
import com.yiwise.dialogflow.thread.BotSyncThreadExecutorHelper;
import com.yiwise.dialogflow.utils.IdMappingUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class StepServiceImpl implements StepService, RobotResourceService {

    public static final int MAIN_FIRST_ORDER_NUM = 0;

    @Resource
    private MongoTemplate mongoTemplate;

    @Lazy
    @Resource
    private StepNodeService stepNodeService;

    @Resource
    private IntentService intentService;

    @Lazy
    @Resource
    private KnowledgeService knowledgeService;

    @Resource
    private BotResourceSequenceService botResourceSequenceService;

    @Resource
    private DependResourceService dependResourceService;

    @Resource
    private LabelGenerateService labelGenerateService;

    @Lazy
    @Resource
    private BotService botService;

    @Resource
    private SourceRefService sourceRefService;

    @Resource
    private BotStatsFacadeService botStatsFacadeService;

    @Resource
    private VariableService variableService;

    @Resource
    private IntentCorpusService intentCorpusService;

    @Resource
    private AnswerAudioManagerService answerAudioManagerService;

    @Resource
    private AnswerAudioMappingService answerAudioMappingService;

    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    @Resource
    private StepOperationLogService operationLogService;

    @Resource
    private BotSyncOperationLogService botSyncOperationLogService;

    @Resource
    private BotRefService botRefService;

    @Resource
    private EntityService entityService;

    @Resource
    private AlgorithmLabelService algorithmLabelService;

    @Resource
    private LlmStepConfigService llmStepConfigService;

    private static final Pattern MAIN_STEP_LABEL_PATTERN = Pattern.compile("(M-?)([1-9]\\d*)");
    private static final Pattern INDEPENDENT_STEP_LABEL_PATTERN = Pattern.compile("(I-?)([1-9]\\d*)");

    @Override
    public void initOnCreateBot(Long botId) {
        StepPO first = getDefaultTemplate(botId);
        mongoTemplate.insert(first, StepPO.COLLECTION_NAME);
    }

    private StepPO getDefaultTemplate(Long botId) {
        StepPO step = new StepPO();
        step.setOrderNum(MAIN_FIRST_ORDER_NUM);
        step.setName("开场白");
        step.setBotId(botId);
        step.setCategory(StepCategoryEnum.NORMAL);
        step.setType(StepTypeEnum.MAIN);
        step.setTriggerByIntent(false);
        step.setTriggerIntentIdList(Collections.emptyList());
        step.setIsCustomerConcern(false);
        generateLabel(botId, step);
        return step;
    }


    private void generateLabel(Long botId, StepPO step) {
        if (Objects.isNull(step)) {
            return;
        }
        if (StepTypeEnum.MAIN.equals(step.getType())) {
            labelGenerateService.mainStepLabel(step.getBotId(), Collections.singletonList(step));
        } else {
            labelGenerateService.independentStepLabel(step.getBotId(), Collections.singletonList(step));
        }
    }

    private void updateBotToDraft(Long botId) {
        botService.updateAuditStatus(botId, AuditStatusEnum.DRAFT);
    }

    @Override
    public StepVO create(StepPO form, Long userId) {
        return doCreate(form, userId, false);
    }

    private StepVO doCreate(StepPO form, Long userId, boolean isSync) {
        validParam(form, true, isSync);
        generateOrderNumber(form);

        if (StringUtils.isNotBlank(form.getLabel())) {
            validateLabelAndUpdateSeq(form);
        }
        form.setCreateTime(LocalDateTime.now());
        form.setUpdateTime(LocalDateTime.now());
        generateLabel(form.getBotId(), form);
        form.setCreateUserId(userId);
        form.setUpdateUserId(userId);
        mongoTemplate.insert(form, StepPO.COLLECTION_NAME);
        updateBotToDraft(form.getBotId());
        updateDependRef(form);
        operationLogService.compareAndCreateOperationLog(form.getBotId(), null, form, userId);
        return convertPO2VO(form);
    }

    private boolean labelExists(Long botId, String label) {
        return mongoTemplate.exists(Query.query(Criteria.where("botId").is(botId).and("label").is(label)), StepPO.COLLECTION_NAME);
    }

    private void validateLabelAndUpdateSeq(StepPO form) {
        String newLabel = form.getLabel();
        boolean isMainStep = StepTypeEnum.MAIN.equals(form.getType());
        Matcher matcher = (isMainStep ? MAIN_STEP_LABEL_PATTERN : INDEPENDENT_STEP_LABEL_PATTERN).matcher(newLabel);
        if (!matcher.find()) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("流程ID【%s】格式错误", newLabel));
        }
        if (labelExists(form.getBotId(), newLabel)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("流程ID【%s】已存在", newLabel));
        }
        Integer seq = Integer.parseInt(matcher.group(2));
        if (isMainStep) {
            labelGenerateService.updateMainStepSeqIfNeed(form.getBotId(), seq);
        } else {
            labelGenerateService.updateIndependentStepSeqIfNeed(form.getBotId(), seq);
        }
    }

    @Override
    public StepVO update(StepPO form, Long userId) {
        if (StringUtils.isBlank(form.getId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程id不能为空");
        }
        StepPO old = getPOById(form.getBotId(), form.getId());
        if (Objects.isNull(old)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "流程不存在");
        }

        validParam(form, false, false);

        // 主流程只能通过reorder改变orderNum字段,
        if (!old.getType().equals(form.getType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程类型不能变更");
        }
        if (!old.getSubType().equals(form.getSubType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程类别不能变更");
        }
        if (old.isLlmStep() && !old.getLlmStepType().equals(form.getLlmStepType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程任务类型不能变更");
        }
        if (StepTypeEnum.MAIN.equals(old.getType())) {
            form.setOrderNum(old.getOrderNum());
        }

        String newLabel = form.getLabel();
        if (StringUtils.isBlank(newLabel)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "标签不能为空");
        }
        if (!StringUtils.equals(newLabel, old.getLabel())) {
            validateLabelAndUpdateSeq(form);
            stepNodeService.updateAllNodeLabelPrefix(form.getBotId(), form.getId(), old.getLabel(), newLabel);
            labelGenerateService.renameNodeLabelPrefix(form.getBotId(), old.getLabel(), newLabel);
        }

        generateOrderNumber(form);
        form.setUpdateTime(LocalDateTime.now());
        generateLabel(form.getBotId(), form);
        form.setUpdateUserId(userId);
        mongoTemplate.save(form, StepPO.COLLECTION_NAME);
        updateBotToDraft(form.getBotId());
        updateDependRef(form);
        operationLogService.compareAndCreateOperationLog(form.getBotId(), old, form, userId);
        return convertPO2VO(form);
    }

    private void generateOrderNumber(StepPO step) {
        if (Objects.isNull(step.getOrderNum())) {
            if (StepTypeEnum.MAIN.equals(step.getType())) {
                step.setOrderNum(generateOrderNum(step.getBotId()));
            } else {
                step.setOrderNum(Integer.MAX_VALUE);
            }
        }
    }

    private List<StepVO> convertPO2VO(Long botId, List<StepPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }
        List<IntentPO> intentList = intentService.getAllByBotId(botId);
        Map<String, IntentPO> intentMap = MyCollectionUtils.listToMap(intentList, IntentPO::getId);
        return poList.stream()
                .map(po -> {
                    StepVO vo = MyBeanUtils.copy(po, StepVO.class);
                    if (BooleanUtils.isTrue(vo.getTriggerByIntent()) && CollectionUtils.isNotEmpty(vo.getTriggerIntentIdList())) {
                        List<SimpleIntentVO> intentInfoList = vo.getTriggerIntentIdList()
                                .stream()
                                .filter(intentMap::containsKey)
                                .map(id -> SimpleIntentVO.createFrom(intentMap.get(id)))
                                .collect(Collectors.toList());
                        vo.setIntentInfoList(intentInfoList);
                    } else {
                        vo.setIntentInfoList(Collections.emptyList());
                    }
                    return vo;
                }).collect(Collectors.toList());
    }

    private StepVO convertPO2VO(StepPO po) {
        if (Objects.isNull(po)) {
            return null;
        }
        return convertPO2VO(po.getBotId(), Collections.singletonList(po)).get(0);
    }

    @Override
    public void validAndThrow(StepPO step) {
        if (Objects.isNull(step.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "机器人id不能为空");
        }
        if (Objects.isNull(step.getType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程类型不能为空");
        }
        if (Objects.isNull(step.getCategory())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程类别不能为空");
        }
        if (StringUtils.isBlank(step.getName())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程名称不能为空");
        }
    }

    @Override
    public void validAndThrow(StepPO step, DependentResourceBO dependentResource) {
        validAndThrow(step, dependentResource, false);
    }

    @Override
    public void validAndThrow(StepPO step, DependentResourceBO dependentResource, boolean isSync) {
        validAndThrow(step);
        if (BooleanUtils.isTrue(step.getTriggerByIntent())) {
            if (!StepSubTypeEnum.isLlm(step.getSubType()) || StepTypeEnum.MAIN.equals(step.getType())) {
                if (MapUtils.isEmpty(dependentResource.getIntentIdNameMap())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "依赖的触发意图不存在");
                }
                if (CollectionUtils.isEmpty(step.getTriggerIntentIdList())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "触发意图不能为空");
                }
            }
            step.getTriggerIntentIdList().forEach(intentId -> {
                if (!dependentResource.getIntentIdNameMap().containsKey(intentId)) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("%s依赖的触发意图不存在, 意图id=[%s]", step.getName(), intentId));
                }
            });
        }

        if (BooleanUtils.isTrue(step.getEnableSkipCondition())) {
            if (!StepTypeEnum.MAIN.equals(step.getType())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "只有主流程才能开启流程过滤条件");
            }

            if (Objects.isNull(step.getSkipCondition()) || CollectionUtils.isEmpty(step.getSkipCondition().getConditionList())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程过滤条件不能为空");
            }

            step.getSkipCondition().validateWithResource(dependentResource);
        }

        // 校验最后一个主流程不能开启流程过滤条件
        if (StepTypeEnum.MAIN.equals(step.getType()) && BooleanUtils.isTrue(step.getEnableSkipCondition())) {
            StepPO lastStep = null;
            if (CollectionUtils.isNotEmpty(dependentResource.getStepMap().values())) {
                lastStep = dependentResource.getStepMap().values().stream()
                        .filter(item -> StepTypeEnum.MAIN.equals(item.getType()))
                        .max(Comparator.comparing(StepPO::getOrderNum))
                        .orElse(null);
            }
            if (Objects.equals(lastStep, step)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "不支持最后一个对话流配置“流程跳过设置”");
            }
        }

        if (step.isLlmStep() && Objects.isNull(step.getLlmStepType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "大模型流程任务类型不能为空");
        }
        if (!isSync && step.isLlmStep() && StepTypeEnum.INDEPENDENT.equals(step.getType()) && StringUtils.isBlank(step.getDesc())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程描述不能为空");
        }
        if (step.isLlmStep() && StringUtils.isBlank(step.getLlmModelName())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "模型名称不能为空");
        }
    }

    @Override
    public StepPO deleteById(Long botId, String id, Long userId) {
        StepPO old = getPOById(botId, id);
        if (Objects.isNull(old)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "流程不存在, 删除失败");
        }
        batchDelete(botId, Collections.singletonList(old), userId);
        return old;
    }

    private void batchDelete(Long botId, List<StepPO> stepList, Long userId) {
        if (CollectionUtils.isEmpty(stepList)) {
            return;
        }
        for (StepPO step : stepList) {
            if (StepTypeEnum.MAIN.equals(step.getType()) && step.getOrderNum() <= MAIN_FIRST_ORDER_NUM) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("流程%s为开场白流程不能删除", step.getName()));
            }
        }

        Map<String, String> id2nameMap = MyCollectionUtils.listToConvertMap(stepList, StepPO::getId, StepPO::getName);
        List<String> stepIdList = stepList.stream().map(StepPO::getId).collect(Collectors.toList());

        // 流程只能被流程的跳转节点或者问答知识设置的重复主流程所引用
        stepNodeService.getByDependStepIdList(botId, stepIdList).stream()
                .map(tuple -> {
                    DialogBaseNodePO node = tuple._1();
                    List<String> stepNameList = tuple._2().stream().map(id2nameMap::get).collect(Collectors.toList());
                    return String.format("节点[%s:%s]依赖流程[%s], 无法删除",
                            node.getLabel(), node.getName(), String.join(", ", stepNameList));
                }).reduce((s1, s2) -> s1 + "\n" + s2)
                .ifPresent(msg -> {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, msg);
                });

        knowledgeService.getByDependStepIdList(botId, stepIdList).stream()
                        .map(tuple -> {
                            KnowledgePO knowledge = tuple._1();
                            List<String> stepNameList = tuple._2().stream().map(id2nameMap::get).collect(Collectors.toList());
                            return String.format("问答知识[%s:%s]依赖流程[%s], 无法删除",
                                    knowledge.getLabel(), knowledge.getName(), String.join(", ", stepNameList));
                        }).reduce((s1, s2) -> s1 + "\n" + s2)
                        .ifPresent(msg -> {
                            throw new ComException(ComErrorCode.VALIDATE_ERROR, msg);
                        });

        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("_id").is(stepIdList));
        mongoTemplate.remove(query, StepPO.COLLECTION_NAME);
        stepNodeService.deleteByStepIdList(botId, stepIdList);
        llmStepConfigService.deleteByStepIdList(botId, stepIdList);
        updateBotToDraft(botId);
        sourceRefService.deleteSourceByRefIds(botId, stepIdList);
        operationLogService.createDeleteOperationLog(botId, stepList, userId);
    }

    // 准备流程依赖的资源
    private DependentResourceBO prepareDependResource(Long botId) {
        return dependResourceService.generateByCondition(new DependentResourceBO.Condition(botId).intent().variable());
    }

    @Override
    public StepVO getById(Long botId, String id) {
        return convertPO2VO(getPOById(botId, id));
    }

    @Override
    public StepPO getPOById(Long botId, String stepId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("_id").is(stepId));
        return mongoTemplate.findOne(query, StepPO.class, StepPO.COLLECTION_NAME);
    }

    @Override
    public List<StepPO> getAllListByBotId(Long botId) {
        return queryListByBotIdAndType(botId, null);
    }

    @Override
    public List<StepVO> queryByCondition(StepQueryVO condition) {
        Long botId = condition.getBotId();
        StepTypeEnum type = condition.getType();
        List<StepVO> result = convertPO2VO(botId, queryListByBotIdAndType(botId, type));
        botStatsFacadeService.wrapStepStatsInfo(result, condition);
        return result;
    }

    @Override
    public List<StepPO> getMainListByBotId(Long botId) {
        return queryListByBotIdAndType(botId, StepTypeEnum.MAIN);
    }

    @Override
    public List<StepPO> getIndependentListByBotId(Long botId) {
        return queryListByBotIdAndType(botId, StepTypeEnum.INDEPENDENT);
    }

    @Override
    public List<StepVO> mainStepSort(Long botId, List<String> stepIdList) {
        if (CollectionUtils.isEmpty(stepIdList)) {
            return Collections.emptyList();
        }
        int orderNum = MAIN_FIRST_ORDER_NUM;
        for (String s : stepIdList) {
            Query query = new Query();
            query.addCriteria(Criteria.where("botId").is(botId))
                    .addCriteria(Criteria.where("_id").is(s));
            Update update = new Update();
            update.set("orderNum", orderNum++);
            mongoTemplate.updateFirst(query, update, StepPO.COLLECTION_NAME);
        }
        updateBotToDraft(botId);
        return convertPO2VO(botId, getMainListByBotId(botId));
    }

    @Override
    public StepPO copyStepInBot(Long botId, String sourceStepId, String targetName, StepTypeEnum targetStepType, Long userId) {
        StepPO sourceStep = getPOById(botId, sourceStepId);
        if (Objects.isNull(sourceStep)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "流程不存在, 复制失败");
        }
        if (StringUtils.isBlank(targetName)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程名称不能为空");
        }
        // todo copy properties
        StepPO targetStep = new StepPO();
        targetStep.setBotId(botId);
        targetStep.setName(targetName);
        targetStep.setType(targetStepType);
        targetStep.setCategory(sourceStep.getCategory());
        targetStep.setTriggerByIntent(sourceStep.getTriggerByIntent());
        targetStep.setIsCustomerConcern(sourceStep.getIsCustomerConcern());
        targetStep.setLlmModelName(sourceStep.getLlmModelName());

        if (StepTypeEnum.INDEPENDENT.equals(sourceStep.getType())) {
            targetStep.setTriggerByIntent(true);
        }
        if (StepTypeEnum.MAIN.equals(targetStepType)) {
            targetStep.setEnableSkipCondition(sourceStep.getEnableSkipCondition());
            targetStep.setSkipCondition(sourceStep.getSkipCondition());
        } else {
            targetStep.setEnableSkipCondition(false);
            targetStep.setSkipCondition(null);
        }
        targetStep.setSubType(sourceStep.getSubType());
        targetStep.setLlmStepType(sourceStep.getLlmStepType());
        targetStep.setDesc(sourceStep.getDesc());
        checkNameDuplicate(targetStep.getBotId(), targetStep.getName(), targetStep.getId());
        generateOrderNumber(targetStep);
        targetStep.setCreateTime(LocalDateTime.now());
        targetStep.setUpdateTime(LocalDateTime.now());
        generateLabel(botId, targetStep);
        mongoTemplate.insert(targetStep, StepPO.COLLECTION_NAME);
        if (targetStep.isLlmStep()) {
            llmStepConfigService.copyInBot(botId, sourceStepId, targetStep);
        } else {
            stepNodeService.copyStepNodeInBot(botId, sourceStepId, targetStep.getId());
        }
        updateDependRef(targetStep);
        operationLogService.copyStepInBot(botId, sourceStep, targetStep, userId);
        updateBotToDraft(botId);
        return targetStep;
    }

    @Override
    public Optional<StepPO> getRootStep(List<StepPO> stepList) {
        if (CollectionUtils.isEmpty(stepList)) {
            return Optional.empty();
        }
        for (StepPO step : stepList) {
            if (StepTypeEnum.MAIN.equals(step.getType()) && Integer.valueOf(0).equals(step.getOrderNum())) {
                return Optional.of(step);
            }
        }
        return Optional.empty();
    }

    @Override
    public Optional<StepPO> getLastMainStep(List<StepPO> stepList) {
        if (CollectionUtils.isEmpty(stepList)) {
            return Optional.empty();
        }
        return stepList.stream()
                .filter(step -> StepTypeEnum.MAIN.equals(step.getType()))
                .max(Comparator.comparing(StepPO::getOrderNum));
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        List<StepPO> stepPOList = getAllListByBotId(context.getSrcBotId());
        context.getSnapshot().setStepList(stepPOList);
    }

    @Override
    public void validateResource(RobotResourceContext context) {
        List<StepPO> stepList = context.getSnapshot().getStepList();
        if (CollectionUtils.isEmpty(stepList)) {
            SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg.builder()
                    .failMsg("流程不应该为空")
                    .resourceType(BotResourceTypeEnum.TIP)
                    .build();
            context.getInvalidMsgList().add(msg);
            return;
        }
        List<String> existsLabelList = new ArrayList<>(stepList.size());
        // 先校验流程
        stepList.forEach(step -> {
            try {
                validAndThrow(step, context.getDependentResource());
                if (existsLabelList.contains(step.getLabel())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("ID[%s]重复", step.getLabel()));
                }
                existsLabelList.add(step.getLabel());
            } catch (ComException e) {
                String failMsg = String.format("流程[%s] %s", step.getName(), e.getMessage());
                BotResourceTypeEnum resourceType = BotResourceTypeEnum.MAIN_STEP;
                if (StepTypeEnum.INDEPENDENT.equals(step.getType())) {
                    resourceType = BotResourceTypeEnum.INDEPENDENT_STEP;
                }
                SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg.builder()
                        .failMsg(failMsg)
                        .resourceType(resourceType)
                        .resourceId(step.getId())
                        .resourceName(step.getName())
                        .resourceLabel(step.getLabel())
                        .build();
                context.getInvalidMsgList().add(msg);
            }
        });
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        Long targetBotId = context.getTargetBotId();
        Long currentUserId = context.getCurrentUserId();
        ResourceCopyReferenceMappingBO mapping = context.getResourceCopyReferenceMapping();
        Map<String, String> intentIdMapping = mapping.getIntentIdMapping();
        List<StepPO> stepList = context.getSnapshot().getStepList();

        if (context.isCopy()) {
            Map<String, String> stepIdMapping = mapping.getStepIdMapping();
            stepList.forEach(stepPO -> {
                String oldId = stepPO.getId();
                String newId = new ObjectId().toString();
                stepIdMapping.put(oldId, newId);
            });

            for (StepPO step : stepList) {
                step.setId(stepIdMapping.get(step.getId()));
                step.setBotId(targetBotId);
                step.setCreateTime(LocalDateTime.now());
                step.setUpdateTime(LocalDateTime.now());
                step.setCreateUserId(currentUserId);
                step.setUpdateUserId(currentUserId);

                // 更新引用的意图信息
                if (BooleanUtils.isTrue(step.getTriggerByIntent()) && CollectionUtils.isNotEmpty(step.getTriggerIntentIdList())) {
                    List<String> newIntentIdList = step.getTriggerIntentIdList().stream()
                            .map(intentIdMapping::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    step.setTriggerIntentIdList(newIntentIdList);
                } else {
                    step.setTriggerIntentIdList(Collections.emptyList());
                }

                // 更新引用的变量信息
                if (BooleanUtils.isTrue(step.getEnableSkipCondition())
                        && Objects.nonNull(step.getSkipCondition())) {
                    mapping.mapConditionGroupVarId(step.getSkipCondition());
                }

                generateLabel(targetBotId, step);
            }
        }
        mongoTemplate.insert(stepList, StepPO.COLLECTION_NAME);
    }

    @Override
    public List<Class<? extends RobotResourceService>> dependsOn() {
        return Lists.newArrayList(IntentServiceImpl.class, VariableServiceImpl.class);
    }

    private void updateDependRef(StepPO step) {
        // 先删除, 再添加
        sourceRefService.deleteSourceByRefId(step.getBotId(), step.getId());
        if (BooleanUtils.isTrue(step.getTriggerByIntent())
                && CollectionUtils.isNotEmpty(step.getTriggerIntentIdList())) {
            sourceRefService.saveSourceRef(buildIntentRefParam(step));
        }
        if (BooleanUtils.isTrue(step.getEnableSkipCondition())
                && Objects.nonNull(step.getSkipCondition())
                && CollectionUtils.isNotEmpty(step.getSkipCondition().getConditionList())) {
            Set<String> variableIdSet = step.getSkipCondition().calDependVariableIdSet();
            if (CollectionUtils.isNotEmpty(variableIdSet)) {
                sourceRefService.saveSourceRef(buildVariableRefParam(step, variableIdSet));
            }
        }
    }

    private SourceRefBO buildIntentRefParam(StepPO step) {
        SourceRefBO sourceRefBO = new SourceRefBO();
        sourceRefBO.setBotId(step.getBotId());
        sourceRefBO.setSourceType(SourceTypeEnum.INTENT);
        Set<String> intentIdSet = new HashSet<>(step.getTriggerIntentIdList());
        sourceRefBO.setSourceIdSet(intentIdSet);
        sourceRefBO.setRefId(step.getId());
        sourceRefBO.setRefType(IntentRefTypeEnum.STEP);
        sourceRefBO.setRefLabel(step.getLabel());
        return sourceRefBO;
    }

    private SourceRefBO buildVariableRefParam(StepPO step, Set<String> varIdSet) {
        SourceRefBO sourceRefBO = new SourceRefBO();
        sourceRefBO.setBotId(step.getBotId());
        sourceRefBO.setSourceType(SourceTypeEnum.VARIABLE);
        sourceRefBO.setSourceIdSet(varIdSet);
        sourceRefBO.setRefId(step.getId());
        sourceRefBO.setRefType(IntentRefTypeEnum.STEP);
        sourceRefBO.setRefLabel(step.getLabel());
        return sourceRefBO;
    }

    @Override
    public Map<String, String> getNameByIdList(Collection<String> stepIdList) {
        return MyCollectionUtils.listToConvertMap(getStepListByIdList(stepIdList), StepPO::getId, StepPO::getName);
    }

    @Override
    public String getNameById(String stepId) {
        if (StringUtils.isBlank(stepId)) {
            return null;
        }
        return getNameByIdList(Collections.singletonList(stepId)).get(stepId);
    }

    @Override
    public List<IdNamePair<String, String>> getIdNamePairByBotId(Long botId) {
        return getAllListByBotId(botId).stream()
                .map(step -> new IdNamePair<>(step.getId(), step.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public void updateStepLabel(Long botId, String id, String label) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId).and("_id").is(id));
        Update update = new Update();
        update.set("label", label);
        mongoTemplate.updateFirst(query, update, StepPO.COLLECTION_NAME);
    }

    @Override
    public List<SimpleStepInfoVO> getSimpleStepInfo(Long botId, StepTypeEnum type) {
        List<SimpleStepInfoVO> stepList = getAllListByBotId(botId)
                .stream()
                .filter(item -> type == null || item.getType().equals(type))
                .map(item -> MyBeanUtils.copy(item, SimpleStepInfoVO.class))
                .collect(Collectors.toList());
        List<String> stepIdList = stepList.stream().map(SimpleStepInfoVO::getId).collect(Collectors.toList());
        List<SimpleNodeInfoVO> nodeList = stepNodeService.getSimpleInfoList(botId, stepIdList);

        Map<String, List<SimpleNodeInfoVO>> stepNodeListMap = MyCollectionUtils.listToMapList(nodeList, SimpleNodeInfoVO::getStepId);

        stepList.forEach(step -> {
            List<SimpleNodeInfoVO> stepNodeList = stepNodeListMap.getOrDefault(step.getId(), Collections.emptyList());
            step.setNodeList(stepNodeList);
        });

        return stepList;
    }

    @Override
    public List<StepPO> getAllStepList() {
        return mongoTemplate.findAll(StepPO.class);
    }

    @Override
    public List<SimpleStepStatsInfoVO> getAllStepStatsInfo(StepQueryVO condition) {
        condition.setWrapCustomerHangupStats(true);
        return queryByCondition(condition).stream()
                .map(vo -> MyBeanUtils.copy(vo, SimpleStepStatsInfoVO.class))
                .collect(Collectors.toList());
    }

    /**
     * 流程同步
     *
     * @return
     */
    @Override
    public StepSyncResultVO sync(StepSyncVO syncVO) {
        Long srcBotId = syncVO.getSrcBotId();
        List<String> targetIdList = syncVO.getTargetIdList();
        List<Long> targetBotIdList = syncVO.getTargetBotIdList();
        Assert.notEmpty(targetBotIdList, "目标Bot不能为空");
        Assert.isTrue(!targetBotIdList.contains(srcBotId), "目标bot不能包含自己");
        Assert.notEmpty(targetIdList, "流程ID不能为空");
        SystemEnum systemType = syncVO.getSystemType();

        StepSyncResultVO result = new StepSyncResultVO();
        Map<Long, List<String>> failBotMap = new HashMap<>();

        for (String stepId : targetIdList) {
            StepVO step = getById(srcBotId, stepId);
            if (Objects.nonNull(step)) {
                log.info("开始同步流程:{}", step.getName());
                BotSyncResultVO tmpResult = syncOneStep(syncVO, srcBotId, step, systemType, targetBotIdList);
                if (CollectionUtils.isNotEmpty(tmpResult.getFailBotIdList())) {
                    tmpResult.getFailBotIdList().forEach(failBotId -> failBotMap.computeIfAbsent(failBotId, k -> new ArrayList<>()).add(step.getLabel()));
                }
                log.info("{}流程同步完成", step.getName());
            }
        }
        result.setFailBotMap(failBotMap);
        result.setFailNum(failBotMap.size());
        result.setSuccessNum(targetBotIdList.size() - failBotMap.size());
        return result;
    }

    private BotSyncResultVO syncOneStep(StepSyncVO syncVO,
                                        Long srcBotId,
                                        StepVO stepVO,
                                        SystemEnum systemType,
                                        List<Long> targetBotIdList) {

        Set<String> triggerIntentIdSet = Sets.newHashSet(stepVO.getTriggerIntentIdList());

        List<IntentPO> allIntentList = intentService.getAllByBotId(srcBotId);
        Map<String, IntentPO> intentMap = MyCollectionUtils.listToMap(allIntentList, IntentPO::getId);

        Set<String> tmpIntentIdSet = new HashSet<>();
        for (String intentId : triggerIntentIdSet) {
            IntentPO intent = intentMap.get(intentId);
            if (Objects.nonNull(intent)
                    && IntentTypeEnum.COMPOSITE.equals(intent.getIntentType())
                    && CollectionUtils.isNotEmpty(intent.getCompositeConditionList())) {
                for (CompositeIntentCondition condition : intent.getCompositeConditionList()) {
                    if (CollectionUtils.isNotEmpty(condition.getIntentIdList())) {
                        tmpIntentIdSet.addAll(condition.getIntentIdList());
                    }
                }
            }
        }
        triggerIntentIdSet.addAll(tmpIntentIdSet);

        List<IntentVO> triggerIntentVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(triggerIntentIdSet)) {
            triggerIntentVOList = intentService.listWithoutPages(IntentQuery.builder().intentIdList(triggerIntentIdSet).build());
        }

        List<IntentCorpusPO> triggerIntentCorpusPOList = intentCorpusService.findByIntentIdIn(triggerIntentIdSet);

        BotVO srcBot = botService.detail(srcBotId);
        Long intentLevelTagId = srcBot.getIntentLevelTagId();

        Set<String> nodeIntentIdSet = Sets.newHashSet();
        Set<String> dependVariableIdSet = Sets.newHashSet();
        Set<String> stepIdSet = Sets.newHashSet();
        Set<String> knowledgeIdSet = Sets.newHashSet();
        Set<String> entityIdSet = Sets.newHashSet();
        List<DialogBaseNodePO> nodePOList= new ArrayList<>();
        LlmStepConfigPO llmStepConfig;
        DependentResourceBO srcDependResource = prepareDependResource(srcBotId);
        Set<String> specialAnswerConfigIdSet = Sets.newHashSet();
        if (!stepVO.isLlmStep()) {
            llmStepConfig = null;
            nodePOList.addAll(stepNodeService.getListByStepId(srcBotId, stepVO.getId()));
            nodePOList.forEach(dialogBaseNodePO -> {
                if (NodeTypeEnum.CHAT.equals(dialogBaseNodePO.getType()) || NodeTypeEnum.QUERY.equals(dialogBaseNodePO.getType())) {
                    DialogChatNodePO chatNodePO = (DialogChatNodePO) dialogBaseNodePO;
                    List<String> selectIntentIdList = chatNodePO.getSelectIntentIdList();
                    nodeIntentIdSet.addAll(selectIntentIdList);
                }

                if (NodeTypeEnum.JUDGE.equals(dialogBaseNodePO.getType())) {
                    DialogJudgeNodePO judgeNode = (DialogJudgeNodePO) dialogBaseNodePO;
                    nodeIntentIdSet.addAll(judgeNode.calBranchDependIntentIdSet());
                }

                if (NodeTypeEnum.COLLECT.equals(dialogBaseNodePO.getType())) {
                    DialogCollectNodePO collectNode = (DialogCollectNodePO) dialogBaseNodePO;
                    nodeIntentIdSet.addAll(collectNode.getSelectIntentIdList());
                    if (BooleanUtils.isTrue(collectNode.getEnableSkipCondition())
                            && CollectionUtils.isNotEmpty(collectNode.getSkipConditionList())) {
                        for (CollectNodeSkipCondition condition : collectNode.getSkipConditionList()) {
                            if (CollectionUtils.isNotEmpty(condition.getIntentIdList())) {
                                nodeIntentIdSet.addAll(condition.getIntentIdList());
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(collectNode.getEntityCollectList())) {
                        entityIdSet.addAll(
                                collectNode.getEntityCollectList().stream()
                                        .map(CollectNodeEntityItemPO::getEntityId)
                                        .filter(StringUtils::isNotBlank)
                                        .collect(Collectors.toList())
                        );
                    }
                }

                if (BooleanUtils.isTrue(dialogBaseNodePO.getEnableAssign()) && Objects.nonNull(dialogBaseNodePO.getOriginInputAssign())) {
                    nodeIntentIdSet.addAll(dialogBaseNodePO.getOriginInputAssign().getDependIntentIdList());
                }

                if (BooleanUtils.isTrue(dialogBaseNodePO.getEnableAssign())
                        && Objects.nonNull(dialogBaseNodePO.getEntityAssign())) {
                    entityIdSet.addAll(dialogBaseNodePO.getEntityAssign().getDependentEntityIdList());
                }

                // 节点话术，处理变量
                dependVariableIdSet.addAll(dialogBaseNodePO.calDependVariableIdSet(srcDependResource));

                // 触发动作，OPE同步时清空
                List<RuleActionParam> actionList = dialogBaseNodePO.getActionList();
                if (CollectionUtils.isNotEmpty(actionList) && SystemEnum.isOPE(systemType)) {
                    for (RuleActionParam ruleActionParam : actionList) {
                        ruleActionParam.setSourceIdList(Lists.newArrayList());
                    }
                }

                // 不关联问答知识/流程
                if (BooleanUtils.isTrue(dialogBaseNodePO.getMismatchKnowledgeAndStep())) {
                    // 全选不用做特殊处理
                    if (CollectionUtils.isNotEmpty(dialogBaseNodePO.getMismatchStepIdList())) {
                        stepIdSet.addAll(dialogBaseNodePO.getMismatchStepIdList());
                    }
                    if (CollectionUtils.isNotEmpty(dialogBaseNodePO.getMismatchKnowledgeIdList())) {
                        knowledgeIdSet.addAll(dialogBaseNodePO.getMismatchKnowledgeIdList());
                    }
                    if (CollectionUtils.isNotEmpty(dialogBaseNodePO.getMismatchSpecialAnswerConfigIdList())) {
                        specialAnswerConfigIdSet.addAll(dialogBaseNodePO.getMismatchSpecialAnswerConfigIdList());
                    }
                }
                if (CollectionUtils.isNotEmpty(dialogBaseNodePO.getUninterruptedReplyStepIdList())) {
                    stepIdSet.addAll(dialogBaseNodePO.getUninterruptedReplyStepIdList());
                }
                if (CollectionUtils.isNotEmpty(dialogBaseNodePO.getUninterruptedReplyKnowledgeIdList())) {
                    knowledgeIdSet.addAll(dialogBaseNodePO.getUninterruptedReplyKnowledgeIdList());
                }
                if (CollectionUtils.isNotEmpty(dialogBaseNodePO.getUninterruptedReplySpecialAnswerIdList())) {
                    specialAnswerConfigIdSet.addAll(dialogBaseNodePO.getUninterruptedReplySpecialAnswerIdList());
                }


                // 跳转至指定流程
                if (NodeTypeEnum.JUMP.equals(dialogBaseNodePO.getType())) {
                    DialogJumpNodePO jumpNodePO = (DialogJumpNodePO) dialogBaseNodePO;
                    if (StringUtils.isNotEmpty(jumpNodePO.getJumpStepId())) {
                        stepIdSet.add(jumpNodePO.getJumpStepId());
                    }
                }
            });
        } else {
            llmStepConfig = llmStepConfigService.getByStepId(srcBotId, stepVO.getId());
            if (Objects.nonNull(llmStepConfig)) {
                // 不关联问答知识/流程
                if (BooleanUtils.isTrue(llmStepConfig.getMismatchKnowledgeAndStep())) {
                    // 全选不用做特殊处理
                    if (CollectionUtils.isNotEmpty(llmStepConfig.getMismatchStepIdList())) {
                        stepIdSet.addAll(llmStepConfig.getMismatchStepIdList());
                    }
                    if (CollectionUtils.isNotEmpty(llmStepConfig.getMismatchKnowledgeIdList())) {
                        knowledgeIdSet.addAll(llmStepConfig.getMismatchKnowledgeIdList());
                    }
                    if (CollectionUtils.isNotEmpty(llmStepConfig.getMismatchSpecialAnswerConfigIdList())) {
                        specialAnswerConfigIdSet.addAll(llmStepConfig.getMismatchSpecialAnswerConfigIdList());
                    }
                }
                if (CollectionUtils.isNotEmpty(llmStepConfig.getUninterruptedReplyStepIdList())) {
                    stepIdSet.addAll(llmStepConfig.getUninterruptedReplyStepIdList());
                }
                if (CollectionUtils.isNotEmpty(llmStepConfig.getUninterruptedReplyKnowledgeIdList())) {
                    knowledgeIdSet.addAll(llmStepConfig.getUninterruptedReplyKnowledgeIdList());
                }
                if (CollectionUtils.isNotEmpty(llmStepConfig.getUninterruptedReplySpecialAnswerIdList())) {
                    specialAnswerConfigIdSet.addAll(llmStepConfig.getUninterruptedReplySpecialAnswerIdList());
                }
                dependVariableIdSet.addAll(llmStepConfig.calDependVariableIdSet(srcDependResource));
            }
        }

        // 流程跳过条件
        if (BooleanUtils.isTrue(stepVO.getEnableSkipCondition()) && Objects.nonNull(stepVO.getSkipCondition())) {
            dependVariableIdSet.addAll(stepVO.getSkipCondition().calDependVariableIdSet());
        }

        tmpIntentIdSet.clear();
        for (String intentId : nodeIntentIdSet) {
            IntentPO intent = intentMap.get(intentId);
            if (Objects.nonNull(intent)
                    && IntentTypeEnum.COMPOSITE.equals(intent.getIntentType())
                    && CollectionUtils.isNotEmpty(intent.getCompositeConditionList())) {
                for (CompositeIntentCondition condition : intent.getCompositeConditionList()) {
                    if (CollectionUtils.isNotEmpty(condition.getIntentIdList())) {
                        tmpIntentIdSet.addAll(condition.getIntentIdList());
                    }
                }
            }
        }
        nodeIntentIdSet.addAll(tmpIntentIdSet);
        Set<String> finalNodeIntentIdSet = nodeIntentIdSet.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        List<IntentVO> nodeIntentVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(finalNodeIntentIdSet)) {
            nodeIntentVOList = intentService.listWithoutPages(IntentQuery.builder().intentIdList(finalNodeIntentIdSet).build());
        }
        List<IntentCorpusPO> nodeIntentCorpusPOList = intentCorpusService.findByIntentIdIn(finalNodeIntentIdSet);

        Map<String, String> stepNameIdMapping = MyCollectionUtils.listToConvertMap(getStepListByIdList(stepIdSet), StepPO::getName, StepPO::getId);
        Map<String, String> knowledgeNameIdMapping = MyCollectionUtils.listToConvertMap(knowledgeService.getByIdList(knowledgeIdSet), KnowledgePO::getName, KnowledgePO::getId);
        Map<String, String> specialAnswerConfigNameIdMapping = MyCollectionUtils.listToConvertMap(specialAnswerConfigService.getByBotId(srcBotId), SpecialAnswerConfigPO::getName, SpecialAnswerConfigPO::getId);
        List<VariablePO> variablePOList = variableService.getByIdList(dependVariableIdSet);
        List<BaseEntityPO> entityList = entityService.getByIdList(entityIdSet);

        List<String> nodeIdList = MyCollectionUtils.listToConvertList(nodePOList, DialogBaseNodePO::getId);
        List<AlgorithmLabelPO> algorithmLabelList = algorithmLabelService.listByNodeIdList(nodeIdList);

        AtomicInteger successNum = new AtomicInteger(0);
        AtomicInteger failNum = new AtomicInteger(0);
        List<Long> successBotIdList = new ArrayList<>();
        try {
            List<Future<Long>> futureList = Lists.newArrayList();
            for (Long botId : targetBotIdList) {
                List<IntentVO> finalTriggerIntentVOList = triggerIntentVOList;
                List<IntentVO> finalNodeIntentVOList = nodeIntentVOList;
                Future<Long> future = BotSyncThreadExecutorHelper.submit("流程同步", new MDCDecoratorCallable<>(() -> {
                    StepSyncDTO stepSyncDTO = StepSyncDTO.builder()
                            .targetBotId(botId)
                            .intentLevelTagId(intentLevelTagId)
                            .syncVO(syncVO)
                            .stepPO(stepVO)
                            .nodeList(nodePOList)
                            .triggerIntentPOList(finalTriggerIntentVOList)
                            .triggerIntentCorpusPOList(triggerIntentCorpusPOList)
                            .nodeIntentPOList(finalNodeIntentVOList)
                            .nodeIntentCorpusPOList(nodeIntentCorpusPOList)
                            .variablePOList(variablePOList)
                            .stepNameIdMapping(stepNameIdMapping)
                            .knowledgeNameIdMapping(knowledgeNameIdMapping)
                            .specialAnswerConfigNameIdMapping(specialAnswerConfigNameIdMapping)
                            .entityList(entityList)
                            .algorithmLabelList(algorithmLabelList)
                            .llmStepConfig(llmStepConfig)
                            .srcBot(srcBot)
                            .build();
                    try {
                        singleSync(stepSyncDTO);
                    } catch (Exception e) {
                        log.error("singleSync错误", e);
                        return 0L;
                    }
                    return botId;
                }));
                futureList.add(future);
            }

            for (Future<Long> future : futureList) {
                if (future.get() > 0L) {
                    successBotIdList.add(future.get());
                    successNum.getAndIncrement();
                } else {
                    failNum.getAndIncrement();
                }
            }
        } catch (Exception e) {
            log.error("流程同步异步线程池出错", e);
        }
        List<Long> failBotIdList = new ArrayList<>(targetBotIdList);
        failBotIdList.removeAll(successBotIdList);
        botSyncOperationLogService.stepSync(stepVO, syncVO, successBotIdList, failBotIdList);

        return BotSyncResultVO.builder().successNum(successNum.get()).failNum(failNum.get()).failBotIdList(failBotIdList).build();
    }


    @Override
    public void resetAllStepLabel(Long botId) {
        List<StepPO> stepList = getAllListByBotId(botId);
        if (CollectionUtils.isEmpty(stepList)) {
            return;
        }
        for (StepPO step : stepList) {
            step.setLabel(null);
            step.setOrderNum(null);
            generateOrderNumber(step);
            generateLabel(botId, step);
            mongoTemplate.save(step, StepPO.COLLECTION_NAME);
            stepNodeService.resetAllNodeLabel(botId, step.getId());
        }
    }

    @Override
    public void resetResourceReferenceInfo(Long newBotId) {
        List<StepPO> stepList = getAllListByBotId(newBotId);
        if (CollectionUtils.isEmpty(stepList)) {
            return;
        }
        for (StepPO step : stepList) {
            updateDependRef(step);
        }
    }

    @Override
    public List<SimpleStep> getSimpleStepListByDialogFlowId(Long dialogFlowId) {
        Long botId = botRefService.getBotId(dialogFlowId);
        List<StepPO> stepList = getAllListByBotId(botId);
        List<SimpleNode> allNodeList = stepNodeService.getAllSimpleNodeListByBotId(botId);
        Map<String, List<SimpleNode>> stepNodeListMap = MyCollectionUtils.listToMapList(allNodeList, SimpleNode::getStepId);
        return stepList.stream()
                .map(step -> {
                    SimpleStep simpleStep = new SimpleStep();
                    simpleStep.setLabel(step.getLabel());
                    simpleStep.setName(step.getName());
                    simpleStep.setId(step.getId());
                    simpleStep.setBotId(botId);
                    simpleStep.setDialogFlowId(dialogFlowId);
                    simpleStep.setNodeList(stepNodeListMap.getOrDefault(step.getId(), Collections.emptyList()));
                    simpleStep.setType(step.getType().getDesc());
                    return simpleStep;
                }).collect(Collectors.toList());
    }

    @Override
    public List<StepPO> deleteByBotIdsAndStepIds(List<Long> botIds, List<String> stepIds, Long userId) {
        if (CollectionUtils.isEmpty(botIds) || CollectionUtils.isEmpty(stepIds)) {
            return Collections.emptyList();
        }

        List<StepPO> stepList = getStepListByIdList(stepIds);
        // 按照botId分组, 并简单校验下 botId 是否超出范围了
        Map<Long, List<StepPO>> botStepListMap = MyCollectionUtils.listToMapList(stepList, StepPO::getBotId);
        for (Long botId : botStepListMap.keySet()) {
            if (!botIds.contains(botId)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不匹配" + botId);
            }
        }

        // 删除流程 如果删除过程中异常, 则删除过的就删除了, 没删除的就不删除了
        botStepListMap.forEach((botId, botStepList) -> {
            batchDelete(botId, botStepList, userId);
        });

        return stepList;
    }

    private StepPO getByBotIdAndLabel(Long botId, String label) {
        return mongoTemplate.findOne(Query.query(Criteria.where("botId").is(botId).and("label").is(label)), StepPO.class, StepPO.COLLECTION_NAME);
    }

    private void singleSync(StepSyncDTO stepSyncDTO) {
        try {

        } finally {

        }
        Long targetBotId = stepSyncDTO.getTargetBotId();
        StepSyncVO syncVO = stepSyncDTO.getSyncVO();
        StepPO stepPO = stepSyncDTO.getStepPO();
        List<DialogBaseNodePO> nodeList = stepSyncDTO.getNodeList();
        BotPO srcBot = stepSyncDTO.getSrcBot();
        Long currentUserId = syncVO.getCurrentUserId();
        StepTypeEnum stepTypeEnum = syncVO.getStepTypeEnum();
        SyncModeEnum sameStep = syncVO.getSameStep();
        SyncModeEnum sameNodeIntent = syncVO.getSameNodeIntent();
        SyncModeEnum sameTriggerIntent = syncVO.getSameTriggerIntent();

        // 校验重名流程，如果存在则更新
        List<IdNamePair<String, String>> idNamePairList = getIdNamePairByBotId(targetBotId);
        Optional<IdNamePair<String, String>> any = idNamePairList.stream().filter(idNamePair -> idNamePair.getName().equals(stepPO.getName())).findAny();
        if (any.isPresent() && SyncModeEnum.SKIP.equals(sameStep)) {
            return;
        }
        StepPO sameLabelStep = getByBotIdAndLabel(targetBotId, stepPO.getLabel());
        if (Objects.nonNull(sameLabelStep)) {
            if (any.isPresent() && !StringUtils.equals(sameLabelStep.getId(), any.get().getId())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程已被占用");
            }
            if (!any.isPresent()) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程已被占用");
            }
        }
        StepPO old = null;
        if (any.isPresent()) {
            old = mongoTemplate.findById(any.get().getId(), StepPO.class);
            if (Objects.isNull(old)) {
                throw new ComException(ComErrorCode.NOT_EXIST, "流程不存在");
            }
            if (stepPO.isLlmStep() && !old.isLlmStep()) {
                return;
            }
        }

        // 同步依赖的变量
        Map<String, String> variableIdMapping = syncDependVariable(stepSyncDTO, targetBotId, currentUserId);

        // 意图映射
        Map<String, String> intentIdMapping = new HashMap<>();
        intentIdMapping.put(ApplicationConstant.DEFAULT_INTENT_ID, ApplicationConstant.DEFAULT_INTENT_ID);
        intentIdMapping.put(ApplicationConstant.USER_SILENCE_INTENT_ID, ApplicationConstant.USER_SILENCE_INTENT_ID);
        intentIdMapping.put(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID, ApplicationConstant.COLLECT_SUCCESS_INTENT_ID);
        intentIdMapping.put(ApplicationConstant.COLLECT_FAILED_INTENT_ID, ApplicationConstant.COLLECT_FAILED_INTENT_ID);
        List<IntentPO> intentPOList = intentService.getAllByBotId(targetBotId);
        // 目标Bot原先的意图名称列表
        Map<String, String> existsIntentNameIdMap = MyCollectionUtils.listToMap(intentPOList, IntentPO::getName, IntentPO::getId);
        Set<String> existsIntentNameKeySet = existsIntentNameIdMap.keySet();

        // 流程条件跳过
        if (Objects.nonNull(stepPO.getSkipCondition())) {
            if (CollectionUtils.isNotEmpty(stepPO.getSkipCondition().getConditionList())) {
                for (List<ConditionExpressionPO> group : stepPO.getSkipCondition().getConditionList()) {
                    if (CollectionUtils.isNotEmpty(group)) {
                        for (ConditionExpressionPO condition : group) {
                            condition.setPreVarId(variableIdMapping.getOrDefault(condition.getPreVarId(), condition.getPreVarId()));
                            condition.setPostVarId(variableIdMapping.getOrDefault(condition.getPostVarId(), condition.getPostVarId()));
                        }
                    }
                }
            }
        }

        // 处理触发意图
        List<? extends IntentPO> triggerIntentPOList = intentService.singleSync(targetBotId, sameTriggerIntent, currentUserId, stepSyncDTO.getTriggerIntentPOList(), stepSyncDTO.getTriggerIntentCorpusPOList());
        if (BooleanUtils.isTrue(stepPO.getTriggerByIntent()) && CollectionUtils.isNotEmpty(stepPO.getTriggerIntentIdList())) {
            // 新增的意图不会有冲突，会有冲突的只有可能是重名的意图
            List<String> triggerIntentIdList = Lists.newArrayList();
            List<String> updateTriggerIntentIdList = Lists.newArrayList();
            Map<String, String> createTriggerIntentNameIdMap = MyCollectionUtils.listToMap(triggerIntentPOList, IntentPO::getName, IntentPO::getId);
            Map<String, String> originIntentIdNameMap = MyCollectionUtils.listToMap(stepSyncDTO.getTriggerIntentPOList(), IntentPO::getId, IntentPO::getName);
            Set<String> originTriggerIntentNameSet = stepPO.getTriggerIntentIdList().stream()
                    .map(originIntentIdNameMap::get)
                    .collect(Collectors.toSet());
            stepSyncDTO.getTriggerIntentPOList().forEach(intentPO -> {
                if (!originTriggerIntentNameSet.contains(intentPO.getName())) {
                    return;
                }
                if (existsIntentNameKeySet.contains(intentPO.getName())) {
                    triggerIntentIdList.add(existsIntentNameIdMap.get(intentPO.getName()));
                    updateTriggerIntentIdList.add(existsIntentNameIdMap.get(intentPO.getName()));
                } else {
                    triggerIntentIdList.add(createTriggerIntentNameIdMap.get(intentPO.getName()));
                }
            });
            stepPO.setTriggerIntentIdList(triggerIntentIdList);
            // 清空互斥的关联关系
            // 新的关联关系在创建流程时新建
            List<SourceRefPO> sourceRefPOList = sourceRefService.getBySourceIdList(targetBotId, updateTriggerIntentIdList, SourceTypeEnum.INTENT);
            if (CollectionUtils.isNotEmpty(sourceRefPOList)) {
                CopyOnWriteArrayList<String> copyOnWriteArrayList;
                for (SourceRefPO sourceRefPO : sourceRefPOList) {
                    switch (sourceRefPO.getRefType()) {
                        case STEP:
                            StepVO refStepPO = getById(targetBotId, sourceRefPO.getRefId());
                            copyOnWriteArrayList = Lists.newCopyOnWriteArrayList(refStepPO.getTriggerIntentIdList());
                            copyOnWriteArrayList.remove(sourceRefPO.getSourceId());
                            refStepPO.setTriggerIntentIdList(copyOnWriteArrayList);
                            mongoTemplate.save(refStepPO, StepPO.COLLECTION_NAME);
                            break;
                        case KNOWLEDGE:
                            KnowledgePO refKnowledgePO = knowledgeService.getById(targetBotId, sourceRefPO.getRefId());
                            copyOnWriteArrayList = Lists.newCopyOnWriteArrayList(refKnowledgePO.getTriggerIntentIdList());
                            copyOnWriteArrayList.remove(sourceRefPO.getSourceId());
                            refKnowledgePO.setTriggerIntentIdList(copyOnWriteArrayList);
                            mongoTemplate.save(refKnowledgePO, KnowledgePO.COLLECTION_NAME);
                            break;
                        case SPECIAL_ANSWER:
                            SpecialAnswerConfigPO refSpecialAnswerConfigPO = specialAnswerConfigService.getById(targetBotId, sourceRefPO.getRefId());
                            copyOnWriteArrayList = Lists.newCopyOnWriteArrayList(refSpecialAnswerConfigPO.getTriggerIntentIdList());
                            copyOnWriteArrayList.remove(sourceRefPO.getSourceId());
                            refSpecialAnswerConfigPO.setTriggerIntentIdList(copyOnWriteArrayList);
                            mongoTemplate.save(refSpecialAnswerConfigPO, SpecialAnswerConfigPO.COLLECTION_NAME);
                            break;
                        default:
                    }
                }
            }
            sourceRefService.deleteBySourceIdList(targetBotId, updateTriggerIntentIdList, IntentRefTypeEnum.STEP);
            sourceRefService.deleteBySourceIdList(targetBotId,  updateTriggerIntentIdList, IntentRefTypeEnum.KNOWLEDGE);
            sourceRefService.deleteBySourceIdList(targetBotId, updateTriggerIntentIdList, IntentRefTypeEnum.SPECIAL_ANSWER);
        }

        // 创建或更新流程
        stepPO.setBotId(targetBotId);
        stepPO.setType(stepTypeEnum);
        stepPO.setCreateUserId(currentUserId);
        stepPO.setUpdateUserId(currentUserId);
        if (Objects.nonNull(old)) {
            // 如果原先有触发意图，需要清空原先的关联
            if (BooleanUtils.isTrue(old.getTriggerByIntent())) {
                sourceRefService.deleteBySourceIdList(targetBotId, old.getTriggerIntentIdList(), IntentRefTypeEnum.STEP);
            }

            stepPO.setId(any.get().getId());
            stepPO.setOrderNum(old.getOrderNum());
            stepPO.setLabel(stepPO.getLabel());
            stepPO.setUpdateTime(LocalDateTime.now());
            mongoTemplate.save(stepPO, StepPO.COLLECTION_NAME);

            updateDependRef(stepPO);
        } else {
            stepPO.setId(null);
            stepPO.setLabel(stepPO.getLabel());
            stepPO.setOrderNum(null);
            doCreate(stepPO, currentUserId, true);
        }
        String stepId = stepPO.getId();

        // 处理节点意图
        intentPOList = intentService.getAllByBotId(targetBotId);
        // 目标Bot原先的意图名称列表
        Map<String, String> currentAllIntentNameIdMap =  MyCollectionUtils.listToMap(intentPOList, IntentPO::getName, IntentPO::getId);
        List<? extends IntentPO> nodeIntentPOList = intentService.singleSync(targetBotId, sameNodeIntent, currentUserId, stepSyncDTO.getNodeIntentPOList(), stepSyncDTO.getNodeIntentCorpusPOList());
        Map<String, String> createNodeIntentNameIdMap = MyCollectionUtils.listToMap(nodeIntentPOList, IntentPO::getName, IntentPO::getId);
        stepSyncDTO.getNodeIntentPOList().forEach(intentPO -> {
            if (currentAllIntentNameIdMap.containsKey(intentPO.getName())) {
                // 如果含有重名意图，就用目标Bot内该意图的ID
                intentIdMapping.put(intentPO.getId(), currentAllIntentNameIdMap.get(intentPO.getName()));
            } else {
                // 如果不重名，说明需要新建意图，用新建的意图ID代替
                intentIdMapping.put(intentPO.getId(), createNodeIntentNameIdMap.get(intentPO.getName()));
            }
        });


        // 判断意向标签组是否一致
        AtomicBoolean syncIntentLevel = new AtomicBoolean(false);
        BotVO targetBot = botService.detail(targetBotId);
        if (Objects.equals(targetBot.getIntentLevelTagId(), stepSyncDTO.getIntentLevelTagId())) {
            syncIntentLevel.set(true);
        }

        // 流程映射
        Map<String, String> stepIdMapping = new HashMap<>();
        Map<String, String> stepNameIdMapping = stepSyncDTO.getStepNameIdMapping();
        Set<String> stepNameKeySet = stepNameIdMapping.keySet();
        idNamePairList.forEach(idNamePair -> {
            if (stepNameKeySet.contains(idNamePair.getName())) {
                stepIdMapping.put(stepNameIdMapping.get(idNamePair.getName()), idNamePair.getId());
            }
        });

        // 知识映射
        Map<String, String> knowledgeIdMapping = new HashMap<>();
        Map<String, String> knowledgeNameIdMapping = stepSyncDTO.getKnowledgeNameIdMapping();
        Set<String> knowledgeNameKeySet = knowledgeNameIdMapping.keySet();
        knowledgeService.getAllListByBotId(targetBotId).forEach(knowledgePO -> {
            if (knowledgeNameKeySet.contains(knowledgePO.getName())) {
                knowledgeIdMapping.put(knowledgeNameIdMapping.get(knowledgePO.getName()), knowledgePO.getId());
            }
        });

        // 特殊语境映射
        Map<String, String> specialAnswerConfigIdMapping = new HashMap<>();
        Map<String, String> specialAnswerConfigNameIdMapping = stepSyncDTO.getSpecialAnswerConfigNameIdMapping();
        Set<String> specialAnswerConfigNameSet = specialAnswerConfigNameIdMapping.keySet();
        specialAnswerConfigService.getByBotId(targetBotId).forEach(po -> {
            if (specialAnswerConfigNameSet.contains(po.getName())) {
                specialAnswerConfigIdMapping.put(specialAnswerConfigNameIdMapping.get(po.getName()), po.getId());
            }
        });

        // 同步实体
        Map<String, String> entityIdMapping = new HashMap<>();
        for (BaseEntityPO src : stepSyncDTO.getEntityList()) {
            BaseEntityPO targetEntity = entityService.singleSync(targetBotId, src, syncVO.getSameEntity());
            entityIdMapping.put(src.getId(), targetEntity.getId());
        }

        // 节点拷贝
        // 变量和意图的关联在StepNodeService#autoSave中已处理
        Map<String, String> nodeIdMapping = new HashMap<>();
        nodeList.forEach(dialogBaseNodePO -> {
            String nodeId = MyCollectionUtils.getOrPut(nodeIdMapping, dialogBaseNodePO.getId(), new ObjectId().toString());
            dialogBaseNodePO.setId(nodeId);
            dialogBaseNodePO.setBotId(targetBotId);
            dialogBaseNodePO.setStepId(stepId);
            dialogBaseNodePO.setCreateTime(LocalDateTime.now());
            dialogBaseNodePO.setUpdateTime(LocalDateTime.now());
            dialogBaseNodePO.setCreateUserId(currentUserId);
            dialogBaseNodePO.setUpdateUserId(currentUserId);

            // 节点意图
            if (dialogBaseNodePO instanceof DialogChatNodePO) {
                DialogChatNodePO chatNodePO = (DialogChatNodePO) dialogBaseNodePO;
                if (CollectionUtils.isNotEmpty(chatNodePO.getSelectIntentIdList())) {
                    List<String> collect = chatNodePO.getSelectIntentIdList().stream()
                            .map(intentIdMapping::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    chatNodePO.setSelectIntentIdList(collect);
                }
                Map<String, String> intentRelatedNodeMap = chatNodePO.getIntentRelatedNodeMap();
                Map<String, String> newIntentRelatedNodeMap = Maps.newHashMap();
                intentRelatedNodeMap.forEach((key, value) -> {
                    String intentId = intentIdMapping.get(key);
                    String nId = MyCollectionUtils.getOrPut(nodeIdMapping, value, new ObjectId().toString());
                    newIntentRelatedNodeMap.put(intentId, nId);
                });
                chatNodePO.setIntentRelatedNodeMap(newIntentRelatedNodeMap);
                if (CollectionUtils.isNotEmpty(chatNodePO.getTriggerWaitIntentIdList())) {
                    List<String> collect = chatNodePO.getTriggerWaitIntentIdList().stream()
                            .map(intentIdMapping::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    chatNodePO.setTriggerWaitIntentIdList(collect);
                }
            }
            // 判断节点
            if (NodeTypeEnum.JUDGE.equals(dialogBaseNodePO.getType())) {
                DialogJudgeNodePO judgeNode = (DialogJudgeNodePO) dialogBaseNodePO;
                if (MapUtils.isNotEmpty(judgeNode.getBranchRelatedNodeMap())) {
                    Map<String, String> newBranchRelatedMap = new HashMap<>();
                    judgeNode.getBranchRelatedNodeMap().forEach((branchId, nextNodeId) -> {
                        String newNextNodeId = MyCollectionUtils.getOrPut(nodeIdMapping, nextNodeId, new ObjectId().toString());
                        newBranchRelatedMap.put(branchId, newNextNodeId);
                    });
                    judgeNode.setBranchRelatedNodeMap(newBranchRelatedMap);
                }
                if (CollectionUtils.isNotEmpty(judgeNode.getBranchList())) {
                    judgeNode.getBranchList().forEach(branch -> {
                        List<List<ConditionExpressionPO>> conditionList = branch.getConditionList();
                        if (CollectionUtils.isNotEmpty(conditionList)) {
                            conditionList.forEach(conditionExpressionPOS -> conditionExpressionPOS.forEach(conditionExpressionPO -> {
                                conditionExpressionPO.setPreVarId(variableIdMapping.get(conditionExpressionPO.getPreVarId()));
                                conditionExpressionPO.setPostVarId(variableIdMapping.get(conditionExpressionPO.getPostVarId()));
                                if (CollectionUtils.isNotEmpty(conditionExpressionPO.getIntentIdList())) {
                                    conditionExpressionPO.setIntentIdList(conditionExpressionPO.getIntentIdList().stream().map(intentIdMapping::get).filter(Objects::nonNull).collect(Collectors.toList()));
                                }
                            }));
                        }
                        if (BooleanUtils.isTrue(branch.getEnableAction()) && CollectionUtils.isNotEmpty(branch.getActionList())) {
                            branch.getActionList().forEach(action -> action.setVarId(variableIdMapping.get(action.getVarId())));
                        }
                    });
                }
            }

            if (NodeTypeEnum.COLLECT.equals(dialogBaseNodePO.getType())) {
                DialogCollectNodePO collectNode = (DialogCollectNodePO) dialogBaseNodePO;
                if (CollectionUtils.isNotEmpty(collectNode.getEntityCollectList())) {
                    collectNode.getEntityCollectList().forEach(entityItem -> {
                        entityItem.setEntityId(entityIdMapping.get(entityItem.getEntityId()));
                        entityItem.setVariableId(variableIdMapping.get(entityItem.getVariableId()));
                    });
                }
                if (BooleanUtils.isTrue(collectNode.getEnableSkipCondition())
                        && CollectionUtils.isNotEmpty(collectNode.getSkipConditionList())) {
                    for (CollectNodeSkipCondition condition : collectNode.getSkipConditionList()) {
                        if (CollectionUtils.isNotEmpty(condition.getIntentIdList())) {
                            condition.setIntentIdList(condition.getIntentIdList().stream().map(intentIdMapping::get).filter(Objects::nonNull).collect(Collectors.toList()));
                        }
                    }
                }
            }

            List<NodeAnswer> answerList = dialogBaseNodePO.getAnswerList();
            if (CollectionUtils.isNotEmpty(answerList)) {
                answerList.forEach(nodeAnswer -> {
                    if (BooleanUtils.isTrue(nodeAnswer.getEnableVarCondition())) {
                        List<List<ConditionExpressionPO>> conditionList = nodeAnswer.getConditionList();
                        if (CollectionUtils.isNotEmpty(conditionList)) {
                            conditionList.forEach(conditionExpressionPOS -> conditionExpressionPOS.forEach(conditionExpressionPO -> {
                                conditionExpressionPO.setPreVarId(variableIdMapping.get(conditionExpressionPO.getPreVarId()));
                                conditionExpressionPO.setPostVarId(variableIdMapping.get(conditionExpressionPO.getPostVarId()));
                            }));
                        }
                    }
                });
            }

            // 清空意向等级
            if (!syncIntentLevel.get()) {
                dialogBaseNodePO.setIntentLevelDetailCode(null);
            }

            dialogBaseNodePO.mapMismatch(stepIdMapping, knowledgeIdMapping, specialAnswerConfigIdMapping);
            dialogBaseNodePO.mapUninterrupted(stepIdMapping, knowledgeIdMapping, specialAnswerConfigIdMapping, intentIdMapping);

            // 跳转至指定流程
            if (NodeTypeEnum.JUMP.equals(dialogBaseNodePO.getType())) {
                DialogJumpNodePO jumpNodePO = (DialogJumpNodePO) dialogBaseNodePO;
                jumpNodePO.setJumpStepId(stepIdMapping.getOrDefault(jumpNodePO.getJumpStepId(), null));
            }

            // 查询节点http信息中的变量id
            if (NodeTypeEnum.QUERY.equals(dialogBaseNodePO.getType())) {
                IdMappingUtils.mappingQueryNodeHttpInfo((DialogQueryNodePO) dialogBaseNodePO, variableIdMapping);
            }

            // 按键采集节点同步动态变量
            if (dialogBaseNodePO instanceof DialogKeyCaptureNodePO) {
                DialogKeyCaptureNodePO dialogKeyCaptureNode = (DialogKeyCaptureNodePO) dialogBaseNodePO;
                dialogKeyCaptureNode.setResultVarId(variableIdMapping.get(dialogKeyCaptureNode.getResultVarId()));
            }

            // 同步动态变量赋值
            if (BooleanUtils.isTrue(dialogBaseNodePO.getEnableAssign())) {
                dialogBaseNodePO.setEnableAssign(true);
                if (Objects.nonNull(dialogBaseNodePO.getEntityAssign())) {
                    dialogBaseNodePO.getEntityAssign().mappingVariableId(variableIdMapping);
                    dialogBaseNodePO.getEntityAssign().mappingEntityId(entityIdMapping);
                }
                if (Objects.nonNull(dialogBaseNodePO.getConstantAssign())) {
                    dialogBaseNodePO.getConstantAssign().mappingVariableId(variableIdMapping);
                    dialogBaseNodePO.getConstantAssign().mappingEntityId(entityIdMapping);
                }
                if (Objects.nonNull(dialogBaseNodePO.getOriginInputAssign())) {
                    dialogBaseNodePO.getOriginInputAssign().mappingVariableId(variableIdMapping);
                    dialogBaseNodePO.getOriginInputAssign().mappingIntentId(intentIdMapping);
                }
            } else {
                dialogBaseNodePO.setEnableAssign(false);
                dialogBaseNodePO.setConstantAssign(null);
                dialogBaseNodePO.setEntityAssign(null);
                dialogBaseNodePO.setOriginInputAssign(null);
            }
        });

        stepNodeService.autoSave(targetBotId, stepId, nodeList, currentUserId);

        // 同步大模型流程配置
        if (Objects.nonNull(stepSyncDTO.getLlmStepConfig())) {
            llmStepConfigService.sync(stepSyncDTO.getLlmStepConfig(), stepPO, stepIdMapping, knowledgeIdMapping, specialAnswerConfigIdMapping, variableIdMapping);
        }

        // 同行业同场景进行算法标签同步
        if (Objects.equals(srcBot.getCustomerTrackType(), targetBot.getCustomerTrackType()) && Objects.equals(srcBot.getCustomerSceneId(), targetBot.getCustomerSceneId())) {
            algorithmLabelService.sync(stepSyncDTO.getAlgorithmLabelList(), targetBotId, stepId, nodeIdMapping);
        }

        //同步音频
        if (BooleanUtils.isTrue(syncVO.getSyncAudio())) {
            answerAudioManagerService.copyAudioByAnswerSource(syncVO.getSrcBotId(), stepSyncDTO.getTargetBotId(), Lists.newArrayList(stepSyncDTO.getStepPO()), currentUserId);
        }
    }

    @NotNull
    private Map<String, String> syncDependVariable(StepSyncDTO stepSyncDTO, Long targetBotId, Long currentUserId) {
        return variableService.syncDependVariable(stepSyncDTO.getVariablePOList(), targetBotId, currentUserId);
    }

    private List<StepPO> getStepListByIdList(Collection<String> stepIdList) {
        if (CollectionUtils.isEmpty(stepIdList)) {
            return Collections.emptyList();
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(stepIdList));
        return mongoTemplate.find(query, StepPO.class, StepPO.COLLECTION_NAME);
    }

    private void validParam(StepPO step, boolean create, boolean isSync) {
        checkNameDuplicate(step.getBotId(), step.getName(), step.getId());

        DependentResourceBO dependResource = dependResourceService.generateByCondition(new DependentResourceBO.Condition(step.getBotId()).step().variable().intent());
        validAndThrow(step, dependResource, isSync);
    }

    private void checkNameDuplicate(Long botId, String name, String excludeId) {
        List<StepPO> list = getAllListByBotId(botId);
        for (StepPO item : list) {
            if (StringUtils.equals(item.getName(), name) && !StringUtils.equals(item.getId(), excludeId)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("流程名称: %s 已存在", name));
            }
        }
    }

    private Integer generateOrderNum(Long botId) {
        return botResourceSequenceService.generate(botId, BotResourceSequenceService.MAIN_STEP_ORDER_NUM);
    }

    private List<StepPO> queryListByBotIdAndType(Long botId,
                                                 @Nullable StepTypeEnum stepType) {
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "机器人id不能为空");
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId));
        if (Objects.nonNull(stepType)) {
            query.addCriteria(Criteria.where("type").is(stepType.name()));
        }

        List<StepPO> stepList = mongoTemplate.find(query, StepPO.class, StepPO.COLLECTION_NAME);
        stepList.sort(Comparator.comparingInt(item -> Objects.isNull(item.getOrderNum()) ? Integer.MAX_VALUE : item.getOrderNum()));
        return stepList;
    }

}
