package com.yiwise.dialogflow.service.impl.llm;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.dto.llmchat.LLMChatResponse;
import com.yiwise.dialogflow.reactor.Accumulator;
import com.yiwise.dialogflow.utils.AnswerTextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 对大模型生成的答案文本进行转换
 */
@Slf4j
public class LLMGenerateResponsePostProcessor implements Accumulator<LLMChatResponse, LLMChatResponse> {

    Map<String, String> answerConvertMap = new HashMap<>();

    Map<String, String> variableMap;

    public LLMGenerateResponsePostProcessor(Map<String, String> variableMap) {
        this.variableMap = variableMap == null ? Collections.emptyMap() : variableMap;
        if (StringUtils.isNotBlank(ApplicationConstant.LLM_GENERATE_ANSWER_CONVERT_JSON)) {
            try {
                answerConvertMap = JsonUtils.string2MapObject(ApplicationConstant.LLM_GENERATE_ANSWER_CONVERT_JSON, String.class, String.class);
            } catch (Exception e) {
                log.error("解析大模型答案转换配置失败", e);
            }
        }
    }

    @Override
    public Optional<LLMChatResponse> accumulate(LLMChatResponse llmChatResponse) {
        if (Objects.nonNull(llmChatResponse)
                && StringUtils.isNotBlank(llmChatResponse.getResponse())) {
            String originAnswer = llmChatResponse.getResponse();

            boolean isNotSetGenderVariable = !variableMap.containsKey("性别");
            boolean containsGender = variableMap.values().stream().anyMatch(item -> {
                if (StringUtils.isBlank(item)) {
                    return false;
                }
                return item.contains("先生") || item.contains("女士");
            });
            if (isNotSetGenderVariable && !containsGender) {
                if (originAnswer.contains("先生") || originAnswer.contains("女士")) {
                    originAnswer = "";
                    log.debug("未设置性别, 且生成答案中包含性别称谓, 忽略当前短句:{}", originAnswer);
                }
            }

            if (MapUtils.isNotEmpty(answerConvertMap)) {
                for (String regex : answerConvertMap.keySet()) {
                    if (StringUtils.isNotBlank(regex)) {
                        try {
                            originAnswer = originAnswer.replaceAll(regex, answerConvertMap.get(regex));
                        } catch (Exception e) {
                            log.warn("大模型生成答案后处理失败", e);
                        }
                    }
                }
            }

            if (!StringUtils.equals(originAnswer, llmChatResponse.getResponse())) {
                log.info("大模型生成答案后处理成功，原始答案：{}，转换后的答案：{}", llmChatResponse.getResponse(), originAnswer);
            }

            if (StringUtils.isBlank(originAnswer) || isOnlyContainsPun(originAnswer)) {
                return Optional.empty();
            }

            llmChatResponse.setResponse(originAnswer);
        }

        return Optional.ofNullable(llmChatResponse);
    }

    private boolean isOnlyContainsPun(String str) {
        // 判断是否仅仅包含标点符号
        return StringUtils.isBlank(AnswerTextUtils.removeAnswerPrefixAndSuffixSymbols(str));
    }

    @Override
    public Optional<LLMChatResponse> onComplete() {
        return Optional.empty();
    }
}
