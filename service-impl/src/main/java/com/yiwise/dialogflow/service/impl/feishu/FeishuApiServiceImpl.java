package com.yiwise.dialogflow.service.impl.feishu;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.middleware.redis.service.RedisOpsService;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.entity.bo.feishu.FeishuMsgBody;
import com.yiwise.dialogflow.entity.bo.feishu.FeishuMsgResponse;
import com.yiwise.dialogflow.entity.bo.feishu.FeishuUserInfoVO;
import com.yiwise.dialogflow.service.feishu.FeishuApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022/10/12
 */
@Slf4j
@Service
public class FeishuApiServiceImpl implements FeishuApiService {
    private static final Logger logger = LoggerFactory.getLogger(FeishuApiServiceImpl.class);

    public static final String APP_ID = "cli_a12eff153ffa100d";
    public static final String APP_SECRET = "DIodMa7JDHgelQBcObDR4gdUf47Lix4W";

    private static final String GET_TOKEN_URL = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal";
    private static final String MESSAGE_URL = "https://open.feishu.cn/open-apis/im/v1/messages";
    private static final String GET_USER_ID_URL = "https://open.feishu.cn/open-apis/contact/v3/users/batch_get_id";
    private static final String CHATS_URL = "https://open.feishu.cn/open-apis/im/v1/chats";
    private static final String CHATS_SEARCH_URL = "https://open.feishu.cn/open-apis/im/v1/chats/search";

    private static final String CHATS_MEMBER_URL = "https://open.feishu.cn/open-apis/im/v1/chats/%s/members";

    @Resource
    private RedisOpsService redisOpsService;

    @Resource
    private RestTemplate restTemplate;

    /**
     * 获取accessToken
     * @see <a href="https://open.feishu.cn/document/ukTMukTMukTM/ukDNz4SO0MjL5QzM/auth-v3/auth/tenant_access_token_internal"></a>
     */
    @Override
    public String getToken() {
        String key = RedisKeyCenter.getFeishuAccessToken();
        if (redisOpsService.isKeyExist(key)) {
            return redisOpsService.get(key);
        }
        try {
            Map<String, String> body = new HashMap<>();
            body.put("app_id", APP_ID);
            body.put("app_secret", APP_SECRET);
            HttpEntity<String> httpEntity = new HttpEntity<>(JsonUtils.object2PrettyString(body), new HttpHeaders());
            ResponseEntity<String> exchange = restTemplate.exchange(GET_TOKEN_URL, HttpMethod.POST, httpEntity, String.class);
            Map<String, String> result = JsonUtils.string2MapObject(exchange.getBody(), String.class, String.class);
            logger.debug("收到的请求: {}", result);

            // 过期时间为2小时，提前两分钟到期
            if (MapUtils.isNotEmpty(result) && Integer.parseInt(result.get("code")) == 0) {
                redisOpsService.set(key, result.get("tenant_access_token"));
                redisOpsService.expire(key, Long.parseLong(result.get("expire")) - 120, TimeUnit.SECONDS);
            }

            return result.get("tenant_access_token");
        } catch (Exception e) {
            logger.error("获取飞书token失败", e);
            return "";
        }
    }

    /**
     * 获取手机号或邮箱对应的飞书open_id
     * @see <a href="https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/batch_get_id"></a>
     */
    @Override
    public List<String> getUserOpenIdList(List<String> emails, List<String> mobiles) {
        if (CollectionUtils.isEmpty(emails) && CollectionUtils.isEmpty(mobiles)) {
            return new ArrayList<>();
        }

        // open_id不会变，用缓存暂存一下
        String key = RedisKeyCenter.getFeishuOpenId();
        RedisTemplate redisTemplate = redisOpsService.getRedisTemplate();
        Map<String, String> entries = redisTemplate.opsForHash().entries(key);
        if (MapUtils.isNotEmpty(entries)) {
            if (CollectionUtils.isNotEmpty(emails) && entries.keySet().containsAll(emails)) {
                return emails.stream().map(entries::get).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(mobiles) && entries.keySet().containsAll(mobiles)) {
                return mobiles.stream().map(entries::get).collect(Collectors.toList());
            }
        }

        try {
            HttpHeaders headers = new HttpHeaders();
            String token = getToken();
            //若没有token则直接返回
            if (StringUtils.isBlank(token)) {
                return new ArrayList<>();
            }
            String auth = "Bearer " + token;
            headers.add("Authorization", auth);
            headers.add("Content-Type", "application/json; charset=utf-8");

            // 默认获取的是open_id，查询user_id需要权限
            MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
            multiValueMap.put("emails", emails);
            multiValueMap.put("mobiles", mobiles);
            String url = UriComponentsBuilder.fromHttpUrl(GET_USER_ID_URL).build().toUriString();
            HttpEntity<String> httpEntity = new HttpEntity<>(JsonUtils.object2PrettyString(multiValueMap), headers);
            ResponseEntity<String> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            FeishuMsgResponse feishuMsgResponse = JsonUtils.string2Object(exchange.getBody(), FeishuMsgResponse.class);
            List<String> result = new ArrayList<>();
            JSONObject jsonObject = feishuMsgResponse.getData();
            if (feishuMsgResponse.isSuccess()) {
                JSONArray userList = jsonObject.getJSONArray("user_list");
                logger.info("获取飞书用户id, user_list={}", userList.toJSONString());
                for (int i = 0; i < userList.size(); i++) {
                    JSONObject user = userList.getJSONObject(i);
                    result.add(user.getString("user_id"));
                    String hashKey = StringUtils.isEmpty(user.getString("email")) ? user.getString("mobile") : user.getString("email");
                    redisTemplate.opsForHash().putIfAbsent(key, hashKey, user.getString("user_id"));
                    redisOpsService.expire(key, 30, TimeUnit.DAYS);
                }
            } else {
                int code = jsonObject.getIntValue("code");
                String msg = jsonObject.getString("msg");
                logger.error("[LogHub_Warn]获取飞书用户id失败，code={}, msg={}", code, msg);
            }
            return result;
        } catch (Exception e) {
            logger.error("[LogHub_Warn]获取飞书用户id失败", e);
        }
        return new ArrayList<>();
    }

    /**
     * 创建群
     * @see <a href="https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat/create"></a>
     */
    @Override
    public String createChat(String name, List<String> userIdList) {
        try {
            HttpHeaders headers = new HttpHeaders();
            String token = getToken();
            //若没有token则直接返回
            if (StringUtils.isBlank(token)) {
                return null;
            }
            String auth = "Bearer " + token;
            headers.add("Authorization", auth);
            headers.add("Content-Type", "application/json; charset=utf-8");

            String url = UriComponentsBuilder.fromHttpUrl(CHATS_URL).build().toUriString();
            Map<String, Object> map = Maps.newHashMap();
            map.put("name", name);
            if (CollectionUtils.isNotEmpty(userIdList)) {
                map.put("user_id_list", userIdList);
            }
            HttpEntity<String> httpEntity = new HttpEntity<>(JsonUtils.object2PrettyString(map), headers);
            ResponseEntity<String> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            FeishuMsgResponse feishuMsgResponse = JsonUtils.string2Object(exchange.getBody(), FeishuMsgResponse.class);
            JSONObject jsonObject = feishuMsgResponse.getData();
            if (feishuMsgResponse.isSuccess()) {
                return jsonObject.getString("chat_id");
            } else {
                int code = jsonObject.getIntValue("code");
                String msg = jsonObject.getString("msg");
                logger.error("[LogHub_Warn]创建飞书群组失败，code={}, msg={}", code, msg);
            }
        } catch (Exception e) {
            logger.error("[LogHub_Warn]创建飞书群组失败", e);
        }
        return null;
    }

    /**
     * 获取群成员列表
     * @see <a href="https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-members/get"></a>
     */
    @Override
    public List<String> memberList(String chatId) {
        List<String> resultList = Lists.newArrayList();
        try {
            HttpHeaders headers = new HttpHeaders();
            String token = getToken();
            //若没有token则直接返回
            if (StringUtils.isBlank(token)) {
                return resultList;
            }
            String auth = "Bearer " + token;
            headers.add("Authorization", auth);
            headers.add("Content-Type", "application/json; charset=utf-8");

            String url = UriComponentsBuilder.fromHttpUrl(String.format(CHATS_MEMBER_URL, chatId))
                                 .queryParam("member_id_type", "open_id")
                                 .build()
                                 .toUriString();
            HttpEntity<String> httpEntity = new HttpEntity<>(headers);
            ResponseEntity<String> exchange = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
            FeishuMsgResponse feishuMsgResponse = JsonUtils.string2Object(exchange.getBody(), FeishuMsgResponse.class);
            JSONObject jsonObject = feishuMsgResponse.getData();
            if (feishuMsgResponse.isSuccess()) {
                JSONArray items = jsonObject.getJSONArray("items");
                for (int i = 0; i < items.size(); i++) {
                    JSONObject temp = items.getJSONObject(i);
                    String memberId = temp.getString("member_id");
                    resultList.add(memberId);
                }
            } else {
                int code = jsonObject.getIntValue("code");
                String msg = jsonObject.getString("msg");
                logger.error("[LogHub_Warn]获取群成员列表失败，code={}, msg={}", code, msg);
            }
        } catch (Exception e) {
            logger.error("[LogHub_Warn]获取群成员列表失败", e);
        }
        return resultList;
    }

    /**
     * 将用户拉入群聊
     * @see <a href="https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-members/create"></a>
     */
    @Override
    public void createChatMember(String chatId, List<String> userIdList) {
        try {
            HttpHeaders headers = new HttpHeaders();
            String token = getToken();
            //若没有token则直接返回
            if (StringUtils.isBlank(token)) {
                return;
            }
            String auth = "Bearer " + token;
            headers.add("Authorization", auth);
            headers.add("Content-Type", "application/json; charset=utf-8");

            String url = UriComponentsBuilder.fromHttpUrl(String.format(CHATS_MEMBER_URL, chatId)).build().toUriString();
            Map<String, Object> map = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(userIdList)) {
                map.put("id_list", userIdList);
            }
            HttpEntity<String> httpEntity = new HttpEntity<>(JsonUtils.object2PrettyString(map), headers);
            ResponseEntity<String> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            FeishuMsgResponse feishuMsgResponse = JsonUtils.string2Object(exchange.getBody(), FeishuMsgResponse.class);
            JSONObject jsonObject = feishuMsgResponse.getData();
            if (feishuMsgResponse.isSuccess()) {
                logger.info("将用户拉入群聊结果：{}", jsonObject.toJSONString());
            } else {
                int code = jsonObject.getIntValue("code");
                String msg = jsonObject.getString("msg");
                logger.error("[LogHub_Warn]将用户拉入群聊，code={}, msg={}", code, msg);
            }
        } catch (Exception e) {
            logger.error("[LogHub_Warn]将用户拉入群聊", e);
        }
    }

    /**
     * 搜索用户或机器人可见的群列表
     * @see <a href="https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat/search"></a>
     */
    @Override
    public JSONArray searchChat(String query) {
        try {
            HttpHeaders headers = new HttpHeaders();
            String token = getToken();
            //若没有token则直接返回
            if (StringUtils.isBlank(token)) {
                return new JSONArray();
            }
            String auth = "Bearer " + token;
            headers.add("Authorization", auth);
            headers.add("Content-Type", "application/json; charset=utf-8");

            String url = UriComponentsBuilder.fromHttpUrl(CHATS_SEARCH_URL).queryParam("query", query).queryParam("page_size", 100).build().toUriString();
            HttpEntity<String> httpEntity = new HttpEntity<>(headers);
            ResponseEntity<String> exchange = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
            FeishuMsgResponse feishuMsgResponse = JsonUtils.string2Object(exchange.getBody(), FeishuMsgResponse.class);
            JSONObject jsonObject = feishuMsgResponse.getData();
            if (feishuMsgResponse.isSuccess()) {
                JSONArray items = jsonObject.getJSONArray("items");
                logger.info("items={}", items.toJSONString());
                return items;
            } else {
                int code = jsonObject.getIntValue("code");
                String msg = jsonObject.getString("msg");
                logger.error("[LogHub_Warn]搜索飞书群组失败，code={}, msg={}", code, msg);
            }
        } catch (Exception e) {
            logger.error("[LogHub_Warn]搜索飞书群组失败", e);
        }
        return new JSONArray();
    }

    /**
     * 发送消息
     * @see <a href="https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/create"></a>
     */
    @Override
    public String sendMsg(String receiveIdType, String receiveId, String msgType, String content) {
        try {
            HttpHeaders headers = new HttpHeaders();
            String auth = "Bearer " + getToken();
            headers.add("Authorization", auth);
            headers.add("Content-Type", "application/json; charset=utf-8");

            Map<String, String> map = Maps.newHashMap();
            map.put("receive_id", receiveId);
            map.put("msg_type", msgType);
            map.put("content", content);
            String url = UriComponentsBuilder.fromHttpUrl(MESSAGE_URL).queryParam("receive_id_type", receiveIdType).build().toUriString();
            HttpEntity<String> httpEntity = new HttpEntity<>(JsonUtils.object2PrettyString(map), headers);
            ResponseEntity<String> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            FeishuMsgResponse feishuMsgResponse = JsonUtils.string2Object(exchange.getBody(), FeishuMsgResponse.class);
            JSONObject jsonObject = feishuMsgResponse.getData();
            if (feishuMsgResponse.isSuccess()) {
                return jsonObject.getString("message_id");
            } else {
                int code = jsonObject.getIntValue("code");
                String msg = jsonObject.getString("msg");
                logger.error("[LogHub_Warn]发送飞书消息失败，code={}, msg={}", code, msg);
            }
        } catch (Exception e) {
            logger.error("[LogHub_Warn]发送飞书消息失败", e);
        }
        return null;
    }

    @Override
    public void sendFeishuWarn(List<String> mobiles,String title, String content, String webhook) {
        try {
            List<String> idList = getUserIdsFeishu(mobiles);
            FeishuMsgBody bodyObj = new FeishuMsgBody(title,content,idList);
            String body = bodyObj.toJson();
            HttpHeaders headers = new HttpHeaders();
            String auth = "Bearer " + getToken();
            headers.add("Authorization",auth);
            headers.add("Content-Type","application/json; charset=utf-8");
            HttpEntity<String> httpEntity = new HttpEntity<>(body, headers);
            restTemplate.exchange(webhook, HttpMethod.POST,httpEntity, String.class);
        } catch (Exception e) {
            log.error("推送飞书预警消息失败", e);
        }
    }

    @Override
    public void sendFeishuWarn(String body, String webhook) {
        try {
            HttpHeaders headers = new HttpHeaders();
            String auth = "Bearer " + getToken();
            headers.add("Authorization",auth);
            headers.add("Content-Type","application/json; charset=utf-8");
            HttpEntity<String> httpEntity = new HttpEntity<>(body, headers);
            restTemplate.exchange(webhook, HttpMethod.POST,httpEntity, String.class);
        } catch (Exception e) {
            log.error("推送飞书预警消息失败", e);
        }
    }

    @Override
    public List<String> getUserIdsFeishu(List<String> mobiles) {
        if(CollectionUtils.isEmpty(mobiles)){
            return new ArrayList<>();
        }
        try {
            HttpHeaders headers = new HttpHeaders();
            String token = getToken();
            //若没有token则直接返回
            if(StringUtils.isBlank(token)){
                return new ArrayList<>();
            }
            String auth = "Bearer "+token;
            headers.add("Authorization",auth);
            headers.add("Content-Type","application/json; charset=utf-8");
            String url = "https://open.feishu.cn/open-apis/user/v1/batch_get_id?";
            StringBuilder urlBuilder = new StringBuilder(url);

            mobiles.forEach(item-> {
                urlBuilder.append("mobiles=");
                urlBuilder.append(item);
                urlBuilder.append("&");
            });
            url = urlBuilder.toString();
            Map<String, String> body = new HashMap<>();
            HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(body,headers);
            ResponseEntity<String> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            FeishuUserInfoVO feishuUserInfoVO = JsonUtils.string2Object(exchange.getBody(), FeishuUserInfoVO.class);
            List<String> result = new ArrayList<>();
            mobiles.forEach(item -> {
                try {
                    result.add(feishuUserInfoVO.getData().getMobile_users().get(item).get(0).getUser_id());
                } catch (Exception e) {
                    log.error("获取飞书用户id失败, response={}", exchange.getBody(), e);
                }
            });
            log.debug("飞书用户id列表是{}",result);
            return result;
        } catch (Exception  e) {
            log.error("获取飞书用户id失败", e);
        }
        return new ArrayList<>();
    }
}
