package com.yiwise.dialogflow.service.impl.llm;

import cn.hutool.core.io.FileUtil;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.OssKeyCenter;
import com.yiwise.dialogflow.entity.bo.SnapshotInvalidFailItemMsg;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.enums.BotResourceTypeEnum;
import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.entity.enums.llm.DocumentSplitTypeEnum;
import com.yiwise.dialogflow.entity.enums.llm.DocumentTypeEnum;
import com.yiwise.dialogflow.entity.enums.llm.RagDocumentStatusEnum;
import com.yiwise.dialogflow.entity.po.llm.DocumentSplitConfigPO;
import com.yiwise.dialogflow.entity.po.llm.RagDocumentPO;
import com.yiwise.dialogflow.entity.po.llm.RagDocumentSegmentPO;
import com.yiwise.dialogflow.entity.query.RagDocumentQueryVO;
import com.yiwise.dialogflow.entity.vo.llm.*;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.RobotResourceService;
import com.yiwise.dialogflow.service.SpecialAnswerConfigService;
import com.yiwise.dialogflow.service.impl.SpecialAnswerConfigServiceImpl;
import com.yiwise.dialogflow.service.llm.AlgorithmRagDocService;
import com.yiwise.dialogflow.service.llm.RagDocumentSegmentService;
import com.yiwise.dialogflow.service.llm.RagDocumentService;
import com.yiwise.dialogflow.service.operationlog.RagDocumentOperationLogService;
import com.yiwise.dialogflow.utils.BotExportUtils;
import com.yiwise.dialogflow.utils.ParseUtil;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.bson.types.ObjectId;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RagDocumentServiceImpl implements RagDocumentService, RobotResourceService {

    @Resource
    private BotService botService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private ObjectStorageHelper objectStorageHelper;

    @Resource
    private RagDocumentOperationLogService ragDocumentOperationLogService;

    @Resource
    private AlgorithmRagDocService algorithmRagDocService;

    @Lazy
    @Resource
    private RagDocumentSegmentService ragDocumentSegmentService;

    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    private static final Long MAX_FILE_SIZE = 1024 * 1024L;
    private static final List<String> SUPPORT_DOC_TYPE = Arrays.asList("txt", "docx", "xlsx");
    private static final Long TIMEOUT_SECONDS = 10 * 60L;

    @Override
    @SneakyThrows
    public String upload(MultipartFile file, Long botId) {
        String originalFilename = file.getOriginalFilename();
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "文档不能大于1MB");
        }
        if (!SUPPORT_DOC_TYPE.contains(FileUtil.getSuffix(originalFilename))) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "文件格式错误");
        }
        String ossKey = OssKeyCenter.getBotRagDocUploadOssKey(botId, BotExportUtils.normalizeFileName(originalFilename));
        objectStorageHelper.upload(ossKey, file.getInputStream());
        return ossKey;
    }

    private boolean exists(Long botId, String docName) {
        return mongoTemplate.exists(Query.query(Criteria.where("botId").is(botId).and("docName").is(docName)), RagDocumentPO.COLLECTION_NAME);
    }

    private String generateId() {
        return new ObjectId() + ApplicationConstant.ALGORITHM_RAG_DOC_ID_SUFFIX;
    }

    @Override
    public void createDocument(RagDocumentCreateVO request, Long userId) {
        String docName = request.getDocName();
        if (exists(request.getBotId(), docName)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "文档名称重复");
        }

        LocalDateTime now = LocalDateTime.now();
        RagDocumentPO po = new RagDocumentPO();
        po.setId(generateId());
        po.setBotId(request.getBotId());
        po.setDocumentType(DocumentTypeEnum.DOC);
        po.setDocName(docName);
        po.setFileOssKey(request.getUrl());
        po.setStatus(RagDocumentStatusEnum.PARSING);
        po.setEnabledStatus(EnabledStatusEnum.ENABLE);
        po.setCreateTime(now);
        po.setUpdateTime(now);
        po.setCreateUserId(userId);
        po.setUpdateUserId(userId);
        mongoTemplate.save(po, RagDocumentPO.COLLECTION_NAME);
        ragDocumentOperationLogService.createDoc(po);

        uploadDocAndUpdateStatusWhenFailed(po);
        botService.onUpdateBotResource(request.getBotId());
    }

    private void updateLastParseTime(String docId) {
        LocalDateTime now = LocalDateTime.now();
        mongoTemplate.updateFirst(
                Query.query(Criteria.where("_id").is(docId)),
                Update.update("lastParseTime", now).set("updateTime", now),
                RagDocumentPO.class,
                RagDocumentPO.COLLECTION_NAME
        );
    }

    private void uploadDocAndUpdateStatusWhenFailed(RagDocumentPO doc) {
        updateLastParseTime(doc.getId());
        if (algorithmRagDocService.uploadDoc(doc)) {
            return;
        }
        updateStatus(doc.getId(), RagDocumentStatusEnum.FAILED);
    }

    private void updateStatus(String docId, RagDocumentStatusEnum status) {
        mongoTemplate.updateFirst(
                Query.query(Criteria.where("_id").is(docId)),
                Update.update("status", status).set("updateTime", LocalDateTime.now()),
                RagDocumentPO.COLLECTION_NAME
        );
    }

    @Override
    public RagDocumentPO getById(String docId) {
        return mongoTemplate.findById(docId, RagDocumentPO.class, RagDocumentPO.COLLECTION_NAME);
    }

    @Override
    public void callback(String request) {
        log.info("[LogHub]收到RAG文档上传回调消息={}", request);
        RagDocumentCallbackVO callback = JsonUtils.string2Object(request, RagDocumentCallbackVO.class);
        String docId = callback.getFileId();
        if (!callback.isSuccess()) {
            updateStatus(docId, RagDocumentStatusEnum.FAILED);
            return;
        }
        RagDocumentPO doc = getById(docId);
        if (Objects.isNull(doc)) {
            return;
        }
        List<RagDocumentSegmentPO> segments = callback.getSegments();
        if (CollectionUtils.isEmpty(segments)) {
            updateStatus(docId, RagDocumentStatusEnum.FAILED);
            return;
        }
        segments.forEach(segment -> {
            segment.setBotId(doc.getBotId());
            segment.setRagDocumentId(docId);
            mongoTemplate.save(segment, RagDocumentSegmentPO.COLLECTION_NAME);
        });
        updateStatus(docId, RagDocumentStatusEnum.SUCCESS);
    }

    private Query buildQuery(RagDocumentQueryVO condition) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(condition.getBotId()));
        if (StringUtils.isNotBlank(condition.getSearch())) {
            query.addCriteria(Criteria.where("docName").regex(ParseUtil.regex(condition.getSearch())));
        }
        if (CollectionUtils.isNotEmpty(condition.getDocIdList())) {
            query.addCriteria(Criteria.where("_id").in(condition.getDocIdList()));
        }
        if (Objects.nonNull(condition.getStatus())) {
            query.addCriteria(Criteria.where("status").is(condition.getStatus()));
        }
        if (Objects.nonNull(condition.getEnabledStatus())) {
            query.addCriteria(Criteria.where("enabledStatus").is(condition.getEnabledStatus()));
        }
        return query;
    }

    @Override
    public PageResultObject<RagDocumentVO> queryByCondition(RagDocumentQueryVO condition) {
        Query query = buildQuery(condition);
        long count = mongoTemplate.count(query, RagDocumentPO.COLLECTION_NAME);
        if (count <= 0) {
            return PageResultObject.of(Collections.emptyList(), condition.getPageNum(), condition.getPageSize());
        }
        query.with(PageRequest.of(condition.getPageNum() - 1, condition.getPageSize()));
        query.with(Sort.by(Sort.Direction.DESC, "updateTime"));
        List<RagDocumentVO> resultList = mongoTemplate.find(query, RagDocumentVO.class, RagDocumentVO.COLLECTION_NAME);
        updateToFailedWhenTimeout(resultList);
        return PageResultObject.of(resultList, condition.getPageNum(), condition.getPageSize(), (int) count);
    }

    @Override
    public boolean checkEnableLLMChat(Long botId) {
        return specialAnswerConfigService.checkEnableLLMChat(botId);
    }

    private void updateToFailedWhenTimeout(List<RagDocumentVO> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        resultList.forEach(doc -> {
            LocalDateTime lastParseTime = doc.getLastParseTime();
            if (RagDocumentStatusEnum.isParsing(doc.getStatus()) && Objects.nonNull(lastParseTime) && lastParseTime.plusSeconds(TIMEOUT_SECONDS).isBefore(now)) {
                updateStatus(doc.getId(), RagDocumentStatusEnum.FAILED);
            }
        });
    }

    @Override
    public void updateEnabledStatus(Long botId, String docId, EnabledStatusEnum enabledStatus, Long userId) {
        RagDocumentPO doc = mongoTemplate.findAndModify(
                Query.query(Criteria.where("botId").is(botId).and("_id").is(docId).and("enabledStatus").ne(enabledStatus)),
                Update.update("enabledStatus", enabledStatus).set("updateTime", LocalDateTime.now()).set("updateUserId", userId),
                new FindAndModifyOptions().returnNew(true),
                RagDocumentPO.class,
                RagDocumentPO.COLLECTION_NAME
        );
        if (Objects.nonNull(doc)) {
            ragDocumentOperationLogService.updateEnabledStatus(doc);
            botService.onUpdateBotResource(botId);
        }
    }

    @Override
    public void update(RagDocumentUpdateVO request, Long userId) {
        RagDocumentPO doc = getByBotIdAndDocId(request.getBotId(), request.getRagDocumentId());
        if (Objects.isNull(doc)) {
            return;
        }
        String oldName = doc.getDocName();
        String newName = request.getDocName();
        if (StringUtils.equals(oldName, newName)) {
            return;
        }
        if (exists(request.getBotId(), newName)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "文档名称已存在");
        }
        doc.setDocName(newName);
        doc.setUpdateTime(LocalDateTime.now());
        doc.setUpdateUserId(userId);
        mongoTemplate.save(doc, RagDocumentPO.COLLECTION_NAME);

        ragDocumentOperationLogService.updateDocName(request.getBotId(), oldName, newName, userId);
        botService.onUpdateBotResource(request.getBotId());
    }

    @Override
    public void delete(RagDocumentQueryVO request, Long userId) {
        List<RagDocumentPO> docList = mongoTemplate.findAllAndRemove(buildQuery(request), RagDocumentPO.class, RagDocumentPO.COLLECTION_NAME);
        if (CollectionUtils.isNotEmpty(docList)) {
            docList.forEach(doc -> {
                ragDocumentSegmentService.deleteByDocId(doc.getId());
                algorithmRagDocService.deleteDoc(doc.getId());
            });
            ragDocumentOperationLogService.deleteDoc(request.getBotId(), docList, userId);
            botService.onUpdateBotResource(request.getBotId());
        }
    }

    private RagDocumentPO getByBotIdAndDocId(Long botId, String docId) {
        return mongoTemplate.findOne(
                Query.query(Criteria.where("botId").is(botId).and("_id").is(docId)),
                RagDocumentPO.class,
                RagDocumentPO.COLLECTION_NAME
        );
    }

    private void validateSplitRequest(RagDocumentSplitRequestVO request) {
        if (DocumentSplitTypeEnum.isAuto(request.getSplitType())) {
            return;
        }
        if (Objects.isNull(request.getSegmentMaxWords())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "自定义分段字数上限不能为空");
        }
        if (Objects.isNull(request.getSplitMethod())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "自定义分段标识不能为空");
        }
    }

    @Override
    public void reSplit(RagDocumentSplitRequestVO splitRequest, Long userId) {
        Long botId = splitRequest.getBotId();
        String docId = splitRequest.getRagDocumentId();
        validateSplitRequest(splitRequest);

        RagDocumentPO doc = getByBotIdAndDocId(botId, docId);
        if (Objects.isNull(doc) || !RagDocumentStatusEnum.isSuccess(doc.getStatus())) {
            return;
        }

        doc.setSplitConfig(MyBeanUtils.copy(splitRequest, DocumentSplitConfigPO.class));
        doc.setUpdateTime(LocalDateTime.now());
        doc.setUpdateUserId(userId);
        doc.setStatus(RagDocumentStatusEnum.PARSING);
        mongoTemplate.save(doc, RagDocumentPO.COLLECTION_NAME);
        ragDocumentSegmentService.deleteByDocId(docId);
        ragDocumentOperationLogService.reSplitDoc(doc);

        if (algorithmRagDocService.deleteDoc(docId)) {
            uploadDocAndUpdateStatusWhenFailed(doc);
        }
        botService.onUpdateBotResource(botId);
    }

    @Override
    public void reParse(Long botId, String docId, Long userId) {
        RagDocumentPO doc = getByBotIdAndDocId(botId, docId);
        if (Objects.isNull(doc)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "文档不存在");
        }
        if (!RagDocumentStatusEnum.isFailed(doc.getStatus())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "文档状态不为失败，不能重新解析");
        }
        doc.setStatus(RagDocumentStatusEnum.PARSING);
        doc.setUpdateTime(LocalDateTime.now());
        doc.setUpdateUserId(userId);
        mongoTemplate.save(doc, RagDocumentPO.COLLECTION_NAME);

        uploadDocAndUpdateStatusWhenFailed(doc);
        botService.onUpdateBotResource(botId);
    }

    private List<RagDocumentPO> getByIdList(Collection<String> idList) {
        return mongoTemplate.find(Query.query(Criteria.where("_id").in(idList)), RagDocumentPO.class, RagDocumentPO.COLLECTION_NAME);
    }

    @Override
    public RagDocumentTestResultVO test(RagDocumentTestRequestVO request) {
        long startTime = System.currentTimeMillis();
        request.setEnabledStatus(EnabledStatusEnum.ENABLE);
        request.setStatus(RagDocumentStatusEnum.SUCCESS);
        Query query = buildQuery(request);
        List<String> docIdList = MyCollectionUtils.listToConvertList(mongoTemplate.find(query, RagDocumentPO.class, RagDocumentPO.COLLECTION_NAME), RagDocumentPO::getId);

        List<RagDocumentSegmentVO> searchResultList = algorithmRagDocService.search(request.getTestContent(), docIdList);
        if (CollectionUtils.isEmpty(searchResultList)) {
            return new RagDocumentTestResultVO(System.currentTimeMillis() - startTime, Collections.emptyList());
        }

        List<String> segmentIdList = MyCollectionUtils.listToConvertList(searchResultList, RagDocumentSegmentPO::getId);
        List<RagDocumentSegmentPO> segmentList = ragDocumentSegmentService.getByIdList(segmentIdList);
        if (CollectionUtils.isEmpty(segmentList)) {
            return new RagDocumentTestResultVO(System.currentTimeMillis() - startTime, Collections.emptyList());
        }

        Map<String, String> segmentIdDocIdMap = MyCollectionUtils.listToMap(segmentList, RagDocumentSegmentPO::getId, RagDocumentSegmentPO::getRagDocumentId);
        List<RagDocumentPO> docList = getByIdList(segmentIdDocIdMap.values());
        Map<String, String> docIdNameMap = MyCollectionUtils.listToMap(docList, RagDocumentPO::getId, RagDocumentPO::getDocName);

        List<SimpleSegmentInfoVO> resultList = new ArrayList<>(searchResultList.size());
        for (RagDocumentSegmentVO segment : searchResultList) {
            SimpleSegmentInfoVO vo = new SimpleSegmentInfoVO();
            String docId = segmentIdDocIdMap.get(segment.getId());
            vo.setDocId(docId);
            vo.setSegmentId(segment.getId());
            vo.setDocName(docIdNameMap.get(docId));
            vo.setContent(segment.getContent());
            vo.setScore(segment.getScore());
            resultList.add(vo);
        }
        return new RagDocumentTestResultVO(System.currentTimeMillis() - startTime, resultList);
    }

    @Override
    public String export(String docId) {
        RagDocumentPO doc = getById(docId);
        if (Objects.isNull(doc)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "文档不存在");
        }
        if (!RagDocumentStatusEnum.isSuccess(doc.getStatus())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "仅支持解析成功的文档导出");
        }
        return exportSingleDoc(doc);
    }

    private String exportSingleDoc(RagDocumentPO doc) {
        return BotExportUtils.uploadFile(doc.getBotId(), doc.getDocName() + ".docx", new Consumer<FileOutputStream>() {
            @Override
            @SneakyThrows
            public void accept(FileOutputStream fos) {
                exportOneDoc(doc, fos);
            }
        });
    }

    private void exportOneDoc(RagDocumentPO po, FileOutputStream fos) throws IOException {
        List<String> contentList = MyCollectionUtils.listToConvertList(ragDocumentSegmentService.getByDocId(po.getBotId(), po.getId()), RagDocumentSegmentPO::getContent);
        XWPFDocument doc = new XWPFDocument();
        contentList.forEach(content -> {
            writeLine(doc, content);
            writeLine(doc, "");
        });
        doc.write(fos);
        IOUtils.closeQuietly(doc, null);
    }

    private void writeLine(XWPFDocument doc, String content) {
        for (String c : content.split("\n")) {
            doc.createParagraph().createRun().setText(c);
        }
    }

    @Override
    public String batchExport(RagDocumentQueryVO request) {
        request.setStatus(RagDocumentStatusEnum.SUCCESS);
        List<RagDocumentPO> docList = mongoTemplate.find(buildQuery(request), RagDocumentPO.class, RagDocumentPO.COLLECTION_NAME);
        if (CollectionUtils.isEmpty(docList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "没有可导出的文档");
        }
        // 只有一个文档的话，直接导出doc文件
        if (docList.size() == 1) {
            return exportSingleDoc(docList.get(0));
        }
        return BotExportUtils.batchExport(docList, "文档知识-" + System.currentTimeMillis() + ".zip",
                (doc, fileGenerator) -> fileGenerator.generate(doc.getDocName() + ".docx", new Consumer<FileOutputStream>() {
                    @Override
                    @SneakyThrows
                    public void accept(FileOutputStream fos) {
                        exportOneDoc(doc, fos);
                    }
                }));
    }

    @Override
    public void changeUpdateTimeToNow(String docId) {
        mongoTemplate.updateFirst(Query.query(Criteria.where("_id").is(docId)),
                new Update().set("updateTime", LocalDateTime.now()), RagDocumentPO.class, RagDocumentPO.COLLECTION_NAME);
    }

    @Override
    public List<RagDocumentPO> getByBotId(Long botId) {
        return mongoTemplate.find(Query.query(Criteria.where("botId").is(botId)), RagDocumentPO.class, RagDocumentPO.COLLECTION_NAME);
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        context.getSnapshot().setRagDocumentList(getByBotId(context.getSrcBotId()));
        context.getSnapshot().setRagDocumentSegmentList(ragDocumentSegmentService.getByBotId(context.getSrcBotId()));
    }

    @Override
    public void validateResource(RobotResourceContext context) {
        if (V3BotTypeEnum.MAGIC.equals(context.getSrcBotType())) {
            return;
        }
        // 未启用大模型兜底话术不校验
        if (!specialAnswerConfigService.checkEnableLLMChat(context.getSnapshot().getSpecialAnswerConfigList())) {
            return;
        }
        List<RagDocumentPO> ragDocumentList = context.getSnapshot().getRagDocumentList();
        if (CollectionUtils.isEmpty(ragDocumentList)) {
            return;
        }
        boolean match = ragDocumentList.stream().filter(doc -> EnabledStatusEnum.ENABLE.equals(doc.getEnabledStatus()))
                .anyMatch(doc -> !RagDocumentStatusEnum.isSuccess(doc.getStatus()));
        if (match) {
            SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                    .builder()
                    .resourceType(BotResourceTypeEnum.RAG_DOC)
                    .failMsg("文档知识存在启用且未解析成功状态，请检查!")
                    .build();
            context.getInvalidMsgList().add(msg);
        }
    }

    @Override
    public List<Class<? extends RobotResourceService>> dependsOn() {
        return Collections.singletonList(SpecialAnswerConfigServiceImpl.class);
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        List<RagDocumentPO> docList = context.getSnapshot().getRagDocumentList();
        if (CollectionUtils.isEmpty(docList)) {
            return;
        }
        // 轻量版话术只复制启用且解析成功的文档
        if (V3BotTypeEnum.MAGIC.equals(context.getTargetBotType())) {
            docList = docList.stream().filter(doc -> EnabledStatusEnum.ENABLE.equals(doc.getEnabledStatus()) && RagDocumentStatusEnum.isSuccess(doc.getStatus())).collect(Collectors.toList());
        }

        Map<String, List<RagDocumentSegmentPO>> docIdSegmentListMap = MyCollectionUtils.listToMapList(context.getSnapshot().getRagDocumentSegmentList(), RagDocumentSegmentPO::getRagDocumentId);
        Long targetBotId = context.getTargetBotId();
        for (RagDocumentPO doc : docList) {
            String oldId = doc.getId();
            RagDocumentStatusEnum status = doc.getStatus();
            String newDocId = generateId();
            doc.setId(newDocId);
            doc.setBotId(targetBotId);
            doc.setStatus(RagDocumentStatusEnum.PARSING);
            mongoTemplate.save(doc, RagDocumentPO.COLLECTION_NAME);

            if (RagDocumentStatusEnum.isSuccess(status)) {
                List<RagDocumentSegmentPO> segmentList = docIdSegmentListMap.getOrDefault(oldId, Collections.emptyList());
                List<RagDocumentSegmentPO> newSegmentList = algorithmRagDocService.batchUpload(newDocId, MyCollectionUtils.listToConvertList(segmentList, RagDocumentSegmentPO::getContent));
                if (CollectionUtils.isNotEmpty(newSegmentList)) {
                    newSegmentList.forEach(segment -> {
                        segment.setRagDocumentId(newDocId);
                        segment.setBotId(targetBotId);
                        mongoTemplate.save(segment, RagDocumentSegmentPO.COLLECTION_NAME);
                    });
                    updateStatus(newDocId, RagDocumentStatusEnum.SUCCESS);
                    continue;
                }
            }
            uploadDocAndUpdateStatusWhenFailed(doc);
        }
    }
}