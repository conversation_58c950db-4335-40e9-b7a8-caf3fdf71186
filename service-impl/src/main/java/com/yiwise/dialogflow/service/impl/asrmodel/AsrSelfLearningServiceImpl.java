package com.yiwise.dialogflow.service.impl.asrmodel;

import com.github.pagehelper.PageHelper;
import com.tencentcloudapi.asr.v20190614.models.Model;
import com.yiwise.base.common.thread.decorator.MDCDecoratorRunnable;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.bo.asr.AsrLmData;
import com.yiwise.dialogflow.entity.bo.asr.AsrLmModel;
import com.yiwise.dialogflow.entity.enums.AsrSourceConcurrencyRecordTypeEnum;
import com.yiwise.dialogflow.engine.share.enums.AsrProviderEnum;
import com.yiwise.dialogflow.entity.enums.asr.AsrSlefLearningTrainStatusEnum;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrSelfLearningDetailPO;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrSelfLearningProviderRelationPO;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrSourceConcurrencyRecordPO;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.entity.query.AsrSelfLearningDetailQuery;
import com.yiwise.dialogflow.entity.query.BotQuery;
import com.yiwise.dialogflow.entity.vo.BotVO;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrSelfLearningDetailVO;
import com.yiwise.dialogflow.mapper.AsrSelfLearningPOMapper;
import com.yiwise.dialogflow.mapper.AsrSelfLearningProviderRelationPOMapper;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.asrmodel.AsrSelfLearningProviderRelationService;
import com.yiwise.dialogflow.service.asrmodel.AsrSelfLearningService;
import com.yiwise.dialogflow.service.asrmodel.AsrSourceConcurrencyRecordService;
import com.yiwise.dialogflow.service.remote.UserService;
import com.yiwise.dialogflow.thread.AsrModelThreadExecutorHelper;
import com.yiwise.dialogflow.utils.asr.AliSelfLearningApiUtil;
import com.yiwise.dialogflow.utils.asr.TencentCosUtil;
import com.yiwise.dialogflow.utils.asr.TencentSelfLearningApiUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/19 14:52:38
 */
@Service
public class AsrSelfLearningServiceImpl implements AsrSelfLearningService {

    private static final Logger logger = LoggerFactory.getLogger(AsrSelfLearningServiceImpl.class);

    @Resource
    private AsrSelfLearningPOMapper asrSelfLearningPOMapper;

    @Resource
    private AsrSelfLearningProviderRelationService asrSelfLearningProviderRelationService;

    @Resource
    private AsrSelfLearningProviderRelationPOMapper asrSelfLearningProviderRelationPOMapper;

    @Resource
    private BotService botService;

    @Resource
    private AsrSourceConcurrencyRecordService asrSourceConcurrencyRecordService;

    @Resource
    private UserService userService;

    @Override
    public PageResultObject<AsrSelfLearningDetailVO> list(AsrSelfLearningDetailQuery query) {
        if (StringUtils.isNotEmpty(query.getBotInfo())) {
            BotQuery botQuery = new BotQuery();
            botQuery.setName(query.getBotInfo());
            botQuery.setName(query.getBotInfo());
            List<BotVO> botVOList = botService.queryListWithoutPage(botQuery);
            if (CollectionUtils.isNotEmpty(botVOList)) {
                query.setAsrSelfLearningDetailIdList(botVOList.stream().filter(botVO -> botVO.getAsrSelfLearningDetailId() != null).map(botVO -> botVO.getAsrSelfLearningDetailId()).collect(Collectors.toList()));
            } else {
                return PageResultObject.of(Collections.emptyList());
            }
        }
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<AsrSelfLearningDetailVO> asrSelfLearningDetailVOList = asrSelfLearningPOMapper.listByCondition(query);
        if (CollectionUtils.isNotEmpty(asrSelfLearningDetailVOList)) {
            List<Long> sourceIdList = asrSelfLearningDetailVOList.stream().map(AsrSelfLearningDetailVO::getAsrSelfLearningDetailId).collect(Collectors.toList());
            List<AsrSourceConcurrencyRecordPO> asrSourceConcurrencyRecordPOList = asrSourceConcurrencyRecordService.countByTime(query.getStartTime(), query.getEndTime(), sourceIdList, AsrSourceConcurrencyRecordTypeEnum.ASR_SELF_LEARNING);
            Map<Long, Integer> countMap = asrSourceConcurrencyRecordPOList.stream().collect(Collectors.toMap(AsrSourceConcurrencyRecordPO::getSourceId, AsrSourceConcurrencyRecordPO::getCount));
            BotQuery bquery = new BotQuery();
            bquery.setAsrSelfLearningDetailIdList(asrSelfLearningDetailVOList.stream().map(AsrSelfLearningDetailVO::getAsrSelfLearningDetailId).collect(Collectors.toList()));
            List<BotVO> botVOS = botService.queryListWithoutPage(bquery);
            List<Long> asrSelfIdList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(botVOS)) {
                asrSelfIdList = botVOS.stream().map(BotVO::getAsrSelfLearningDetailId).collect(Collectors.toList());
            }
            List<Long> finalAsrSelfIdList = asrSelfIdList;
            asrSelfLearningDetailVOList.forEach(asrSelfLearningDetailVO -> {
                //调用次数
                asrSelfLearningDetailVO.setCallCount(countMap.getOrDefault(asrSelfLearningDetailVO.getAsrSelfLearningDetailId(), 0));
                //是否绑定话术
                if (CollectionUtils.isNotEmpty(finalAsrSelfIdList) && finalAsrSelfIdList.contains(asrSelfLearningDetailVO.getAsrSelfLearningDetailId())) {
                    asrSelfLearningDetailVO.setIsBind(true);
                }
                //更新人
                UserPO userPO = userService.getUserById(asrSelfLearningDetailVO.getUpdateUserId());
                if (Objects.nonNull(userPO)) {
                    asrSelfLearningDetailVO.setUpdateUserName(userPO.getName());
                }
                //文件名
                getFileNameByUrl(asrSelfLearningDetailVO);
            });
        }
        //删除停用超过12月的
        AsrModelThreadExecutorHelper.execute("停用自学习模型", new MDCDecoratorRunnable(() -> {
            if (CollectionUtils.isNotEmpty(asrSelfLearningDetailVOList)) {
                asrSelfLearningDetailVOList.forEach(asrSelfLearningDetailVO -> {
                    Duration between = Duration.between(asrSelfLearningDetailVO.getUpdateTime(), LocalDateTime.now());
                    if (asrSelfLearningDetailVO.getStatus() == 0 && between.toDays() >= 365) {
                        delete(asrSelfLearningDetailVO);
                    }
                });
            }
        }));
        return PageResultObject.of(asrSelfLearningDetailVOList);
    }

    @Override
    public void update(AsrSelfLearningDetailVO asrSelfLearningDetailVO) {
        asrSelfLearningDetailVO.setUpdateUserId(asrSelfLearningDetailVO.getCurrentUserId());
        asrSelfLearningDetailVO.setUpdateTime(LocalDateTime.now());
        if (Objects.isNull(asrSelfLearningDetailVO.getAsrSelfLearningDetailId())) {
            List<AsrSelfLearningDetailPO> asrSelfLearningDetailPOList = getByName(asrSelfLearningDetailVO.getName());
            if (CollectionUtils.isNotEmpty(asrSelfLearningDetailPOList)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "自学习模型名称重复");
            }
            asrSelfLearningDetailVO.setCreateUserId(asrSelfLearningDetailVO.getCurrentUserId());
            asrSelfLearningPOMapper.insertSelective(asrSelfLearningDetailVO);
        } else {
            asrSelfLearningPOMapper.updateByPrimaryKeySelective(asrSelfLearningDetailVO);
            //若语料内容变更，从而重新训练
            if (BooleanUtils.isTrue(asrSelfLearningDetailVO.getIsUpdateCorpus())) {
                AsrModelThreadExecutorHelper.execute("训练自学习模型", new MDCDecoratorRunnable(() -> {
                    List<AsrSelfLearningProviderRelationPO> asrSelfLearningProviderRelationPOList = asrSelfLearningProviderRelationService.getByAsrSelfLearningId(asrSelfLearningDetailVO.getAsrSelfLearningDetailId());
                    if (CollectionUtils.isNotEmpty(asrSelfLearningProviderRelationPOList)) {
                        asrSelfLearningProviderRelationPOList.forEach(asrSelfLearningProviderRelationPO -> {
                            //先修改训练状态为未训练，在去对应厂商训练
                            asrSelfLearningProviderRelationService.updateTrainStatusById(asrSelfLearningProviderRelationPO.getAsrSelfLearningProviderRelationId(), AsrSlefLearningTrainStatusEnum.NOT_TRAIN);
                            switch (asrSelfLearningProviderRelationPO.getProvider()) {
                                case ALI:
                                    updateAliDataSetAndTrain(asrSelfLearningDetailVO, asrSelfLearningProviderRelationPO.getDataId(), asrSelfLearningProviderRelationPO.getProviderModelId());
                                    break;
                                case TENCENT:
                                    String cosUrl = TencentCosUtil.ossUrlToCosUrl(asrSelfLearningDetailVO.getTenantId(), asrSelfLearningDetailVO.getCurrentUserId(), asrSelfLearningDetailVO.getUrl());
                                    asrSelfLearningDetailVO.setUrl(cosUrl);
                                    TencentSelfLearningApiUtil.updateStatus(asrSelfLearningProviderRelationPO.getProviderModelId(), -1l);
                                    TencentSelfLearningApiUtil.update(asrSelfLearningProviderRelationPO.getProviderModelId(), asrSelfLearningDetailVO.getUrl());
                                    TencentSelfLearningApiUtil.updateStatus(asrSelfLearningProviderRelationPO.getProviderModelId(), 1l);
                                    asrSelfLearningProviderRelationService.updateTrainStatusById(asrSelfLearningProviderRelationPO.getAsrSelfLearningProviderRelationId(), AsrSlefLearningTrainStatusEnum.ONLINE);
                                    break;
                                default:
                                    break;
                            }
                        });
                    }
                }));
            }

        }
    }

    /**
     * 删除源模型的数据集，创建新数据集，将新数据集添加到模型，训练模型
     *
     * @param asrSelfLearningDetailVO
     * @param dataId
     * @param providerModelId
     */
    private void updateAliDataSetAndTrain(AsrSelfLearningDetailVO asrSelfLearningDetailVO, String dataId, String providerModelId) {
        AliSelfLearningApiUtil.removeDataFromAsrLmModel(dataId, providerModelId);
        AliSelfLearningApiUtil.deleteAsrLmData(dataId);
        String newDataId = AliSelfLearningApiUtil.createAsrLmData(asrSelfLearningDetailVO.getName(), asrSelfLearningDetailVO.getUrl(), asrSelfLearningDetailVO.getDescription());
        AliSelfLearningApiUtil.addDataToAsrLmModel(newDataId, providerModelId);
        aliTrainModel(asrSelfLearningDetailVO, providerModelId);
    }

    @Override
    public void stop(AsrSelfLearningDetailVO asrSelfLearningDetailVO) {
        if (Objects.isNull(asrSelfLearningDetailVO.getAsrSelfLearningDetailId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "自学习模型id不能为空");
        }
        asrSelfLearningDetailVO.setStatus(0);
        asrSelfLearningDetailVO.setUpdateTime(LocalDateTime.now());
        asrSelfLearningPOMapper.updateByPrimaryKeySelective(asrSelfLearningDetailVO);
    }

    @Override
    public void start(AsrSelfLearningDetailVO asrSelfLearningDetailVO) {
        if (Objects.isNull(asrSelfLearningDetailVO.getAsrSelfLearningDetailId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "自学习模型id不能为空");
        }
        asrSelfLearningDetailVO.setStatus(1);
        asrSelfLearningDetailVO.setUpdateTime(LocalDateTime.now());
        asrSelfLearningPOMapper.updateByPrimaryKeySelective(asrSelfLearningDetailVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(AsrSelfLearningDetailVO asrSelfLearningDetailVO) {
        if (Objects.isNull(asrSelfLearningDetailVO.getAsrSelfLearningDetailId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "自学习模型id不能为空");
        }
        asrSelfLearningPOMapper.deleteByPrimaryKey(asrSelfLearningDetailVO);
        //删除对应厂商的自学习模型及关联信息
        List<AsrSelfLearningProviderRelationPO> asrSelfLearningProviderRelationPOList = asrSelfLearningProviderRelationService.getByAsrSelfLearningId(asrSelfLearningDetailVO.getAsrSelfLearningDetailId());
        if (CollectionUtils.isNotEmpty(asrSelfLearningProviderRelationPOList)) {
            asrSelfLearningProviderRelationPOList.forEach(asrSelfLearningProviderRelationPO -> {
                asrSelfLearningProviderRelationService.delete(asrSelfLearningProviderRelationPO);
                switch (asrSelfLearningProviderRelationPO.getProvider()) {
                    case ALI:
                        AliSelfLearningApiUtil.deleteAsrLmModel(asrSelfLearningProviderRelationPO.getProviderModelId());
                        AliSelfLearningApiUtil.deleteAsrLmData(asrSelfLearningProviderRelationPO.getDataId());
                        break;
                    case TENCENT:
                        //先下线再删除，否则删除不成功
                        TencentSelfLearningApiUtil.updateStatus(asrSelfLearningProviderRelationPO.getProviderModelId(), -1l);
                        TencentSelfLearningApiUtil.delete(asrSelfLearningProviderRelationPO.getProviderModelId());
                        break;
                    default:
                        break;
                }
            });
        }
    }

    @Override
    public PageResultObject<BotVO> getBotList(Long asrSelfLearningDetailId, String botInfo, Integer pageSize, Integer pageNum) {
        BotQuery botQuery = new BotQuery();
        botQuery.setAsrSelfLearningDetailId(asrSelfLearningDetailId);
        botQuery.setName(botInfo);
        botQuery.setPageNum(pageNum);
        botQuery.setPageSize(pageSize);
        return botService.queryListWithoutWrapVO(botQuery);
    }

    @Override
    public void unbindBot(AsrSelfLearningDetailVO asrSelfLearningDetailVO) {
        if (Objects.isNull(asrSelfLearningDetailVO.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "要解绑的bot不能为空");
        }
        if (Objects.isNull(asrSelfLearningDetailVO.getAsrSelfLearningDetailId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "模型id不能为空");
        }
        botService.unBindAsrSelfLearning(asrSelfLearningDetailVO.getBotId(), asrSelfLearningDetailVO.getAsrSelfLearningDetailId());
    }

    @Override
    public void bindBot(AsrSelfLearningDetailVO asrSelfLearningDetailVO) {
        if (Objects.isNull(asrSelfLearningDetailVO.getBotIdList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "要绑定的bot不能为空");
        }
        if (Objects.isNull(asrSelfLearningDetailVO.getAsrSelfLearningDetailId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "模型id不能为空");
        }

        //批量绑定
        botService.batchBindAsrSelfLearning(asrSelfLearningDetailVO.getBotIdList(), asrSelfLearningDetailVO.getAsrSelfLearningDetailId());
        //训练模型
        trainSelfLearning(asrSelfLearningDetailVO.getTenantId(), asrSelfLearningDetailVO.getCurrentUserId(), asrSelfLearningDetailVO.getAsrSelfLearningDetailId());
    }

    @Override
    public void trainSelfLearning(Long tenantId, Long currentUserId, Long asrSelfLearningDetailId) {
        AsrModelThreadExecutorHelper.execute("训练自学习模型", new MDCDecoratorRunnable(() -> {
            AsrSelfLearningDetailPO asrSelfLearningDetailPO = asrSelfLearningPOMapper.selectByPrimaryKey(asrSelfLearningDetailId);
            if (Objects.nonNull(asrSelfLearningDetailPO)) {
                //阿里
                AsrSelfLearningProviderRelationPO aliSelfRelation = asrSelfLearningProviderRelationService.getByAsrSelfLearningIdAndProvider(asrSelfLearningDetailPO.getAsrSelfLearningDetailId(), AsrProviderEnum.ALI);
                if (Objects.isNull(aliSelfRelation)) {
                    aliCreateAndTrainModel(asrSelfLearningDetailPO);
                } else {
                    if (!AsrSlefLearningTrainStatusEnum.ONLINE.equals(aliSelfRelation.getTrainStatus())) {
                        //未上线则上线
                        aliTrainModel(asrSelfLearningDetailPO, aliSelfRelation.getProviderModelId());
                    }
                }
                //腾讯
                AsrSelfLearningProviderRelationPO tecentSelfRelation = asrSelfLearningProviderRelationService.getByAsrSelfLearningIdAndProvider(asrSelfLearningDetailPO.getAsrSelfLearningDetailId(), AsrProviderEnum.TENCENT);
                if (Objects.isNull(tecentSelfRelation)) {
                    tencentCreateAndTrainModel(asrSelfLearningDetailPO, tenantId, currentUserId);
                } else {
                    if (!AsrSlefLearningTrainStatusEnum.ONLINE.equals(tecentSelfRelation.getTrainStatus())) {
                        //未上线则设为上线
                        TencentSelfLearningApiUtil.updateStatus(tecentSelfRelation.getProviderModelId(), 1l);
                    }
                }
            }
        }));
    }

    @Override
    public void tencentCreateAndTrainModel(AsrSelfLearningDetailPO asrSelfLearningDetailPO, Long tenantId, Long userId) {
        //本地创建模型与厂商的关联
        asrSelfLearningProviderRelationService.add(asrSelfLearningDetailPO.getAsrSelfLearningDetailId(), AsrProviderEnum.TENCENT);
        String cosUrl = TencentCosUtil.ossUrlToCosUrl(tenantId, userId, asrSelfLearningDetailPO.getUrl());
        //url更新为腾讯云cos的url
        asrSelfLearningDetailPO.setUrl(cosUrl);
        //创建模型
        String modelId = TencentSelfLearningApiUtil.create(asrSelfLearningDetailPO.getName(), String.valueOf(System.currentTimeMillis()), asrSelfLearningDetailPO.getUrl());
        if (StringUtils.isNotEmpty(modelId)) {
            //将厂商的模型id和数据集id存入本地系统
            asrSelfLearningProviderRelationService.updateByCondition(asrSelfLearningDetailPO.getAsrSelfLearningDetailId(), modelId, null, AsrProviderEnum.TENCENT);
            try {
                //需要多等一会再上线模型，否则上线会失败
                Thread.sleep(2000l);
            } catch (InterruptedException e) {
            }
            //上线模型
            TencentSelfLearningApiUtil.updateStatus(modelId, 1l);
            //修改本地模型训练状态
            AsrSelfLearningProviderRelationPO asrSelfLearningProviderRelationPO = asrSelfLearningProviderRelationService.getByAsrSelfLearningIdAndProvider(asrSelfLearningDetailPO.getAsrSelfLearningDetailId(), AsrProviderEnum.TENCENT);
            asrSelfLearningProviderRelationService.updateTrainStatusById(asrSelfLearningProviderRelationPO.getAsrSelfLearningProviderRelationId(), AsrSlefLearningTrainStatusEnum.ONLINE);
        }
    }

    @Override
    public List<AsrSelfLearningDetailPO> getByName(String name) {
        return asrSelfLearningPOMapper.getByName(name);
    }

    @Override
    public void deleteAllProviderData(Integer pageNumber, Integer pageSize) {
        //腾讯
        deleteTencentData(pageSize);
        //阿里
        deleteAliData(pageNumber, pageSize);
    }

    @Override
    public AsrLmModel.LmModelPage listAliData(Integer pageNumber, Integer pageSize) {
        return AliSelfLearningApiUtil.listAsrLmModel(pageNumber, pageSize);
    }

    @Override
    public AsrLmModel.LmModelPage listTencentData(Integer pageSize) {
        Model[] models = TencentSelfLearningApiUtil.list(pageSize);
        AsrLmModel.LmModelPage lmModelPage = new AsrLmModel.LmModelPage();
        lmModelPage.setPageSize(pageSize);
        lmModelPage.setTotalItems(Objects.isNull(models) ? 0 : models.length);
        lmModelPage.setModels(models);
        return lmModelPage;
    }

    private void deleteTencentData(Integer pageSize) {
        Model[] list = TencentSelfLearningApiUtil.list(pageSize);
        if (Objects.nonNull(list)) {
            for (Model model : list) {
                //下线
                TencentSelfLearningApiUtil.updateStatus(model.getModelId(), -1l);
                //删除
                TencentSelfLearningApiUtil.delete(model.getModelId());
            }
            logger.debug("【腾讯】删除自学习模型成功，共计：{}个", list.length);
        } else {
            logger.debug("【腾讯】无自学习模型");
        }
    }

    private void deleteAliData(Integer pageNumber, Integer pageSize) {
        //删除模型
        AsrLmModel.LmModelPage lmModelPage = AliSelfLearningApiUtil.listAsrLmModel(pageNumber, pageSize);
        if (Objects.nonNull(lmModelPage) && CollectionUtils.isNotEmpty(lmModelPage.getContent())) {
            lmModelPage.getContent().forEach(lmModel -> {
                AliSelfLearningApiUtil.deleteAsrLmModel(lmModel.getId());
            });
            logger.debug("【阿里】删除自学习模型成功，共计：{}个", lmModelPage.getContent().size());
        } else {
            logger.debug("【阿里】无自学习模型");
        }
        //删除数据集
        AsrLmData.LmDataPage lmDataPage = AliSelfLearningApiUtil.listAsrLmData(pageNumber, pageSize);
        if (Objects.nonNull(lmDataPage) && CollectionUtils.isNotEmpty(lmDataPage.getContent())) {
            lmDataPage.getContent().forEach(lmData -> {
                AliSelfLearningApiUtil.deleteAsrLmData(lmData.getId());
            });
            logger.debug("【阿里】删除数据集成功，共计：{}个", lmDataPage.getContent().size());
        } else {
            logger.debug("【阿里】无数据集");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void aliCreateAndTrainModel(AsrSelfLearningDetailPO asrSelfLearningDetailPO) {
        //本地创建模型与厂商的关联
        asrSelfLearningProviderRelationService.add(asrSelfLearningDetailPO.getAsrSelfLearningDetailId(), AsrProviderEnum.ALI);
        //创建数据集
        String asrLmDataId = AliSelfLearningApiUtil.createAsrLmData(asrSelfLearningDetailPO.getName(), asrSelfLearningDetailPO.getUrl(), asrSelfLearningDetailPO.getDescription());
        //创建自学习模型
        String asrLmModelId = AliSelfLearningApiUtil.createAsrLmModel(asrSelfLearningDetailPO.getName(), AliSelfLearningApiUtil.BASE_ID, asrSelfLearningDetailPO.getDescription());
        //将厂商的模型id和数据集id存入本地系统
        asrSelfLearningProviderRelationService.updateByCondition(asrSelfLearningDetailPO.getAsrSelfLearningDetailId(), asrLmModelId, asrLmDataId, AsrProviderEnum.ALI);
        //添加数据至模型,并训练上线
        if (BooleanUtils.isTrue(AliSelfLearningApiUtil.addDataToAsrLmModel(asrLmDataId, asrLmModelId))) {
            aliTrainModel(asrSelfLearningDetailPO, asrLmModelId);
        }
    }

    @Override
    public void aliTrainModel(AsrSelfLearningDetailPO asrSelfLearningDetailPO, String asrLmModelId) {
        //训练模型，训练成功，状态默认为上线
        if (BooleanUtils.isTrue(AliSelfLearningApiUtil.trainAsrLmModel(asrLmModelId))) {
            //更新本地模型状态为上线
            AsrSelfLearningProviderRelationPO asrSelfLearningProviderRelationPO = asrSelfLearningProviderRelationService.getByAsrSelfLearningIdAndProvider(asrSelfLearningDetailPO.getAsrSelfLearningDetailId(), AsrProviderEnum.ALI);
            if (Objects.nonNull(asrSelfLearningProviderRelationPO)) {
                AsrSelfLearningProviderRelationPO updatePO = new AsrSelfLearningProviderRelationPO();
                updatePO.setAsrSelfLearningProviderRelationId(asrSelfLearningProviderRelationPO.getAsrSelfLearningProviderRelationId());
                updatePO.setTrainStatus(AsrSlefLearningTrainStatusEnum.ONLINE);
                asrSelfLearningProviderRelationPOMapper.updateByPrimaryKeySelective(updatePO);
            }
        }
    }

    @Override
    public AsrSelfLearningDetailPO getById(Long asrSelfLearningDetailId) {
        AsrSelfLearningDetailPO asrSelfLearningDetailPO = asrSelfLearningPOMapper.selectByPrimaryKey(asrSelfLearningDetailId);
        AsrSelfLearningDetailVO asrSelfLearningDetailVO = null;
        if (Objects.nonNull(asrSelfLearningDetailPO)) {
            asrSelfLearningDetailVO = MyBeanUtils.copy(asrSelfLearningDetailPO, AsrSelfLearningDetailVO.class);
            getFileNameByUrl(asrSelfLearningDetailVO);
        }
        return asrSelfLearningDetailVO;
    }

    private void getFileNameByUrl(AsrSelfLearningDetailVO asrSelfLearningDetailVO) {
        String url = asrSelfLearningDetailVO.getUrl();
        if (StringUtils.isNotEmpty(url)) {
            String fileName = "";
            String[] split = url.split("/");
            String s = split[split.length - 1];
            String[] s1 = s.split("_");
            if (s1.length > 2) {
                for (int i = 0; i < s1.length - 1; i++) {
                    fileName += s1[i];
                }
            } else {
                fileName += s1[0];
            }
            fileName += ".txt";
            asrSelfLearningDetailVO.setFileName(fileName);
        }
    }
}