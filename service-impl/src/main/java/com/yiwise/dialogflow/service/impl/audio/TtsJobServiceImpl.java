package com.yiwise.dialogflow.service.impl.audio;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.entity.po.BotAudioConfigPO;
import com.yiwise.dialogflow.entity.po.BotConfigPO;
import com.yiwise.dialogflow.entity.po.TtsJobPO;
import com.yiwise.dialogflow.entity.po.TtsVoiceConfigPO;
import com.yiwise.dialogflow.entity.vo.audio.TtsComposeProgressVO;
import com.yiwise.dialogflow.entity.vo.audio.TtsComposeResultVO;
import com.yiwise.dialogflow.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TtsJobServiceImpl implements TtsJobService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private TtsJobExecuteService ttsJobExecuteService;

    @Resource
    private BotConfigService botConfigService;

    @Override
    public TtsJobPO create(BotAudioConfigPO config, int distTotalCount, Set<String> answerList, Long userId, int distManMadeCompletedCount) {
        // 异步合成
        if (Objects.isNull(config)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "机器人音频配置不能为空");
        }
        TtsVoiceConfigPO ttsConfig;
        // 真人/含变量短句模式和全语音合成模式的音色配置取值字段不同
        if (AudioTypeEnum.COMPOSE.equals(config.getAudioType())) {
            ttsConfig = config.getTtsConfig();
        } else {
            ttsConfig = config.getSpliceTtsConfig();
        }
        if (Objects.isNull(ttsConfig)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "请先配置合成音色");
        }
        if (StringUtils.isBlank(ttsConfig.getTtsVoice())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "请先配置合成音色");
        }
        if (Objects.isNull(ttsConfig.getTtsSpeed())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "请先配置合成速度");
        }
        if (Objects.isNull(ttsConfig.getTtsVolume())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "请先配置合成音量");
        }

        TtsJobPO job = new TtsJobPO();
        job.setBotId(config.getBotId());
        job.setUserId(userId);
        job.setLogId(MDC.get(ApplicationConstant.MDC_LOG_ID));
        job.setTotalCount(distTotalCount);
        job.setManMadeCompletedCount(distManMadeCompletedCount);
        job.setTextSet(answerList);
        job.setTextCount(CollectionUtils.size(answerList));
        job.setCompletedCount(0);
        job.setCompletedSet(Collections.emptySet());
        job.setFailSet(Collections.emptySet());
        job.setCompleted(CollectionUtils.isEmpty(answerList));
        job.setFinish(job.getCompleted());
        job.setFailed(false);
        job.setCreateTime(LocalDateTime.now());
        job.setUpdateTime(LocalDateTime.now());
        job.setSpeed(ttsConfig.getTtsSpeed());
        job.setVolume(ttsConfig.getTtsVolume());
        job.setVoice(ttsConfig.getTtsVoice());
        job.setVoice(ttsConfig.getTtsVoice());


        mongoTemplate.insert(job, TtsJobPO.COLLECTION_NAME);

        if (BooleanUtils.isNotTrue(job.getCompleted())) {
            ttsJobExecuteService.submit(job);
            // 前端非要这个running字段
            job.setRunning(true);
        } else {
            job.setRunning(false);
        }

        return job;
    }

    @Override
    public Optional<TtsJobPO> createIfComposeAudioType(Long botId, Set<String> originalAnswerSet, Long userId) {
        // todo 后续可以在合成前看看是否和已合成的文本重复了
        if (CollectionUtils.isEmpty(originalAnswerSet)) {
            return Optional.empty();
        }
        BotConfigPO config = botConfigService.getByBotId(botId);
        if (Objects.isNull(config)) {
            log.warn("未查询到bot配置:{}", botId);
            return Optional.empty();
        }
        if (Objects.isNull(config.getAudioConfig()) || !AudioTypeEnum.COMPOSE.equals(config.getAudioConfig().getAudioType())) {
            return Optional.empty();
        }

        log.info("创建合成任务, botId:{}, userId:{}, answerSet:{}", botId, userId, originalAnswerSet);
        Set<String> answerSet = new HashSet<>();
        for (String original : originalAnswerSet) {
            AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(original, false);
            answerSet.addAll(splitter.getTextList());
        }

        return Optional.of(create(config.getAudioConfig(), answerSet.size(), answerSet, userId, 0));
    }

    @Override
    public TtsJobPO getLastJob(Long botId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .with(Sort.by(Sort.Direction.DESC, "_id"));
        return mongoTemplate.findOne(query, TtsJobPO.class, TtsJobPO.COLLECTION_NAME);
    }

    @Override
    public boolean lastJobIsRunning(Long botId) {
        TtsJobPO job = getLastJob(botId);
        if (Objects.isNull(job)) {
            return false;
        }
        return ttsJobExecuteService.jobIsRunning(job);
    }

    @Override
    public void updateProgress(Long botId, String jobId, Set<String> completedSet, Set<String> failSet, boolean finish) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("_id").is(jobId));
        Update update = new Update();
        update.set("completedSet", completedSet);
        update.set("failSet", failSet);
        update.set("finish", finish);
        update.set("failed", CollectionUtils.isNotEmpty(failSet));
        update.set("completedCount", completedSet.size());
        update.set("running", !finish);
        if (finish) {
            update.set("completed", CollectionUtils.isEmpty(failSet));
        }
        update.set("updateTime", LocalDateTime.now());
        mongoTemplate.updateFirst(query, update, TtsJobPO.COLLECTION_NAME);
    }

    @Override
    public void updateFinishStatus(Long botId, String jobId, boolean completed, String failMsg) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("_id").is(jobId));
        Update update = new Update();
        update.set("finish", true);
        update.set("running", false);
        update.set("completed", completed);
        update.set("failMsg", failMsg);
        update.set("fail", StringUtils.isNotBlank(failMsg));
        update.set("updateTime", LocalDateTime.now());
        mongoTemplate.updateFirst(query, update, TtsJobPO.COLLECTION_NAME);
    }

    @Override
    public TtsComposeResultVO composeTextByBotConfig(Long botId, String text) {
        return null;
    }

    @Override
    public TtsComposeProgressVO getLastComposeProgress(Long botId) {
        TtsJobPO job = getLastJob(botId);
        TtsComposeProgressVO result = new TtsComposeProgressVO();
        if (Objects.isNull(job)) {
            result.setBotId(botId);
            result.setPercent(0);
            result.setTotalCount(0);
            result.setRunning(false);
            result.setFailed(false);
            return result;
        }
        result.setBotId(job.getBotId());
        int totalCount = job.getTotalCount();
        int textCount = job.getTextCount();
        int existCount = totalCount - textCount;
        int completeCount = job.getCompletedCount();
        int totalCompleteCount = completeCount + existCount;
        result.setCompletedCount(totalCompleteCount);
        result.setTotalCount(job.getTotalCount());
        result.setPercent(calculatePercent(totalCompleteCount, totalCount));
        result.setRunning(ttsJobExecuteService.jobIsRunning(job));
        result.setFailed(BooleanUtils.isTrue(job.getFailed()));
        return result;
    }

    private int calculatePercent(int a, int b) {
        return a * 100 / Math.max(b, 1);
    }
}
