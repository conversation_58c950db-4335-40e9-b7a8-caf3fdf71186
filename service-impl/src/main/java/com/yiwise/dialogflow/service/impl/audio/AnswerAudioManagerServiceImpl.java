package com.yiwise.dialogflow.service.impl.audio;

import com.yiwise.base.common.text.TextPlaceholderElement;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.common.utils.file.MyFileUtils;
import com.yiwise.base.common.utils.file.ZipUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.middleware.redis.service.RedisOpsService;
import com.yiwise.dialogflow.api.dto.response.audio.AudioUploadResult;
import com.yiwise.dialogflow.common.OssKeyCenter;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.common.TempFilePathKeyCenter;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.engine.share.common.PauseSeparatorElement;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.entity.bo.SnapshotInvalidFailItemMsg;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.query.AnswerQuery;
import com.yiwise.dialogflow.entity.vo.SimpleUploadResultVO;
import com.yiwise.dialogflow.entity.vo.audio.*;
import com.yiwise.dialogflow.entity.vo.audio.request.AnswerAudioRequestVO;
import com.yiwise.dialogflow.entity.vo.audio.request.AnswerUpdateAudioRequestVO;
import com.yiwise.dialogflow.entity.vo.audio.request.BatchAdjustVolumeRequestVO;
import com.yiwise.dialogflow.entity.vo.audio.request.SyncAudioRequestVO;
import com.yiwise.dialogflow.helper.AddOssPrefixSerializer;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.operationlog.AnswerAudioOperationLogService;
import com.yiwise.dialogflow.utils.AnswerLocateUtils;
import com.yiwise.dialogflow.utils.AnswerTextUtils;
import com.yiwise.dialogflow.utils.SilenceAudioUtils;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import javaslang.Tuple;
import javaslang.Tuple2;
import javaslang.Tuple4;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AnswerAudioManagerServiceImpl implements AnswerAudioManagerService, RobotResourceService {
    @Resource
    private AnswerAudioMappingService answerAudioMappingService;

    @Resource
    private BotConfigService botConfigService;

    @Lazy
    @Resource
    private TtsJobService ttsJobService;

    @Resource
    private ObjectStorageHelper objectStorageHelper;

    @Lazy
    @Resource
    private AudioUploadService audioUploadService;

    @Resource
    private TtsJobExecuteService ttsJobExecuteService;

    @Lazy
    @Resource
    private BotService botService;

    @Resource
    private RedisOpsService redisOpsService;

    @Resource
    private AnswerManagerService answerManagerService;

    @Lazy
    @Resource
    private MiniAppAudioManagerService miniAppAudioManagerService;

    @Resource
    private AnswerAudioOperationLogService operationLogService;

    @Lazy
    @Resource
    private StepNodeService stepNodeService;

    @Lazy
    @Resource
    private StepService stepService;

    @Lazy
    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    @Resource
    private GroupService groupService;

    @Resource
    private PublicAudioService publicAudioService;

    @Resource
    private KnowledgeService knowledgeService;

    @Resource
    private BotRefService botRefService;

    @Resource
    private NlsService nlsService;

    @Override
    public Optional<LocalDateTime> getLastAudioCreateTime(Long botId) {
        return answerAudioMappingService.getLastMapping(botId, AudioTypeEnum.MAN_MADE)
                .map(AnswerAudioMappingPO::getUpdateTime);
    }

    private List<BaseAnswerContent> getAnswerContentList(List<StepPO> stepList,
                                                         List<DialogBaseNodePO> nodeList,
                                                         List<KnowledgePO> knowledgeList,
                                                         List<SpecialAnswerConfigPO> specialAnswerList) {
        List<BaseAnswerContent> resultList = new ArrayList<>();
        // 查询所有的流程和节点
        nodeList.forEach(node -> {
            if (CollectionUtils.isEmpty(node.getAnswerList())) {
                return;
            }
            resultList.addAll(node.getAnswerList());
        });

        // 查询所有的问答知识
        knowledgeList.forEach(knowledge -> {
            if (CollectionUtils.isEmpty(knowledge.getAnswerList())) {
                return;
            }
            resultList.addAll(knowledge.getAnswerList());
        });

        // 特殊配置
        specialAnswerList.forEach(special -> {
            if (EnabledStatusEnum.ENABLE.equals(special.getEnabledStatus())
                    && CollectionUtils.isNotEmpty(special.getAnswerList())) {
                resultList.addAll(special.getAnswerList());
            }
        });
        return resultList;
    }

    @Override
    public PageResultObject<AnswerAudioWrapVO> queryByCondition(AnswerAudioRequestVO request) {
        Long botId = request.getBotId();
        AnswerSourceEnum answerSource = request.getAnswerSource();
        String search = request.getSearch();
        Integer volume = request.getVolume();
        DialogFlowConditionOperationTypeEnum comparison = request.getComparison();
        List<RecordingResultsEnum> recordingResultsList = request.getRecordingResultsList();
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "机器人id不能为空");
        }
        Set<AnswerSourceEnum> sourceList = new HashSet<>();
        if (Objects.nonNull(answerSource)) {
            sourceList.add(answerSource);
        } else {
            sourceList.addAll(Arrays.asList(AnswerSourceEnum.values()));
        }

        List<AnswerAudioWrapVO> result = getByAnswerSourceList(botId, sourceList);

        String searchContent = StringUtils.trimToEmpty(search);
        if (StringUtils.isNotBlank(searchContent)) {
            result = result.stream()
                    .filter(item -> item.getText().contains(searchContent)
                            || item.getLocate().getDisplayName().contains(searchContent))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(recordingResultsList)) {
            result = result.stream().filter(item -> item.getAnswerElementList().stream()
                            .filter(ele -> TextPlaceholderTypeEnum.TEXT.equals(ele.getType()))
                            .map(AnswerPlaceholderElementVO::getRecordingResults).anyMatch(recordingResultsList::contains))
                    .collect(Collectors.toList());
        }
        if (Objects.nonNull(request.getStartTime()) && Objects.nonNull(request.getEndTime())) {
            result = result.stream().filter(item -> Objects.nonNull(item.getRecordingTime()))
                    .filter(item -> request.getStartTime().isBefore(item.getRecordingTime()) && request.getEndTime().isAfter(item.getRecordingTime()))
                    .collect(Collectors.toList());
        }
        if (Objects.nonNull(comparison) && Objects.nonNull(volume)) {
            result = result.stream().filter(item -> {
                if (CollectionUtils.isNotEmpty(item.getAudioSourceList())) {
                    return item.getAudioSourceList().stream().anyMatch(audio -> {
                                if (Objects.isNull(audio.getVolume())) {
                                    return false;
                                }
                                switch (comparison) {
                                    case LESS_OR_EQUAL:
                                        return audio.getVolume() <= volume;
                                    case EQUAL:
                                        return Objects.equals(audio.getVolume(), volume);
                                    case GREATER_OR_EQUAL:
                                        return audio.getVolume() >= volume;
                                    default:
                                        return false;
                                }
                            }
                    );
                }
                return false;
            }).collect(Collectors.toList());
        }
        if (Objects.nonNull(request.getRecordingTimeDirection())) {
            result.sort(
                    request.getRecordingTimeDirection() == Sort.Direction.DESC
                            ? Comparator.comparing(AnswerAudioWrapVO::getRecordingTime, Comparator.nullsFirst(LocalDateTime::compareTo)).reversed()
                            : Comparator.comparing(AnswerAudioWrapVO::getRecordingTime, Comparator.nullsLast(LocalDateTime::compareTo))
            );
        }
        return PageResultObject.of(result, 0, result.size(), result.size());
    }

    @Override
    public List<AnswerAudioWrapVO> queryByNodeId(Long botId, String stepId, String nodeId) {
        DialogBaseNodePO node = stepNodeService.getById(botId, stepId, nodeId);
        if (Objects.isNull(node) || CollectionUtils.isEmpty(node.getAnswerList())) {
            return Collections.emptyList();
        }

        StepPO step = stepService.getById(botId, node.getStepId());
        if (Objects.isNull(step)) {
            return Collections.emptyList();
        }

        List<BaseAnswerContentVO> answerList = node.getAnswerList()
                .stream()
                .filter(item -> StringUtils.isNotBlank(item.getText()))
                .map(item -> answerManagerService.convertToVO(item, AnswerLocateUtils.generate(step, node), node))
                .collect(Collectors.toList());
        return queryByAnswerTextList(botId, answerList);
    }

    @Override
    public List<AnswerAudioWrapVO> queryByKnowledgeId(Long botId, String knowledgeId) {
        KnowledgePO knowledge = knowledgeService.getById(botId, knowledgeId);
        if (Objects.isNull(knowledge) || CollectionUtils.isEmpty(knowledge.getAnswerList())) {
            return Collections.emptyList();
        }

        List<BaseAnswerContentVO> answerList = knowledge.getAnswerList()
                .stream()
                .filter(item -> StringUtils.isNotBlank(item.getText()))
                .map(item -> answerManagerService.convertToVO(item, AnswerLocateUtils.generate(knowledge)))
                .collect(Collectors.toList());
        return queryByAnswerTextList(botId, answerList);
    }

    @Override
    public List<AnswerAudioWrapVO> queryBySpecialAnswerId(Long botId, String specialAnswerId) {
        SpecialAnswerConfigPO specialAnswer = specialAnswerConfigService.getById(botId, specialAnswerId);
        if (Objects.isNull(specialAnswer) || CollectionUtils.isEmpty(specialAnswer.getAnswerList())) {
            return Collections.emptyList();
        }

        List<BaseAnswerContentVO> answerList = specialAnswer.getAnswerList()
                .stream()
                .filter(item -> StringUtils.isNotBlank(item.getText()))
                .map(item -> answerManagerService.convertToVO(item, AnswerLocateUtils.generate(specialAnswer)))
                .collect(Collectors.toList());
        return queryByAnswerTextList(botId, answerList);
    }

    private List<AnswerAudioWrapVO> queryByAnswerTextList(Long botId, List<BaseAnswerContentVO> answerList) {
        if (CollectionUtils.isEmpty(answerList)) {
            return Collections.emptyList();
        }

        BotAudioConfigPO config = botConfigService.getAudioConfig(botId);
        // 保证可以查出来答案列表
        Long recordUserId = config.getRecordUserId() == null ? 0L : config.getRecordUserId();

        List<String> textList = answerList.stream()
                .map(BaseAnswerContentVO::getText)
                .map(originText -> {
                    AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(originText, AudioTypeEnum.MIXTURE.equals(config.getAudioType()));
                    return splitter.getTextList();
                }).flatMap(Collection::stream)
                .collect(Collectors.toList());

        List<AnswerAudioMappingVO> audioMappingList = answerAudioMappingService.getVOListByTextList(config.getBotId(), recordUserId, config.getAudioType(), textList);
        Map<String, AnswerAudioMappingVO> mappingMap = MyCollectionUtils.listToMap(audioMappingList, AnswerAudioMappingPO::getText);

        List<AnswerAudioWrapVO> resultList = new ArrayList<>();
        for (BaseAnswerContentVO answerContent : answerList) {
            resultList.add(wrapAudioInfo(mappingMap, answerContent, config.getAudioType()));
        }
        return resultList;
    }

    /**
     * 包装单个答案音频内容
     */
    @Override
    public AnswerAudioWrapVO wrapSingleAnswerAudio(Long botId, AnswerLocateBO answerLocate) {
        BaseAnswerContentVO answer = answerManagerService.getAnswerContextByLocation(botId, answerLocate);
        if (Objects.isNull(answer)) {
            return null;
        }

        BotAudioConfigPO config = botConfigService.getAudioConfig(botId);

        // 获取音频列表?
        AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(answer.getText(), AudioTypeEnum.MIXTURE.equals(config.getAudioType()));
        List<String> subAnswerTextList = splitter.getTextList();

        List<AnswerAudioMappingVO> audioMappingList = answerAudioMappingService.getVOListByTextList(botId, config.getRecordUserId(), config.getAudioType(), subAnswerTextList);
        Map<String, AnswerAudioMappingVO> audioMappingMap = MyCollectionUtils.listToMap(audioMappingList, AnswerAudioMappingVO::getText);
        return wrapAudioInfo(audioMappingMap, answer, config.getAudioType());
    }

    private List<AnswerAudioWrapVO> getByAnswerSourceList(Long botId, Set<AnswerSourceEnum> answerSourceSet) {
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        if (CollectionUtils.isEmpty(answerSourceSet)) {
            return Collections.emptyList();
        }
        BotAudioConfigPO config = botConfigService.getAudioConfig(botId);
        // 保证可以查出来答案列表
        Long recordUserId = config.getRecordUserId() == null ? 0L : config.getRecordUserId();
        List<AnswerAudioMappingVO> audioMappingList = answerAudioMappingService.listAllAvailableAudioVO(config.getBotId(), recordUserId, config.getAudioType());
        Map<String, AnswerAudioMappingVO> mappingMap = MyCollectionUtils.listToMap(audioMappingList, AnswerAudioMappingPO::getText);

        List<AnswerAudioWrapVO> resultList = new ArrayList<>();


        AnswerQuery query = new AnswerQuery();
        query.setBotId(botId);
        query.setAnswerSourceSet(answerSourceSet);
        query.setSpecialAnswerConfigEnabledStatus(EnabledStatusEnum.ENABLE);
        List<BaseAnswerContentVO> answerContentVOList = answerManagerService.queryByCondition(query);
        for (BaseAnswerContentVO answerContent : answerContentVOList) {
            resultList.add(wrapAudioInfo(mappingMap, answerContent, config.getAudioType()));
        }
        return resultList;
    }

    @Override
    public List<AnswerAudioWrapVO> getAllByBotId(Long botId) {
        return getByAnswerSourceList(botId, Arrays.stream(AnswerSourceEnum.values()).collect(Collectors.toSet()));
    }

    @Override
    public List<AnswerAudioWrapVO> getByRobotSnapshot(Long botId, RobotSnapshotPO snapshot) {
        BotAudioConfigPO config = snapshot.getBotAudioConfig();
        List<AnswerAudioMappingVO> audioMappingList = answerAudioMappingService.listAllAvailableAudioVO(config.getBotId(), config.getRecordUserId(), config.getAudioType());
        Map<String, AnswerAudioMappingVO> mappingMap = MyCollectionUtils.listToMap(audioMappingList, AnswerAudioMappingPO::getText);

        List<BaseAnswerContentVO> answerContentList = answerManagerService.getAnswerFromSnapshot(snapshot);

        List<AnswerAudioWrapVO> resultList = new ArrayList<>();
        for (BaseAnswerContentVO answerContent : answerContentList) {
            resultList.add(wrapAudioInfo(mappingMap, answerContent, config.getAudioType()));
        }
        return resultList;
    }

    @Override
    public TtsJobPO startCompose(Long botId, Long userId) {
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        BotAudioConfigPO config = botConfigService.getAudioConfig(botId);
        if (Objects.isNull(config)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "音频配置缺失");
        }

        if (ttsJobService.lastJobIsRunning(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "合成任务正在运行中");
        }

        Tuple4<Set<String>, Set<String>, Set<String>, Set<String>> parseData = prepareComposeData(config);

        Set<String> allAnswerSet = parseData._1;
        Set<String> needComposeSet = parseData._2;

        return ttsJobService.create(config, allAnswerSet.size(), needComposeSet, userId, CollectionUtils.size(parseData._3));
    }

    private Tuple4<Set<String>, Set<String>, Set<String>, Set<String>> prepareComposeData(BotAudioConfigPO botAudioConfig) {

        List<AnswerAudioWrapVO> audioList = getAllByBotId(botAudioConfig.getBotId());

        Set<String> allAnswerSet = new HashSet<>();
        Set<String> needComposeSet = new HashSet<>();
        // 已经真人录音的文本集合
        Set<String> manMadeCompletedTextSet = new HashSet<>();
        // 已经tts合成的文本集合
        Set<String> ttsCompletedTextSet = new HashSet<>();
        // 包含变量的答案是在播放时整句合成的, 所以在提前合成阶段无需合成
        audioList.forEach(answer -> {
            answer.getAnswerElementList().forEach(element -> {
                if (TextPlaceholderTypeEnum.TEXT.equals(element.getType()) && !element.getValue().trim().isEmpty()) {
                    allAnswerSet.add(element.getValue());
                    if (StringUtils.isBlank(element.getUrl())) {
                        needComposeSet.add(element.getValue());
                    } else {
                        if (AudioTypeEnum.COMPOSE.equals(element.getAudioType())) {
                            ttsCompletedTextSet.add(element.getValue());
                        } else {
                            manMadeCompletedTextSet.add(element.getValue());
                        }
                    }
                }
            });
        });
        return Tuple.of(allAnswerSet, needComposeSet, manMadeCompletedTextSet, ttsCompletedTextSet);
    }

    @Override
    public AudioCompleteProgressVO getAudioCompleteProgress(Long botId) {
        AudioCompleteProgressVO cache = redisOpsService.get(RedisKeyCenter.getBotAudioCompleteProgressKey(botId), AudioCompleteProgressVO.class);
        if (Objects.nonNull(cache)) {
            log.info("从缓存中加载处理进度结果");
            return cache;
        }
        AudioCompleteProgressVO result = doCalculateAudioCompleteProgress(botId);
        redisOpsService.set(RedisKeyCenter.getBotAudioCompleteProgressKey(botId), result, 1L, TimeUnit.HOURS);
        return result;
    }

    private AudioCompleteProgressVO doCalculateAudioCompleteProgress(Long botId) {
        List<String> allAnswerList = new ArrayList<>();
        List<String> allCompleteList = new ArrayList<>();
        List<String> manMadeCompleteList = new ArrayList<>();
        List<String> ttsCompleteList = new ArrayList<>();
        List<String> allReadyList = new ArrayList<>();

        List<AnswerAudioWrapVO> audioWrapList = getAllByBotId(botId);

        audioWrapList.forEach(answerAudio -> {
            if (CollectionUtils.isNotEmpty(answerAudio.getAnswerElementList())) {
                answerAudio.getAnswerElementList().forEach(element -> {
                    if (TextPlaceholderTypeEnum.TEXT.equals(element.getType())) {
                        String text = answerTrim(element.getValue());
                        allAnswerList.add(text);
                        if (answerAudio.getCompletedSentenceSet().contains(text)) {
                            allCompleteList.add(text);
                        } else {
                            allReadyList.add(text);
                        }
                        if (answerAudio.getManMadeCompletedSentenceSet().contains(text)) {
                            manMadeCompleteList.add(text);
                        }
                        if (answerAudio.getTtsCompletedSentenceSet().contains(text)) {
                            ttsCompleteList.add(text);
                        }
                    }
                });
            }
        });

        return calculateProgress(allAnswerList, allCompleteList, allReadyList, manMadeCompleteList, ttsCompleteList);
    }

    @Override
    public void clearAudioCompleteProgressCache(Long botId) {
        log.info("清除机器人:{}录音进度缓存结果", botId);
        redisOpsService.delete(RedisKeyCenter.getBotAudioCompleteProgressKey(botId));
    }

    private AudioCompleteProgressVO calculateProgress(List<String> allAnswerList, List<String> allCompleteList, List<String> allReadyList, List<String> manMadeCompleteList, List<String> ttsCompleteList) {
        AudioCompleteProgressVO result = new AudioCompleteProgressVO();

        // 统计句数
        result.setTotalCount(CollectionUtils.size(allAnswerList));
        result.setCompletedCount(CollectionUtils.size(allCompleteList));
        result.setManMadeCompletedCount(CollectionUtils.size(manMadeCompleteList));
        result.setTtsCompletedCount(CollectionUtils.size(ttsCompleteList));
        result.setIncompleteCount(result.getTotalCount() - result.getCompletedCount());

        // 统计去重后句数
        result.setDistTotalCount(CollectionUtils.size(new HashSet<>(allAnswerList)));
        result.setDistCompletedCount(CollectionUtils.size(new HashSet<>(allCompleteList)));
        result.setDistManMadeCompletedCount(CollectionUtils.size(new HashSet<>(manMadeCompleteList)));
        result.setDistTtsCompletedCount(CollectionUtils.size(new HashSet<>(ttsCompleteList)));
        result.setDistIncompleteCount(result.getDistTotalCount() - result.getDistCompletedCount());

        // 统计字数
        result.setTotalWordCount(allAnswerList.stream().mapToInt(StringUtils::length).sum());
        result.setCompletedWordCount(allCompleteList.stream().mapToInt(StringUtils::length).sum());
        result.setManMadeCompletedWordCount(manMadeCompleteList.stream().mapToInt(StringUtils::length).sum());
        result.setTtsCompletedWordCount(ttsCompleteList.stream().mapToInt(StringUtils::length).sum());
        result.setIncompleteWordCount(result.getTotalWordCount() - result.getCompletedWordCount());

        // 统计去重后字数
        result.setDistTotalWordCount(allAnswerList.stream().distinct().mapToInt(StringUtils::length).sum());
        result.setDistCompletedWordCount(allCompleteList.stream().distinct().mapToInt(StringUtils::length).sum());
        result.setDistManMadeCompletedWordCount(manMadeCompleteList.stream().distinct().mapToInt(StringUtils::length).sum());
        result.setDistTtsCompletedWordCount(ttsCompleteList.stream().distinct().mapToInt(StringUtils::length).sum());
        result.setDistIncompleteWordCount(result.getDistTotalWordCount() - result.getDistCompletedWordCount());

        result.setProcessingSet(new HashSet<>(allReadyList));
        result.setCompletedPercent(calculatePercent(CollectionUtils.size(allCompleteList), CollectionUtils.size(allAnswerList)));
        result.setManMadeCompletedPercent(calculatePercent(CollectionUtils.size(manMadeCompleteList), CollectionUtils.size(allAnswerList)));
        result.setTtsCompletedPercent(calculatePercent(CollectionUtils.size(ttsCompleteList), CollectionUtils.size(allAnswerList)));
        return result;
    }

    private int calculatePercent(int a, int b) {
        return 100 * a / Math.max(b, 1);
    }

    private static void validRecordUser(BotAudioConfigPO audioConfig) {
        if (!AudioTypeEnum.isExtendManMade(audioConfig.getAudioType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "非真人录音, 不支持上传录音");
        }
        if (Objects.isNull(audioConfig.getRecordUserId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "未选择录音师, 请先选择录音师之后再上传");
        }
    }

    @Override
    public void updateAnswerAudio(AnswerUpdateAudioRequestVO request, Long userId) {
        if (StringUtils.isBlank(request.getAnswerText())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "录音文本不能为空");
        }
        if (Objects.isNull(request.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        if (StringUtils.isBlank(request.getUrl())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "relativeUrl不能为空");
        }

        BotAudioConfigPO audioConfig = botConfigService.getAudioConfig(request.getBotId());
        validRecordUser(audioConfig);

        // 把录音文件存入当前录音师的录音映射文件下面
        // 这里面其实也会存在一个问题, 就是录音师切换了, 但是已经完成的录音不想切换
        // 这里目前先对齐目前1.0的逻辑, 在录音师切换的时候, 先把录音复制出来一份

        log.info("更新 [{}] 录音 relativeUrl: {}, fullUrl: {}", request.getAnswerText(), request.getUrl(), request.getFullUrl());
        answerAudioMappingService.upsertAudioMapping(request.getBotId(),
                audioConfig.getRecordUserId(),
                AudioTypeEnum.MAN_MADE,
                request.getAnswerText(),
                request.getUrl(),
                request.getVolume(),
                request.getDuration());

        // 添加操作日志
        operationLogService.uploadAnswerAudio(request.getBotId(), Collections.singletonList(request.getAnswerText()), userId);

        // 异步录音文件识别
        nlsService.asyncFlashRecognizer(request.getBotId(), request.getUrl());
    }

    @Override
    public void copyAllToOtherRecorder(Long botId, Long oldUserId, Long newUserId) {
        List<AnswerAudioWrapVO> allAnswerList = getAllByBotId(botId);
        Set<String> answerSet = new HashSet<>();
        for (AnswerAudioWrapVO wrap : allAnswerList) {
            if (CollectionUtils.isNotEmpty(wrap.getCompletedSentenceSet())) {
                answerSet.addAll(wrap.getCompletedSentenceSet());
            }
        }
        answerAudioMappingService.copyAudioMapping(botId, AudioTypeEnum.MAN_MADE, oldUserId, newUserId, answerSet);
    }

    private Set<String> getAnswerSetByBotAudioConfig(List<BaseAnswerContentVO> contentList, BotAudioConfigPO config) {
        Set<String> answerSet = new HashSet<>();
        for (BaseAnswerContentVO content : contentList) {
            if (StringUtils.isNotBlank(content.getText())) {
                AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(content.getText(), AudioTypeEnum.MIXTURE.equals(config.getAudioType()));
                answerSet.addAll(splitter.getTextList());
            }
        }
        return answerSet.stream()
                .map(this::answerTrim)
                .collect(Collectors.toSet());
    }

    @Override
    public Integer copyAllOnCopyBot(Long fromBotId, Long fromUserId, Long toBotId, Long toUserId, Long operationUserId, List<String> filterAnswerIdList, SyncModeEnum syncMode) {
        AnswerQuery query = new AnswerQuery();
        query.setBotId(fromBotId);
        BotAudioConfigPO fromConfig = botConfigService.getAudioConfig(fromBotId);
        BotAudioConfigPO toConfig = botConfigService.getAudioConfig(toBotId);
        List<BaseAnswerContentVO> fromContentList = answerManagerService.queryByCondition(query);
        if (CollectionUtils.isNotEmpty(filterAnswerIdList)) {
            fromContentList = fromContentList.stream().filter(content -> filterAnswerIdList.contains(content.getUniqueId())).collect(Collectors.toList());
        }
        query.setBotId(toBotId);
        List<BaseAnswerContentVO> toContentList = answerManagerService.queryByCondition(query);

        Set<String> fromAnswerSet = getAnswerSetByBotAudioConfig(fromContentList, fromConfig);
        Set<String> toAnswerSet = getAnswerSetByBotAudioConfig(toContentList, toConfig);

        // 取交集
        fromAnswerSet.retainAll(toAnswerSet);

        Integer result = answerAudioMappingService.copyAllOnCopyBot(fromBotId, fromUserId, toBotId, toUserId, fromAnswerSet, operationUserId, syncMode);
        return result;
    }

    @Override
    public String batchAdjustVolume(BatchAdjustVolumeRequestVO request, Long userId) {
        if (CollectionUtils.isEmpty(request.getUrlList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "选择的话术内容没有音频");
        }
        List<String> handleList = request.getUrlList().stream().distinct().filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        miniAppAudioManagerService.batchAdjustVolume(request.getBotId(), null, request.getVolume(), handleList, userId);
        return String.format("成功调节%s条音频的音量", handleList.size());
    }

    @Override
    public AudioUploadResultVO adjustVolume(Long botId, String url, Integer volume) {
        // 调整录音文件的volume, 并重新上传到oss
        AudioUploadResultVO adjustResult = audioUploadService.adjustVolume(botId, url, volume);

        // 更新音频文件映射里面的volume值
        answerAudioMappingService.adjustVolume(botId, url, adjustResult.getVolume());
        return adjustResult;
    }

    @Override
    public AudioBatchUploadResultVO batchUploadAudio(Long botId, MultipartFile file, Long userId) {
        // 批量上传录音文件
        BotAudioConfigPO botAudioConfig = botConfigService.getAudioConfig(botId);
        if (!AudioTypeEnum.isExtendManMade(botAudioConfig.getAudioType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "非真人录音/整句合成, 不支持上传录音");
        }
        // 查询所有的录音
        AnswerQuery query = new AnswerQuery();
        query.setBotId(botId);
        List<? extends BaseAnswerContent> answerContentList = answerManagerService.queryByCondition(query);
        Map<String, String> answerLabel2ContentMap = parseAnswer(botAudioConfig, answerContentList);

        // 调用上传接口
        AudioBatchUploadResultVO batchResult = new AudioBatchUploadResultVO();
        Map<String, AudioUploadResultVO> audioUploadResultMap = audioUploadService.batchUploadAudio(botId, file, answerLabel2ContentMap, batchResult);
        audioUploadResultMap.forEach((answer, result) -> {
            answerAudioMappingService.upsertAudioMapping(botId, botAudioConfig.getRecordUserId(),
                    botAudioConfig.getAudioType(), answer, result.getUrl(), result.getVolume());
        });
        operationLogService.batchUpload(botId, audioUploadResultMap, userId);
        return batchResult;
    }

    @Override
    public void importAudio(Long botId, Long userId, String audioDirPath) {
        // 批量上传录音文件
        BotAudioConfigPO botAudioConfig = botConfigService.getAudioConfig(botId);
        if (!AudioTypeEnum.isExtendManMade(botAudioConfig.getAudioType())) {
            return;
        }
        // 查询所有的录音
        AnswerQuery query = new AnswerQuery();
        query.setBotId(botId);
        List<? extends BaseAnswerContent> answerContentList = answerManagerService.queryByCondition(query);
        Map<String, String> answerLabel2ContentMap = parseAnswer(botAudioConfig, answerContentList);

        // 调用上传接口
        AudioBatchUploadResultVO batchResult = new AudioBatchUploadResultVO();
        Map<String, AudioUploadResultVO> audioUploadResultMap = audioUploadService.batchUploadAudio(botId, audioDirPath, answerLabel2ContentMap, batchResult);
        audioUploadResultMap.forEach((answer, result) -> {
            answerAudioMappingService.upsertAudioMapping(botId, botAudioConfig.getRecordUserId(),
                    botAudioConfig.getAudioType(), answer, result.getUrl(), result.getVolume());
        });
        operationLogService.batchUpload(botId, audioUploadResultMap, userId);
    }

    @Override
    public SimpleUploadResultVO downloadAudioZip(Long botId) {
        List<AnswerAudioWrapVO> allAudioList = getAllByBotId(botId);
        return downloadZip(botId, allAudioList);
    }

    @Override
    public void downloadAllAudioToDirPath(Long botId, String dirPath) {
        List<AnswerAudioWrapVO> allAudioList = getAllByBotId(botId);
        downloadAudioToLocal(botId, allAudioList, dirPath);
    }

    @Override
    public void downloadSingleAudio(HttpServletResponse response, String ossKey, String label) {
        File file = null;
        try {
            file = audioUploadService.downloadRemoteAudio(ossKey, label);
            response.reset();
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.getMimeType());
            response.setContentLength((int) file.length());
            response.setHeader("Content-Disposition", String.format("attachment;filename=%s", file.getName()));
            try (BufferedInputStream bis = new BufferedInputStream(Files.newInputStream(file.toPath()));) {
                byte[] buff = new byte[1024];
                OutputStream os = response.getOutputStream();
                int i = 0;
                while ((i = bis.read(buff)) != -1) {
                    os.write(buff, 0, i);
                    os.flush();
                }
            } catch (IOException e) {
                throw new ComException(ComErrorCode.UNKNOWN_ERROR, "文件下载失败", e);
            }
        } finally {
            if (file != null) {
                MyFileUtils.deleteFileByPath(file.getPath());
            }
        }
    }

    @Override
    public SimpleUploadResultVO downloadZip(Long botId, List<AnswerAudioWrapVO> allAudioList) {
        SimpleUploadResultVO result = new SimpleUploadResultVO();
        if (CollectionUtils.isEmpty(allAudioList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "需要下载的答案列表不能为空");
        }
        // 获取所有要下载的url
        Map<String, String> label2urlMap = new HashMap<>();
        for (AnswerAudioWrapVO wrap : allAudioList) {
            AtomicInteger index = new AtomicInteger(1);
            boolean containsVariable = CollectionUtils.size(wrap.getAnswerElementList()) != 1;
            wrap.getAnswerElementList().stream()
                    .filter(item -> TextPlaceholderTypeEnum.TEXT.equals(item.getType()))
                    .forEach(item -> {
                        int number = index.getAndIncrement();
                        if (StringUtils.isNotBlank(item.getUrl())) {
                            String label = containsVariable ? String.format("%s-%s", wrap.getLabel(), number) : wrap.getLabel();
                            label2urlMap.put(label, item.getUrl());
                        }
                    });
        }

        if (MapUtils.isEmpty(label2urlMap)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "选择的话术内容没有音频");
        }
        File file = null;
        String ossKey = null;
        // 下载录音文件到本地
        String audioLocalFilePath = TempFilePathKeyCenter.getDownloadAudioFileDirPath(botId);
        String zipFileDirPath = TempFilePathKeyCenter.getDownloadAudioZipFileDirPath(botId);
        String zipFilePath = String.format("%s/%s.zip", zipFileDirPath, botId);
        try {
            if (label2urlMap.size() == 1) {
                for (Map.Entry<String, String> entry : label2urlMap.entrySet()) {
                    file = audioUploadService.downloadRemoteAudio(entry.getValue(), entry.getKey());
                    ossKey = OssKeyCenter.getDownloadAudioOssKey(botId, entry.getKey());
                    result.setFileName(file.getName());
                }
            } else {
                audioUploadService.downAudioToLocal(botId, audioLocalFilePath, label2urlMap);

                File zipFileDir = new File(zipFileDirPath);
                if (!zipFileDir.exists()) {
                    zipFileDir.mkdirs();
                }
                // 压缩文件
                ZipUtils.zipDir(audioLocalFilePath, zipFilePath);

                file = new File(zipFilePath);
                ossKey = OssKeyCenter.getDownloadAudioZipOssKey(botId);
                result.setFileName(String.format("%s_%s.zip", botId, "音频导出"));
            }
            String fullUrl = objectStorageHelper.upload(ossKey, file);
            result.setUrl(ossKey);
            result.setFullUrl(fullUrl);
            result.setBotId(botId);
            result.setSum(label2urlMap.size());
            return result;
        } finally {
            // 删除本地文件
            MyFileUtils.deleteFileByPath(audioLocalFilePath);
            if (file != null) {
                MyFileUtils.deleteFileByPath(file.getPath());
            }
        }
    }

    private void downloadAudioToLocal(Long botId,
                                      List<AnswerAudioWrapVO> allAudioList,
                                      String localDirPath) {
        if (CollectionUtils.isEmpty(allAudioList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "需要下载的答案列表不能为空");
        }
        // 获取所有要下载的url
        Map<String, String> label2urlMap = new HashMap<>();
        for (AnswerAudioWrapVO wrap : allAudioList) {
            AtomicInteger index = new AtomicInteger(1);
            boolean containsVariable = CollectionUtils.size(wrap.getAnswerElementList()) != 1;
            wrap.getAnswerElementList().stream()
                    .filter(item -> TextPlaceholderTypeEnum.TEXT.equals(item.getType()))
                    .forEach(item -> {
                        int number = index.getAndIncrement();
                        if (StringUtils.isNotBlank(item.getUrl())) {
                            String label = containsVariable ? String.format("%s-%s", wrap.getLabel(), number) : wrap.getLabel();
                            label2urlMap.put(label, item.getUrl());
                        }
                    });
        }

        if (MapUtils.isEmpty(label2urlMap)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "选择的话术内容没有音频");
        }
        audioUploadService.downAudioToLocal(botId, localDirPath, label2urlMap);
    }


    @Override
    public TtsComposeProgressVO getTtsCompleteProgress(Long botId) {
        BotAudioConfigPO config = botConfigService.getAudioConfig(botId);
        Tuple4<Set<String>, Set<String>, Set<String>, Set<String>> completeData = prepareComposeData(config);
        Set<String> allAnswerTextSet = completeData._1;
        Set<String> needComposeSet = completeData._2;
        int totalCount = CollectionUtils.size(allAnswerTextSet);
        int needCount = CollectionUtils.size(needComposeSet);
        int manMadeCompletedCount = CollectionUtils.size(completeData._3);
        int ttsCompletedCount = CollectionUtils.size(completeData._4);
        int completedCount = totalCount - needCount;

        TtsComposeProgressVO result = new TtsComposeProgressVO();
        result.setBotId(botId);
        result.setTotalCount(totalCount);
        result.setCompletedCount(completedCount);
        result.setPercent(calculatePercent(completedCount, totalCount));
        result.setManMadePercent(calculatePercent(manMadeCompletedCount, totalCount));
        result.setTtsPercent(calculatePercent(ttsCompletedCount, totalCount));
        TtsJobPO lastJob = ttsJobService.getLastJob(botId);
        result.setFailed(Objects.nonNull(lastJob) && BooleanUtils.isTrue(lastJob.getFailed()));
        result.setRunning(ttsJobExecuteService.jobIsRunning(lastJob));
        return result;
    }

    @Override
    public Boolean copyAudioByAnswerSource(Long fromBotId, Long toBotId, List<? extends Labelled> labelledList, Long userId) {
        try {
            Tuple2<Long, Long> fromAndToRecordUserId = validAudioInfo(fromBotId, toBotId);

            BotAudioConfigPO fromConfig = botConfigService.getAudioConfig(fromBotId);
            BotAudioConfigPO toConfig = botConfigService.getAudioConfig(toBotId);

            AnswerQuery query = new AnswerQuery();
            query.setBotId(fromBotId);
            query.setLabelledList(labelledList);

            List<BaseAnswerContentVO> contentList = answerManagerService.queryByCondition(query);
            Set<String> fromAnswerSet = getAnswerSetByBotAudioConfig(contentList, fromConfig);

//        query.setBotId(toBotId);
//        List<BaseAnswerContentVO> toContentList = answerManagerService.queryByCondition(query);
//        Set<String> toAnswerSet = getAnswerSetByBotAudioConfig(toContentList, toConfig);

//        // 取交集
//        fromAnswerSet.retainAll(toAnswerSet);

            answerAudioMappingService.copyAllOnCopyBot(fromBotId, fromAndToRecordUserId._1, toBotId, fromAndToRecordUserId._2, fromAnswerSet, userId, SyncModeEnum.COVER);
        } catch (Exception e) {
            log.warn("录音未同步：{}", e.getMessage());
            return false;
        }
        return true;
    }

    private Tuple2<Long, Long> validAudioInfo(Long fromBotId, Long toBotId) {
        if (Objects.isNull(fromBotId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "来源机器人不能为空");
        }
        if (Objects.isNull(toBotId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "目标机器人不能为空");
        }
        if (fromBotId.equals(toBotId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "不能自己复制给自己");
        }
        BotAudioConfigPO fromConfig = botConfigService.getAudioConfig(fromBotId);
        BotAudioConfigPO toConfig = botConfigService.getAudioConfig(toBotId);
        if (AudioTypeEnum.COMPOSE.equals(fromConfig.getAudioType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "来源机器人为合成音, 不支持复制");
        }
        if (AudioTypeEnum.COMPOSE.equals(toConfig.getAudioType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "目标机器人为合成音, 不支持复制");
        }
        if (Objects.isNull(fromConfig.getRecordUserId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "来源机器人未设置录音师");
        }

        if (Objects.isNull(toConfig.getRecordUserId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "目标机器人未设置录音师");
        }
        if (!Objects.equals(fromConfig.getRecordUserId(), toConfig.getRecordUserId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "来源机器人和目标机器人录音师设置不一致");
        }
        return new Tuple2<>(fromConfig.getRecordUserId(), fromConfig.getRecordUserId());
    }

    @Override
    public Integer copyAudio(Long fromBotId, Long toBotId, Long operationUserId) {
        Tuple2<Long, Long> fromAndToRecordUserId = validAudioInfo(fromBotId, toBotId);

        return copyAllOnCopyBot(fromBotId, fromAndToRecordUserId._1, toBotId, fromAndToRecordUserId._2, operationUserId, null, SyncModeEnum.COVER);
    }

    @Override
    public void batchSyncAudio(BatchSyncAudioRequestVO request, Long userId) {
        Long fromBotId = request.getSrcBotId();
        List<Long> targetBotIdList = request.getTargetBotIdList();
        if (targetBotIdList.contains(fromBotId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "目标bot不能包含自己");
        }
        for (Long toBotId : targetBotIdList) {
            try {
                Tuple2<Long, Long> fromAndToRecordUserId = validAudioInfo(fromBotId, toBotId);
                copyAllOnCopyBot(fromBotId, fromAndToRecordUserId._1, toBotId, fromAndToRecordUserId._2, userId, request.getAnswerIdList(), request.getSyncMode());
            } catch (ComException ignore) {
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    @Override
    public void resetAllManMadeAudio(Long botId, Long userId) {
        BotAudioConfigPO config = botConfigService.getAudioConfig(botId);
        if (AudioTypeEnum.COMPOSE.equals(config.getAudioType())) {
            answerAudioMappingService.deleteAllComposeAudio(botId);
        } else {
            if (Objects.isNull(config.getRecordUserId())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "未设置录音师, 无法重置");
            }
            answerAudioMappingService.deleteAllByRecordUserId(botId, config.getRecordUserId());
        }
        updateToDraft(botId);
        operationLogService.resetAllAnswerAudio(botId, config.getAudioType(), userId);
    }

    private void updateToDraft(Long botId) {
        botService.updateAuditStatus(botId, AuditStatusEnum.DRAFT);
    }

    @Override
    public void resetByAnswer(Long botId, List<String> answerTextList, Long userId) {
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        if (CollectionUtils.isEmpty(answerTextList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "选择的话术内容没有音频");
        }
        BotAudioConfigPO config = botConfigService.getAudioConfig(botId);

        answerAudioMappingService.deleteByAnswerList(botId, answerTextList);
        updateToDraft(botId);
        operationLogService.resetAnswerAudio(botId, answerTextList, userId);
    }

    private Map<String, String> parseAnswer(BotAudioConfigPO botAudioConfig, List<? extends BaseAnswerContent> answerContentList) {
        Map<String, String> answerLabel2ContentMap = new HashMap<>();
        for (BaseAnswerContent answer : answerContentList) {
            if (StringUtils.isNotBlank(answer.getLabel())) {
                AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(answer.getText(), AudioTypeEnum.MIXTURE.equals(botAudioConfig.getAudioType()));
                if (CollectionUtils.isEmpty(splitter.getPlaceholderList())) {
                    answerLabel2ContentMap.put(answer.getLabel(), answer.getText());
                } else {
                    for (int i = 0; i < splitter.getTextList().size(); i++) {
                        String answerContent = splitter.getTextList().get(i);
                        answerLabel2ContentMap.put(String.format("%s-%s", answer.getLabel(), i + 1), answerTrim(answerContent));
                    }
                }
            }
        }
        return answerLabel2ContentMap;
    }

    private AnswerLocateBO createLocate(KnowledgePO knowledge) {
        return AnswerLocateUtils.generate(knowledge);
    }

    private AnswerLocateBO createLocate(SpecialAnswerConfigPO config) {
        return AnswerLocateUtils.generate(config);
    }

    private AnswerLocateBO createLocate(StepPO step, DialogBaseNodePO node) {
        return AnswerLocateUtils.generate(step, node);
    }

    @Override
    public AnswerAudioWrapVO convertAnswer(Map<String, AnswerAudioMappingVO> textAudioStorageMap,
                                           AnswerLocateBO locate,
                                           BaseAnswerContent answer,
                                           AudioTypeEnum audioType) {
        AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(answer.getText(), AudioTypeEnum.MIXTURE.equals(audioType));
        List<AnswerAudioMappingVO> audioSourceList = new ArrayList<>();
        AtomicInteger index = new AtomicInteger(1);
        List<AnswerPlaceholderElementVO> answerPlaceholderElementList = splitter.getTextPlaceholderList().stream()
                .filter(item -> !TextPlaceholderTypeEnum.SEPARATOR.equals(item.getType()) || item instanceof PauseSeparatorElement )
                .map(item -> convert(item, textAudioStorageMap, audioSourceList, answer.getLabel(), CollectionUtils.size(splitter.getTextList()), index))
                .collect(Collectors.toList());
        // 已经录音完成的文案集合
        Set<String> completedSentenceSet = new HashSet<>();
        // 真人录音的文案集合
        Set<String> manMadeCompletedSentenceSet = new HashSet<>();
        // tts合成的文案集合
        Set<String> ttsCompletedSentenceSet = new HashSet<>();
        boolean completed = answerPlaceholderElementList.stream()
                .filter(item -> TextPlaceholderTypeEnum.TEXT.equals(item.getType()))
                .allMatch(item -> StringUtils.isNotBlank(item.getUrl()) || item.getValue().trim().isEmpty());
        answerPlaceholderElementList.stream().filter(item -> TextPlaceholderTypeEnum.TEXT.equals(item.getType()))
                .forEach(item -> {
                    if (StringUtils.isNotBlank(item.getUrl())) {
                        completedSentenceSet.add(answerTrim(item.getValue()));
                        if (AudioTypeEnum.COMPOSE.equals(item.getAudioType())) {
                            ttsCompletedSentenceSet.add(answerTrim(item.getValue()));
                        } else {
                            manMadeCompletedSentenceSet.add(answerTrim(item.getValue()));
                        }
                    }
                });
        List<Integer> volumeList = audioSourceList.stream()
                .map(AnswerAudioMappingPO::getVolume)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        AnswerAudioWrapVO wrap = MyBeanUtils.copy(answer, AnswerAudioWrapVO.class);
        locate.setAnswerId(answer.getUniqueId());
        wrap.setLocate(locate);
        wrap.setAnswerElementList(answerPlaceholderElementList);
        wrap.setAudioSourceList(audioSourceList);
        wrap.setCompleted(completed);
        wrap.setCompletedSentenceSet(completedSentenceSet);
        wrap.setManMadeCompletedSentenceSet(manMadeCompletedSentenceSet);
        wrap.setTtsCompletedSentenceSet(ttsCompletedSentenceSet);
        if (CollectionUtils.isNotEmpty(volumeList)) {
            int avg = volumeList.stream().mapToInt(Integer::intValue).sum() / volumeList.size();
            wrap.setVolume(avg);
        }
        return wrap;
    }

    @Override
    public AnswerAudioWrapVO convertAnswer(Map<String, AnswerAudioMappingVO> textAudioStorageMap, StepPO step, DialogBaseNodePO node, BaseAnswerContent answer, AudioTypeEnum audioType) {
        AnswerAudioWrapVO result = convertAnswer(textAudioStorageMap, createLocate(step, node), answer, audioType);
        if (node instanceof DialogJumpNodePO) {
            DialogJumpNodePO jumpNode = (DialogJumpNodePO) node;
            if (Objects.nonNull(jumpNode.getJumpType())) {
                result.setAction(jumpNode.getJumpType().getDesc());
            }
        }
        return result;
    }

    public AnswerAudioWrapVO wrapAudioInfo(Map<String, AnswerAudioMappingVO> textAudioStorageMap,
                                           BaseAnswerContentVO answer,
                                           AudioTypeEnum audioType) {
        return convertAnswer(textAudioStorageMap, answer.getLocate(), answer, audioType);
    }

    private AnswerPlaceholderElementVO convert(TextPlaceholderElement item,
                                             Map<String, AnswerAudioMappingVO> textAudioStorageMap,
                                             List<AnswerAudioMappingVO> refAudioMappingList,
                                             String parentLabel,
                                             Integer brotherSize,
                                             AtomicInteger index) {

        AnswerPlaceholderElementVO result = MyBeanUtils.copy(item, AnswerPlaceholderElementVO.class);
        if (TextPlaceholderTypeEnum.TEXT.equals(item.getType())) {
            AnswerAudioMappingVO urlStorageInfo = textAudioStorageMap.get(answerTrim(item.getValue()));
            if (Objects.nonNull(urlStorageInfo) && StringUtils.isNotBlank(urlStorageInfo.getUrl())) {
                result.setUrl(urlStorageInfo.getUrl());
                result.setVolume(urlStorageInfo.getVolume());
                result.setFullUrl(urlStorageInfo.getFullUrl());
                result.setDuration(urlStorageInfo.getDuration());
                result.setAudioType(urlStorageInfo.getAudioType());
                result.setRecordingTime(urlStorageInfo.getUpdateTime());
                refAudioMappingList.add(urlStorageInfo);
            }
            if (brotherSize <= 1) {
                result.setLabel(parentLabel);
            } else {
                result.setLabel(String.format("%s-%s", parentLabel, index.getAndIncrement()));
            }
        }
        if (TextPlaceholderTypeEnum.SEPARATOR.equals(item.getType()) && item instanceof PauseSeparatorElement) {
            String ossKey = SilenceAudioUtils.makeSilenceAudioAndUploadIfNotExists(((PauseSeparatorElement) item).getPauseMs());
            result.setUrl(ossKey);
            result.setFullUrl(AddOssPrefixSerializer.getAddOssPrefixUrl(ossKey));
        }
        return result;
    }


    private String answerTrim(String text) {
        return AnswerTextUtils.removeAnswerPrefixAndSuffixSymbols(text);
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        // 在校验的时候设置
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {

    }

    @Override
    public void validateResource(RobotResourceContext context) {
        if (BooleanUtils.isTrue(context.getValidateConfig().getRequireValidAudio())) {
            List<AnswerAudioWrapVO> answerAudioList = getByRobotSnapshot(context.getSrcBotId(), context.getSnapshot());
            try {
                validAndSetAudio(context.getSnapshot(), answerAudioList);
            } catch (ComException e) {
                SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg.builder()
                        .resourceType(BotResourceTypeEnum.AUDIO)
                        .failMsg(e.getDetailMsg())
                        .build();
                context.getInvalidMsgList().add(msg);
            }
        }
    }

    private void validAndSetAudio(RobotSnapshotPO snapshot, List<AnswerAudioWrapVO> answerAudioList) {
        boolean allComplete = answerAudioList.stream()
                .map(AnswerAudioWrapVO::getCompleted)
                .allMatch(BooleanUtils::isTrue);
        if (!allComplete) {
            if (AudioTypeEnum.COMPOSE.equals(snapshot.getBotConfig().getAudioConfig().getAudioType())) {
                // 检查是否正在合成
                if (ttsJobExecuteService.botIsComposing(snapshot.getBotId())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "请等待合成完成");
                } else {
                    // 开始合成
                    // todo 这里仅针对百度爱番番的 bot 进行处理
                    startCompose(snapshot.getBotId(), snapshot.getCreateUserId());
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "请先合成全部录音");
                }
            } else {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "请先录制全部录音");
            }
        }
        snapshot.setAnswerAudioMappingList(getAnswerAudioMappingList(answerAudioList));
    }

    private List<AnswerAudioMappingPO> getAnswerAudioMappingList(List<AnswerAudioWrapVO> answerList) {
        if (CollectionUtils.isEmpty(answerList)) {
            return Collections.emptyList();
        }
        Map<String, AnswerAudioMappingPO> answerMappingMap = new HashMap<>();
        answerList.forEach(answer -> {
            if (CollectionUtils.isNotEmpty(answer.getAudioSourceList())) {
                answer.getAudioSourceList().forEach(audio -> answerMappingMap.put(audio.getId(), audio));
            }
        });
        return new ArrayList<>(answerMappingMap.values());
    }

    @Override
    public void sync(SyncAudioRequestVO request, Long userId) {
        Long botId = request.getBotId();
        BotPO bot = Optional.ofNullable(botService.getById(botId)).orElseThrow(() -> new ComException(ComErrorCode.VALIDATE_ERROR, "bot不存在"));

        Long recordUserId = Optional.ofNullable(botConfigService.getAudioConfig(botId))
                .filter(config -> !AudioTypeEnum.COMPOSE.equals(config.getAudioType()))
                .map(BotAudioConfigPO::getRecordUserId)
                .orElseThrow(() -> new ComException(ComErrorCode.VALIDATE_ERROR, "录音师不存在"));

        String groupId = request.getGroupId();
        Assert.notNull(groupService.selectOne(groupId, GroupTypeEnum.PUBLIC_AUDIO, 0L), "分组不存在");

        List<AnswerAudioWrapVO> answerAudioWrapVOList = getAllByBotId(botId)
                .stream().filter(s -> request.getAnswerIdList().contains(s.getUniqueId()) && Objects.nonNull(s.getLocate()))
                .collect(Collectors.toList());

        publicAudioService.sync(recordUserId, answerAudioWrapVOList, bot, request, userId);
    }

    @Override
    public AudioUploadResult uploadAndUpdate(Long dialogFlowId, String text, String ossKey, Long userId) {
        Long botId = botRefService.getBotId(dialogFlowId);
        String basePathStr = TempFilePathKeyCenter.getBotUploadAudioDirPath(botId);
        // 把原始文件的名称中的空格去掉
        String localName =basePathStr + System.currentTimeMillis() + ".wav";
        File localFile = objectStorageHelper.downloadToFile(ossKey, localName);
        AudioUploadResultVO uploadResult = audioUploadService.uploadAudio(botId, localFile, true);
        AnswerUpdateAudioRequestVO request = new AnswerUpdateAudioRequestVO();
        request.setBotId(botId);
        request.setAnswerText(text);
        request.setUrl(uploadResult.getUrl());
        request.setFullUrl(uploadResult.getFullUrl());
        request.setVolume(uploadResult.getVolume());
        request.setDuration(uploadResult.getDuration());
        updateAnswerAudio(request, userId);
        AudioUploadResult result = new AudioUploadResult();
        result.setAudioKey(uploadResult.getUrl());
        result.setAudioUrl(uploadResult.getFullUrl());
        result.setText(text);
        return result;
    }
}
