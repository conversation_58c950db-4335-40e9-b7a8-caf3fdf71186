package com.yiwise.dialogflow.service.impl.operationlog;

import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.dto.OperationLogDTO;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import com.yiwise.dialogflow.entity.enums.StepSubTypeEnum;
import com.yiwise.dialogflow.entity.po.StepPO;
import com.yiwise.dialogflow.service.DependResourceService;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.operationlog.StepOperationLogService;
import com.yiwise.dialogflow.utils.AnswerConditionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StepOperationLogServiceImpl implements StepOperationLogService {

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private DependResourceService dependResourceService;

    @Override
    public void compareAndCreateOperationLog(Long botId, StepPO oldStep, StepPO newStep, Long userId) {
        if (Objects.isNull(newStep) && Objects.isNull(oldStep)) {
            return;
        }
        List<String> logDetailList = new ArrayList<>();
        if (Objects.isNull(oldStep)) {
            logDetailList.add(String.format("添加流程:【%s:%s】", newStep.getLabel(), newStep.getName()));
        } else if (Objects.isNull(newStep)) {
            logDetailList.add(String.format("删除流程:【%s:%s】", oldStep.getLabel(), oldStep.getName()));
        } else {
            // 比较名称
            if (!stringEquals(oldStep.getName(), newStep.getName())) {
                logDetailList.add(String.format("%s流程名称【%s】, 修改为【%s】", updatePrefix(oldStep), oldStep.getName(), newStep.getName()));
            }

            // 比较类型
            if (!Objects.equals(oldStep.getCategory(), newStep.getCategory())) {
                logDetailList.add(String.format("%s流程类型【%s】, 修改为【%s】", updatePrefix(oldStep), oldStep.getCategory().getDesc(), newStep.getCategory().getDesc()));
            }

            // 比较关注点
            if (!Objects.equals(oldStep.getIsCustomerConcern(), newStep.getIsCustomerConcern())) {
                logDetailList.add(String.format("%s%s关注点", updatePrefix(oldStep), BooleanUtils.isTrue(newStep.getIsCustomerConcern()) ? "设为" : "取消"));
            }

            DependentResourceBO resource = dependResourceService.generateByCondition(new DependentResourceBO.Condition(newStep.getBotId()).intent().variable());

            // 流程跳过条件
            String oldSkipCondition = skipConditionDescription(oldStep, resource);
            String newSkipCondition = skipConditionDescription(newStep, resource);

            if (!stringEquals(oldSkipCondition, newSkipCondition)) {
                logDetailList.add(String.format("%s流程跳过设置【%s】修改为【%s】",
                        updatePrefix(oldStep),  oldSkipCondition, newSkipCondition));
            }

            compareIntent(oldStep, newStep, logDetailList, resource);

            compareLlmConfig(oldStep, newStep, logDetailList);

            if (!stringEquals(oldStep.getDesc(), newStep.getDesc())) {
                logDetailList.add(String.format("%s流程描述【%s】,修改为【%s】", updatePrefix(oldStep), oldStep.getDesc(), newStep.getDesc()));
            }
            if (!stringEquals(oldStep.getLabel(), newStep.getLabel())) {
                logDetailList.add(String.format("%s流程ID【%s】,修改为【%s】", updatePrefix(oldStep), oldStep.getLabel(), newStep.getLabel()));
            }
            if (StepSubTypeEnum.isLlm(newStep.getSubType()) && !stringEquals(oldStep.getLlmModelName(), newStep.getLlmModelName())) {
                logDetailList.add(String.format("%s流程模型【%s】,修改为【%s】", updatePrefix(oldStep), oldStep.getLlmModelName(), newStep.getLlmModelName()));
            }
        }

        save(botId, logDetailList, userId);
    }

    private boolean stringEquals(String one, String other) {
        if (StringUtils.isBlank(one) && StringUtils.isBlank(other)) {
            return true;
        }
        return StringUtils.equals(one, other);
    }

    private void compareLlmConfig(StepPO oldStep, StepPO newStep, List<String> logDetailList) {
        if (oldStep.isLlmStep() && !newStep.isLlmStep()) {
            logDetailList.add(String.format("%s任务类型【%s】修改为【%s】",
                    updatePrefix(oldStep), oldStep.getLlmStepType().getDesc(), newStep.getLlmStepType().getDesc()));
        }
    }

    private String skipConditionDescription(StepPO step, DependentResourceBO resource) {
        if (BooleanUtils.isNotTrue(step.getEnableSkipCondition())) {
            return "未开启";
        }

        if (Objects.isNull(step.getSkipCondition())) {
            return "已开启, 但条件未配置";
        }

        return AnswerConditionUtil.generateConditionStrContent(step.getSkipCondition().getConditionList(),
                resource.getVariableIdNameMap(),
                resource.getIntentIdNameMap());
    }

    @Override
    public void createDeleteOperationLog(Long botId, List<StepPO> deleteStepList, Long userId) {
        if (CollectionUtils.isEmpty(deleteStepList)) {
            return;
        }
        List<String> logDetailList = deleteStepList.stream()
                .map(step -> String.format("删除流程:【%s:%s】", step.getLabel(), step.getName()))
                .collect(Collectors.toList());
        save(botId, logDetailList, userId);
    }

    @Override
    public void copyStepInBot(Long botId, StepPO sourceStep, StepPO targetStep, Long userId) {
        String log = String.format("复制流程:%s的【%s:%s】至%s的【%s:%s】", sourceStep.getBotId(), sourceStep.getLabel(), sourceStep.getName(), targetStep.getBotId(), targetStep.getLabel(), targetStep.getName());
        save(botId, Collections.singletonList(log), userId);
    }

    private void compareIntent(StepPO oldStep, StepPO newStep, List<String> logDetailList, DependentResourceBO resource) {
        String oldIntentNameList = "";
        String newIntentNameList = "";
        if (CollectionUtils.isNotEmpty(oldStep.getTriggerIntentIdList())) {
            oldIntentNameList = oldStep.getTriggerIntentIdList().stream()
                    .map(resource.getIntentIdNameMap()::get)
                    .filter(StringUtils::isNotBlank)
                    .sorted()
                    .collect(Collectors.joining("、"));
        }
        if (CollectionUtils.isNotEmpty(newStep.getTriggerIntentIdList())) {
            newIntentNameList = newStep.getTriggerIntentIdList().stream()
                    .map(resource.getIntentIdNameMap()::get)
                    .filter(StringUtils::isNotBlank)
                    .sorted()
                    .collect(Collectors.joining("、"));
        }
        if (!Objects.equals(oldStep.getTriggerByIntent(), newStep.getTriggerByIntent())) {
            logDetailList.add(String.format("%s%s意图触发", updatePrefix(oldStep), BooleanUtils.isTrue(newStep.getTriggerByIntent()) ? "开启" : "关闭"));
        } else if (BooleanUtils.isTrue(newStep.getTriggerByIntent())
                && !StringUtils.equals(oldIntentNameList, newIntentNameList)) {
            logDetailList.add(String.format("%s触发意图【%s】修改为【%s】",
                    updatePrefix(oldStep), oldIntentNameList, newIntentNameList));
        }
    }

    private String updatePrefix(StepPO oldStep) {
        return String.format("编辑流程: 原【%s:%s】", oldStep.getLabel(), oldStep.getName());
    }

    private void save(Long botId, List<String> logDetailList, Long userId) {
        if (CollectionUtils.isEmpty(logDetailList)) {
            return;
        }
        List<OperationLogDTO> logs = logDetailList.stream()
                .map(detail -> {
                    OperationLogDTO item = new OperationLogDTO();
                    item.setBotId(botId);
                    item.setType(OperationLogTypeEnum.STEP);
                    item.setOperatorId(userId);
                    item.setResourceType(OperationLogResourceTypeEnum.STEP);
                    item.setDetail(detail);
                    return item;
                })
                .collect(Collectors.toList());
        operationLogService.batchSave(logs);
    }
}
