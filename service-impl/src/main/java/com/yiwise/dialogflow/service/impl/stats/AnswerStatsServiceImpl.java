package com.yiwise.dialogflow.service.impl.stats;

import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.po.stats.AnswerPlayProgressHangupStatsPO;
import com.yiwise.dialogflow.entity.po.stats.AnswerStatsPO;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;
import com.yiwise.dialogflow.service.CallStatsMongoService;
import com.yiwise.dialogflow.service.stats.AnswerStatsService;
import com.yiwise.dialogflow.utils.BotStatsUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class AnswerStatsServiceImpl implements AnswerStatsService {

    @Resource
    private CallStatsMongoService callStatsMongoService;

    @Resource
    private MongoTemplate readMongoTemplate;

    @Override
    public List<AnswerStatsPO> queryAllStepAllAnswerTotalHangupStats(Long botId, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("answerSource").is(AnswerSourceEnum.STEP.name())));
        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);


        AggregationOperation groupOperation = Aggregation.group("stepId", "nodeId", "answerTemplate")
                .sum("totalHangupCount").as("totalHangupCount")
                .first("answerTemplate").as("answerTemplate")
                .first("answerSource").as("answerSource")
                .first("stepId").as("stepId")
                .first("nodeId").as("nodeId");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.ANSWER_STATS, AnswerStatsPO.class)
                .getMappedResults();
    }

    @Override
    public List<AnswerStatsPO> queryStepAllAnswerTotalHangupStats(Long botId, String stepId, List<String> nodeIdList, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("stepId").is(stepId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("answerSource").is(AnswerSourceEnum.STEP.name())));

        if (CollectionUtils.isNotEmpty(nodeIdList)) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("nodeId").in(nodeIdList)));
        }
        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);


        AggregationOperation groupOperation = Aggregation.group("nodeId", "answerTemplate")
                .sum("totalHangupCount").as("totalHangupCount")
                .first("answerTemplate").as("answerTemplate")
                .first("nodeId").as("nodeId");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.ANSWER_STATS, AnswerStatsPO.class)
                .getMappedResults();
    }

    @Override
    public List<AnswerStatsPO> queryBotAllAnswerTotalHangupAndDeclineStats(Long botId, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));

        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);


        AggregationOperation groupOperation = Aggregation.group( "answerSource", "nodeId", "knowledgeId", "specialAnswerConfigId", "answerTemplate")
                .sum("totalHangupCount").as("totalHangupCount")
                .sum("declineCount").as("declineCount")
                .sum("declineCallCount").as("declineCallCount")
                .first("answerTemplate").as("answerTemplate")
                .first("knowledgeId").as("knowledgeId")
                .first("specialAnswerConfigId").as("specialAnswerConfigId")
                .first("stepId").as("stepId")
                .first("nodeId").as("nodeId")
                .first("answerSource").as("answerSource");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.ANSWER_STATS, AnswerStatsPO.class)
                .getMappedResults();
    }

    @Override
    public List<AnswerStatsPO> queryAllKnowledgeAllAnswerTotalHangupAndDeclineStats(Long botId, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("answerSource").is(AnswerSourceEnum.KNOWLEDGE.name())));

        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);


        AggregationOperation groupOperation = Aggregation.group( "knowledgeId", "answerTemplate")
                .sum("totalHangupCount").as("totalHangupCount")
                .sum("declineCount").as("declineCount")
                .sum("declineCallCount").as("declineCallCount")
                .first("answerTemplate").as("answerTemplate")
                .first("knowledgeId").as("knowledgeId")
                .first("answerSource").as("answerSource");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.ANSWER_STATS, AnswerStatsPO.class)
                .getMappedResults();
    }

    @Override
    public List<AnswerStatsPO> queryAllSpecialAnswerAllAnswerTotalHangupAndDeclineStats(Long botId, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("answerSource").is(AnswerSourceEnum.SPECIAL_ANSWER.name())));

        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);

        AggregationOperation groupOperation = Aggregation.group( "specialAnswerConfigId", "answerTemplate")
                .sum("totalHangupCount").as("totalHangupCount")
                .sum("declineCount").as("declineCount")
                .sum("declineCallCount").as("declineCallCount")
                .first("answerTemplate").as("answerTemplate")
                .first("specialAnswerConfigId").as("specialAnswerConfigId")
                .first("answerSource").as("answerSource");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.ANSWER_STATS, AnswerStatsPO.class)
                .getMappedResults();
    }




    @Override
    public List<AnswerPlayProgressHangupStatsPO> queryNodeAllAnswerProgressHangupStats(Long botId, String stepId, String nodeId, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("answerSource").is(AnswerSourceEnum.STEP.name())));
        aggregationOperationList.add(Aggregation.match(Criteria.where("stepId").is(stepId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("nodeId").is(nodeId)));
        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);

        GroupOperation groupOperation = Aggregation.group("answerTemplate")
                .first("answerTemplate").as("answerTemplate");
        for (int i = 0; i <= 100; i++) {
            groupOperation = groupOperation.sum(String.format("hangupProgress.%s", i)).as(String.format("_%s", i));
        }

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.ANSWER_STATS, AnswerPlayProgressHangupStatsPO.class)
                .getMappedResults();
    }

    @Override
    public List<AnswerPlayProgressHangupStatsPO> queryKnowledgeAllAnswerProgressHangupStats(Long botId, String knowledgeId, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("answerSource").is(AnswerSourceEnum.KNOWLEDGE.name())));
        aggregationOperationList.add(Aggregation.match(Criteria.where("knowledgeId").is(knowledgeId)));
        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);

        GroupOperation groupOperation = Aggregation.group("answerTemplate")
                .first("answerTemplate").as("answerTemplate");
        for (int i = 0; i <= 100; i++) {
            groupOperation = groupOperation.sum(String.format("hangupProgress.%s", i)).as(String.format("_%s", i));
        }

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.ANSWER_STATS, AnswerPlayProgressHangupStatsPO.class)
                .getMappedResults();
    }

    @Override
    public List<AnswerPlayProgressHangupStatsPO> querySpecialAnswerAllAnswerProgressHangupStats(Long botId, String specialAnswerConfigId, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("answerSource").is(AnswerSourceEnum.SPECIAL_ANSWER.name())));
        aggregationOperationList.add(Aggregation.match(Criteria.where("specialAnswerConfigId").is(specialAnswerConfigId)));
        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);

        GroupOperation groupOperation = Aggregation.group("answerTemplate")
                .first("answerTemplate").as("answerTemplate");
        for (int i = 0; i <= 100; i++) {
            groupOperation = groupOperation.sum(String.format("hangupProgress.%s", i)).as(String.format("_%s", i));
        }

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.ANSWER_STATS, AnswerPlayProgressHangupStatsPO.class)
                .getMappedResults();
    }

    @Override
    public void saveAnswerStats(BotStatsAnalysisResult analysisResult) {
        if (MapUtils.isEmpty(analysisResult.getAnswerDeclineCountMap())
                && MapUtils.isEmpty(analysisResult.getAnswerHangupCountMap())) {
            return;
        }

        analysisResult.getAnswerHangupCountMap().forEach((k, count) -> {
            Query query = BotStatsUtil.generateCommonQuery(analysisResult);
            query.addCriteria(Criteria.where("answerTemplate").is(k.getAnswerTemplate()));
            query.addCriteria(Criteria.where("answerTemplateHash").is(BotStatsUtil.calculateAnswerTemplateHash(k.getAnswerTemplate())));
            query.addCriteria(Criteria.where("answerSource").is(k.getAnswerSource().name()));
            if (StringUtils.isNotBlank(k.getStepId())) {
                query.addCriteria(Criteria.where("stepId").is(k.getStepId()));
            }
            if (StringUtils.isNotBlank(k.getNodeId())) {
                query.addCriteria(Criteria.where("nodeId").is(k.getNodeId()));
            }
            if (StringUtils.isNotBlank(k.getKnowledgeId())) {
                query.addCriteria(Criteria.where("knowledgeId").is(k.getKnowledgeId()));
            }
            if (StringUtils.isNotBlank(k.getSpecialAnswerConfigId())) {
                query.addCriteria(Criteria.where("specialAnswerConfigId").is(k.getSpecialAnswerConfigId()));
            }
            Update update = new Update();
            update.inc("totalHangupCount", 1);
            update.inc(String.format("hangupProgress.%s", k.getProgress()), 1);
            callStatsMongoService.updateMongoDataUsingCache(MongoCollectionNameCenter.ANSWER_STATS, query, update);
        });

        analysisResult.getAnswerDeclineCountMap().forEach((k, count) -> {
            Query query = BotStatsUtil.generateCommonQuery(analysisResult);
            query.addCriteria(Criteria.where("answerTemplate").is(k.getAnswerTemplate()));
            query.addCriteria(Criteria.where("answerTemplateHash").is(BotStatsUtil.calculateAnswerTemplateHash(k.getAnswerTemplate())));
            query.addCriteria(Criteria.where("answerSource").is(k.getAnswerSource().name()));
            if (StringUtils.isNotBlank(k.getStepId())) {
                query.addCriteria(Criteria.where("stepId").is(k.getStepId()));
            }
            if (StringUtils.isNotBlank(k.getNodeId())) {
                query.addCriteria(Criteria.where("nodeId").is(k.getNodeId()));
            }
            if (StringUtils.isNotBlank(k.getKnowledgeId())) {
                query.addCriteria(Criteria.where("knowledgeId").is(k.getKnowledgeId()));
            }
            if (StringUtils.isNotBlank(k.getSpecialAnswerConfigId())) {
                query.addCriteria(Criteria.where("specialAnswerConfigId").is(k.getSpecialAnswerConfigId()));
            }

            Update update = new Update();
            update.inc("declineCount", count.get());
            update.inc("declineCallCount", 1);
            callStatsMongoService.updateMongoDataUsingCache(MongoCollectionNameCenter.ANSWER_STATS, query, update);
        });
    }
}
