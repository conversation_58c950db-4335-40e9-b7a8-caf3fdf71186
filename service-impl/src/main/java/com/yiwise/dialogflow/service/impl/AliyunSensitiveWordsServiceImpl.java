package com.yiwise.dialogflow.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.aliyun.green20220302.Client;
import com.aliyun.green20220302.models.TextModerationRequest;
import com.aliyun.green20220302.models.TextModerationResponse;
import com.aliyun.green20220302.models.TextModerationResponseBody;
import com.aliyun.teaopenapi.models.Config;
import com.yiwise.base.common.utils.RetryRunUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.dto.response.SensitiveWordsDetectResultBO;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.service.AliyunSensitiveWordsService;
import com.yiwise.middleware.tts.config.TtsClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AliyunSensitiveWordsServiceImpl implements AliyunSensitiveWordsService {

    private final Client client;

    {
        client = RetryRunUtils.retryOrThrow(() -> {
            Config config = new Config()
                    .setAccessKeyId(TtsClientConfig.ALIYUN_NLS_TTS_ACCESS_KEY_ID)
                    .setAccessKeySecret(TtsClientConfig.ALIYUN_NLS_TTS_ACCESS_KEY_SECRET);
            config.endpoint = ApplicationConstant.ALIYUN_SENSITIVE_WORDS_ENDPOINT;
            return new Client(config);
        }, 1000, "敏感词服务初始化");
    }

    @Override
    public SensitiveWordsDetectResultBO detect(String text) {
        if (StringUtils.isBlank(text)) {
            return null;
        }
        return RetryRunUtils.retryOrThrow(() -> doDetect(text),
                10, RandomUtils.nextLong(300, 500),false, "敏感词检测-" + text);
    }

    private SensitiveWordsDetectResultBO doDetect(String text) {
        try {
            JSONObject serviceParameters = new JSONObject();
            serviceParameters.put("content", text);
            TextModerationRequest textModerationRequest = new TextModerationRequest()
                    .setService(ApplicationConstant.ALIYUN_SENSITIVE_WORDS_SERVICE_NAME)
                    .setServiceParameters(serviceParameters.toJSONString());
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();

            log.info("请求敏感词检测接口,body={}", JsonUtils.object2String(textModerationRequest));
            TextModerationResponse response = client.textModerationWithOptions(textModerationRequest, runtime);
            log.info("敏感词检测接口响应,response={}", JsonUtils.object2String(response));

            if (Objects.nonNull(response.getStatusCode()) && response.getStatusCode().equals(200) && Objects.nonNull(response.getBody())) {
                TextModerationResponseBody body = response.getBody();
                if (Objects.nonNull(body.getCode()) && body.getCode().equals(200) && Objects.nonNull(body.getData())) {
                    TextModerationResponseBody.TextModerationResponseBodyData data = body.getData();
                    String labels = data.getLabels();
                    // 过滤掉广告
                    if (StringUtils.isBlank(labels) || StringUtils.equals("ad", labels)) {
                        return new SensitiveWordsDetectResultBO(text, true, Collections.emptyList());
                    }
                    String reason = data.getReason();
                    List<String> words = new ArrayList<>();
                    if (StringUtils.isNotBlank(reason)) {
                        JSONObject reasonObject = JSONObject.parseObject(reason);

                        String riskWords = reasonObject.getString("riskWords");
                        if (StringUtils.isNotBlank(riskWords)) {
                            words.addAll(Arrays.asList(riskWords.split(",")));
                        }

                        String customizedWords = reasonObject.getString("customizedWords");
                        if (StringUtils.isNotBlank(customizedWords)) {
                            words.addAll(Arrays.asList(customizedWords.split(",")));
                        }
                    }
                    return new SensitiveWordsDetectResultBO(text, false, words);
                }
            }
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "调用敏感词检测接口失败");
        } catch (Exception e) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "调用敏感词检测接口失败", e);
        }
    }
}
