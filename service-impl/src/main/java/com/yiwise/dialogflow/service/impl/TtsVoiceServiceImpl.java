package com.yiwise.dialogflow.service.impl;


import com.yiwise.dialogflow.api.dto.response.audio.TtsVoiceItem;
import com.yiwise.dialogflow.service.TtsVoiceService;
import com.yiwise.middleware.tts.enums.TtsVoiceEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class TtsVoiceServiceImpl implements TtsVoiceService {

    @Override
    public List<TtsVoiceItem> getTianrunSupportVoiceList() {
        List<TtsVoiceItem> list = new ArrayList<>();
        list.add(TtsVoiceItem.builder()
                .voice(TtsVoiceEnum.MAN_Aida.name())
                .name(TtsVoiceEnum.MAN_Aida.getDesc())
                .build()
        );
        return list;
    }
}
