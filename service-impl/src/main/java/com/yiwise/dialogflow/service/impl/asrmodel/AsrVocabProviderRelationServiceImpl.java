package com.yiwise.dialogflow.service.impl.asrmodel;

import com.yiwise.dialogflow.engine.share.enums.AsrProviderEnum;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrVocabProviderRelationPO;
import com.yiwise.dialogflow.mapper.AsrVocabProviderRelationPOMapper;
import com.yiwise.dialogflow.service.asrmodel.AsrVocabProviderRelationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/1 15:42:14
 */
@Service
public class AsrVocabProviderRelationServiceImpl implements AsrVocabProviderRelationService {
    @Resource
    private AsrVocabProviderRelationPOMapper asrVocabProviderRelationPOMapper;

    @Override
    public List<AsrVocabProviderRelationPO> getByAsrVocabId(Long asrVocabId) {
        return asrVocabProviderRelationPOMapper.getByAsrVocabId(asrVocabId);
    }

    @Override
    public AsrVocabProviderRelationPO getByAsrVocabIdAndProvider(Long asrVocabId, AsrProviderEnum provider) {
        return asrVocabProviderRelationPOMapper.getByAsrVocabIdAndProvider(asrVocabId, provider);
    }

    @Override
    public void delete(AsrVocabProviderRelationPO asrVocabProviderRelationPO) {
        asrVocabProviderRelationPOMapper.delete(asrVocabProviderRelationPO);
    }

    @Override
    public void add(AsrVocabProviderRelationPO asrVocabProviderRelationPO) {
        asrVocabProviderRelationPOMapper.insertSelective(asrVocabProviderRelationPO);
    }
}