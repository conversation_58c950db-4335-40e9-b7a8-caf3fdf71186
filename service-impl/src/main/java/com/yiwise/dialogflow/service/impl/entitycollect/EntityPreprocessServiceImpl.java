package com.yiwise.dialogflow.service.impl.entitycollect;

import com.yiwise.base.common.text.TextPlaceholderElement;
import com.yiwise.base.common.text.TextPlaceholderSplitter;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.bo.entity.*;
import com.yiwise.dialogflow.entity.enums.LargeModelEntityModelTypeEnum;
import com.yiwise.dialogflow.entity.enums.SystemEntityCategoryEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.pattern.PatternEnhance;
import com.yiwise.dialogflow.pattern.PatternEnhanceCache;
import com.yiwise.dialogflow.service.entitycollect.EntityPreprocessService;
import com.yiwise.dialogflow.utils.ParseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EntityPreprocessServiceImpl implements EntityPreprocessService {

    private static final AtomicLong SYNONYM_ID_GENERATOR = new AtomicLong(0);

    private static final char[] ALPHABET = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".toCharArray();

    private static final int LENGTH = ALPHABET.length;

    @Override
    public List<RuntimeEntityBO> preprocess(List<BaseEntityPO> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        return entityList.stream()
                .map(item -> {
                    if (item instanceof RegexEntityPO) {
                        return convert((RegexEntityPO) item);
                    } else if (item instanceof StandardEntityPO) {
                        return convert((StandardEntityPO) item);
                    } else if (item instanceof SystemEntityPO) {
                        return convert((SystemEntityPO) item);
                    } else if (item instanceof LargeModelEntityPO) {
                        return convert((LargeModelEntityPO) item);
                    } else {
                        log.warn("不支持的实体类型: {}", item.getClass());
                        throw new IllegalArgumentException("不支持的实体类型");
                    }
                })
                .collect(Collectors.toList());
    }

    @Override
    public void validate(List<BaseEntityPO> entityList) {
        // 对实体进行校验
        if (CollectionUtils.isEmpty(entityList)) {
            throw new IllegalArgumentException("实体列表不能为空");
        }

        for (BaseEntityPO entity : entityList) {
            validateBase(entity);
            if (entity instanceof StandardEntityPO) {
                validateStandard((StandardEntityPO) entity);
            } else if (entity instanceof RegexEntityPO) {
                validateRegex((RegexEntityPO) entity);
            } else if (entity instanceof SystemEntityPO) {
                validateSystem((SystemEntityPO) entity);
            } else if (entity instanceof LargeModelEntityPO) {
                validateLargeModel((LargeModelEntityPO) entity);
            }
        }
    }

    private void validateLargeModel(LargeModelEntityPO entity) {
        LargeModelEntityModelTypeEnum modelType = entity.getModelType();
        if (LargeModelEntityModelTypeEnum.isTuneModel(modelType)) {
            if (StringUtils.isBlank(entity.getDesc())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("大模型实体[%s]描述不能为空", entity.getName()));
            }
            if (CollectionUtils.isEmpty(entity.getMemberList())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("大模型实体[%s]成员列表不能为空", entity.getName()));
            }
            int i = 1;
            for (EntitySynonymPO synonym : entity.getMemberList()) {
                if (StringUtils.isBlank(synonym.getName())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("大模型实体[%s]实体成员%s名称不能为空", entity.getName(), i));
                }
                if (CollectionUtils.isEmpty(synonym.getSynonymList())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("大模型实体[%s]实体成员%s描述不能为空", entity.getName(), i));
                }
                i++;
            }
        }
        if (LargeModelEntityModelTypeEnum.isCommonModel(modelType)) {
            if(StringUtils.isBlank(entity.getPrompt())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("大模型实体[%s]提示词不能为空", entity.getName()));
            }
        }
    }

    private void validateSystem(SystemEntityPO entity) {
        if (CollectionUtils.isEmpty(entity.getExtraRegexList())) {
            return;
        }
        for (String s : entity.getExtraRegexList()) {
            if (ParseUtil.invalid(s)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("系统实体[%s]扩展正则表达式[%s]非法", entity.getName(), s));
            }
        }
    }

    private void validateBase(BaseEntityPO entity) {
        if (StringUtils.isBlank(entity.getName())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "实体名称不能为空");
        }
        if (Objects.isNull(entity.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        if (Objects.isNull(entity.getType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "实体类型不能为空");
        }
        // 校验反例
        if (CollectionUtils.isNotEmpty(entity.getSkipRegexList())) {
            for (String s : entity.getSkipRegexList()) {
                Set<String> refEntityNameSet = getRefEntityNameSet(s);
                if (CollectionUtils.isEmpty(refEntityNameSet)) {
                    if (ParseUtil.invalid(s)) {
                        throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("实体[%s]反例正则表达式[%s]非法", entity.getName(), s));
                    }
                } else {
                    if (refEntityNameSet.size() != 1) {
                        throw new ComException(ComErrorCode.VALIDATE_ERROR, "反例表达式仅能引用当前实体");
                    }
                    String refEntityName = refEntityNameSet.iterator().next();
                    if (!entity.getName().equals(refEntityName)) {
                        throw new ComException(ComErrorCode.VALIDATE_ERROR, "反例表达式仅能引用当前实体");
                    }
                }
            }
        }
    }

    private void validateRegex(RegexEntityPO entity) {
        if (CollectionUtils.isEmpty(entity.getRegexList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "正则实体正则表达式列表不能为空");
        }
        for (String s : entity.getRegexList()) {
            if (ParseUtil.invalid(s)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("正则实体[%s]正则表达式[%s]非法", entity.getName(), s));
            }
        }
    }

    private void validateStandard(StandardEntityPO entity) {
        if (CollectionUtils.isEmpty(entity.getMemberList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "实体成员不能为空");
        }
        Set<String> memberNameSet = new HashSet<>();
        for (EntitySynonymPO entitySynonym : entity.getMemberList()) {
            if (StringUtils.isBlank(entitySynonym.getName())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "实体成员名称不能为空");
            }
            if (memberNameSet.contains(entitySynonym.getName())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("实体成员[%s]重复", entitySynonym.getName()));
            }
            memberNameSet.add(entitySynonym.getName());
            if (CollectionUtils.isNotEmpty(entitySynonym.getSynonymList())) {
                // 对每个同义词进行校验, 防止写如可能导致最终正则编译异常的内容
                for (String s : entitySynonym.getSynonymList()) {
                    if (StringUtils.isBlank(s)) {
                        throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("实体成员[%s]同义词不能为空", entitySynonym.getName()));
                    }
                    s = convertPinyinExpression(s);
                    if (ParseUtil.invalid(s)) {
                        throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("实体成员[%s]同义词[%s]不能是非法的正则表达式", entitySynonym.getName(), s));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(entitySynonym.getRegexSynonymList())) {
                for (String s : entitySynonym.getRegexSynonymList()) {
                    if (ParseUtil.invalid(s)) {
                        throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("实体成员[%s]同义正则表达式[%s]非法", entitySynonym.getName(), s));
                    }
                }
            }
        }
    }

    private String getPinyinContent(String s) {
        s = s.trim();
        return s.substring(1, s.length() - 1).toLowerCase();
    }

    @Override
    public void validate(BaseEntityPO entity) {
        if (Objects.isNull(entity)) {
            throw new IllegalArgumentException("实体不能为空");
        }
        if (entity instanceof SystemEntityPO) {
            SystemEntityPO systemEntity = (SystemEntityPO) entity;
            if (SystemEntityCategoryEnum.ORIGIN_INPUT.equals(systemEntity.getEntityCategory())) {
                return;
            }
        }
        validate(Collections.singletonList(entity));
    }

    private RuntimeSystemEntityBO convert(SystemEntityPO entity) {
        RuntimeSystemEntityBO bo = MyBeanUtils.copy(entity, RuntimeSystemEntityBO.class);
        processSkipRegex(bo, entity);
        if (CollectionUtils.isNotEmpty(entity.getExtraRegexList())) {
            List<PatternEnhance> patternEnhanceList = entity.getExtraRegexList()
                    .stream()
                    .map(regex -> PatternEnhanceCache.getOrCreate(regex, Pattern.compile(regex), false))
                    .collect(Collectors.toList());
            bo.setExtraPatternList(patternEnhanceList);
        }
        return bo;
    }

    private RuntimeLargeModelEntityBO convert(LargeModelEntityPO entity) {
        return MyBeanUtils.copy(entity, RuntimeLargeModelEntityBO.class);
    }

    private RuntimeStandardEntityBO convert(StandardEntityPO entity) {
        RuntimeStandardEntityBO bo = MyBeanUtils.copy(entity, RuntimeStandardEntityBO.class);
        bo.setRuntimeMemberList(convert(entity.getMemberList()));

        // 处理反例
        processSkipRegex(bo, entity);
        return bo;
    }

    private RuntimeRegexEntityBO convert(RegexEntityPO entity) {
        RuntimeRegexEntityBO bo = MyBeanUtils.copy(entity, RuntimeRegexEntityBO.class);
        processSkipRegex(bo, entity);
        if (CollectionUtils.isNotEmpty(entity.getRegexList())) {
            List<PatternEnhance> patternEnhanceList = entity.getRegexList()
                            .stream()
                            .map(regex -> PatternEnhanceCache.getOrCreate(regex, Pattern.compile(regex), false))
                            .collect(Collectors.toList());
            bo.setPatternList(patternEnhanceList);
        }
        return bo;
    }

    private boolean isSelfReferenceRegex(String regex) {
        TextPlaceholderSplitter splitter = new TextPlaceholderSplitter(regex);
        return CollectionUtils.isEmpty(splitter.getPlaceholderList());
    }

    private Set<String> getRefEntityNameSet(String regex) {
        TextPlaceholderSplitter splitter = new TextPlaceholderSplitter(regex);
        return splitter.getPlaceholderSet();
    }

    private void processSkipRegex(RuntimeEntityBO runtimeEntity, BaseEntityPO entityPO) {
        if (CollectionUtils.isEmpty(entityPO.getSkipRegexList())) {
            return;
        }
        List<PatternEnhance> preSkipPatternList = entityPO.getSkipRegexList()
                .stream()
                .filter(regex -> {
                    TextPlaceholderSplitter splitter = new TextPlaceholderSplitter(regex);
                    return CollectionUtils.isEmpty(splitter.getPlaceholderList());
                })
                .map(regex -> PatternEnhanceCache.getOrCreate(regex, Pattern.compile(regex), false))
                .collect(Collectors.toList());
        runtimeEntity.setPreSkipPatternList(preSkipPatternList);
        List<PatternEnhance> postSkipPatternList = entityPO.getSkipRegexList()
                .stream()
                .map(regex -> {
                    TextPlaceholderSplitter splitter = new TextPlaceholderSplitter(regex);
                    if (CollectionUtils.isEmpty(splitter.getPlaceholderList())) {
                        return "";
                    }
                    // 不是${水果} -> 不是水果
                    return splitter.getTextPlaceholderList().stream()
                            .map(TextPlaceholderElement::getValue)
                            .collect(Collectors.joining());
                })
                .filter(StringUtils::isNotBlank)
                .map(regex -> PatternEnhanceCache.getOrCreate(regex, Pattern.compile(regex), false))
                .collect(Collectors.toList());
        runtimeEntity.setPostSkipPatternList(postSkipPatternList);
    }

    private List<EntitySynonymBO> convert(List<EntitySynonymPO> memberList) {
        if (CollectionUtils.isEmpty(memberList)) {
            return Collections.emptyList();
        }
        return memberList.stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    private EntitySynonymBO convert(EntitySynonymPO entitySynonym) {
        EntitySynonymBO bo = MyBeanUtils.copy(entitySynonym, EntitySynonymBO.class);
        List<String> memberContentList = new ArrayList<>();
        List<PatternEnhance> regexMemberPatternList = new ArrayList<>();
        List<String> pinyinMemberContentList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(entitySynonym.getSynonymList())) {
            memberContentList.addAll(
                    entitySynonym.getSynonymList()
                            .stream()
                            .filter(StringUtils::isNotBlank)
                            .filter(this::isNotPinyinExpression)
                            .distinct()
                            .sorted(Comparator.comparingInt(String::length).reversed())
                            .collect(Collectors.toList())
            );
        }
        // 对正则同义词进行处理
        if (CollectionUtils.isNotEmpty(entitySynonym.getRegexSynonymList())) {
            regexMemberPatternList.addAll(
                    entitySynonym.getRegexSynonymList()
                            .stream()
                            .filter(StringUtils::isNotBlank)
                            .filter(this::validRegex)
                            .map(regex -> "(" + regex + ")")
                            .distinct()
                            .sorted(Comparator.comparingInt(String::length).reversed())
                            .map(regex -> PatternEnhanceCache.getOrCreate(regex, Pattern.compile(regex), false))
                            .collect(Collectors.toList())
            );
        }
        // 处理拼音
        if (CollectionUtils.isNotEmpty(entitySynonym.getSynonymList())) {
            pinyinMemberContentList.addAll(entitySynonym.getSynonymList()
                    .stream()
                    .filter(StringUtils::isNotBlank)
                    .filter(this::isPinyinExpression)
                    .map(this::convertPinyinExpression)
                    .distinct()
                    .sorted(Comparator.comparingInt(String::length).reversed())
                    .collect(Collectors.toList()));
        }
        bo.setSortedSynonymList(memberContentList);
        bo.setSortedPinyinSynonymList(pinyinMemberContentList);
        bo.setRegexSynonymPatternList(regexMemberPatternList);
        return bo;
    }

    private String convertPinyinExpression(String pinyinExpression) {
        if (isPinyinExpression(pinyinExpression)) {
            return getPinyinContent(pinyinExpression);
        }
        return pinyinExpression;
    }

    private boolean validRegex(String regex) {
        try {
            Pattern.compile(regex);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    // 判断是否是拼音表达式
    private boolean isPinyinExpression(String member) {
        if (StringUtils.isBlank(member)) {
            return false;
        }
        return member.trim().startsWith("<") && member.trim().endsWith(">");
    }

    // 判断不是拼音表达式
    private boolean isNotPinyinExpression(String content) {
        return !isPinyinExpression(content);
    }

    private static String generateSynonymId() {
        long id = SYNONYM_ID_GENERATOR.incrementAndGet();
        return String.format("e%s", encodeId(id));
    }

    private static String encodeId(long id) {
        StringBuilder sb = new StringBuilder();
        while (id > 0) {
            int mod = (int) (id % LENGTH);
            sb.append(ALPHABET[mod]);
            id = id / LENGTH;
        }
        return sb.reverse().toString();
    }

}
