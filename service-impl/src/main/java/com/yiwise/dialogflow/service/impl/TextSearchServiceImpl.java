package com.yiwise.dialogflow.service.impl;

import com.aliyun.openservices.shade.org.apache.commons.lang3.BooleanUtils;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.entity.bo.ActionNameResourceBO;
import com.yiwise.dialogflow.entity.bo.LlmStepTextLocateBO;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.entity.enums.llm.LlmStepTypeEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.intent.IntentCorpusPO;
import com.yiwise.dialogflow.entity.po.intent.RuleActionParam;
import com.yiwise.dialogflow.entity.query.AnswerQuery;
import com.yiwise.dialogflow.entity.query.IntentCorpusQuery;
import com.yiwise.dialogflow.entity.query.IntentQuery;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.entity.vo.audio.BaseAnswerContentVO;
import com.yiwise.dialogflow.helper.IntentActionConfigCompareHelper;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.intent.IntentCorpusService;
import com.yiwise.dialogflow.service.intent.IntentRuleActionService;
import com.yiwise.dialogflow.service.intent.IntentService;
import com.yiwise.dialogflow.service.llm.LlmStepConfigService;
import com.yiwise.dialogflow.utils.AnswerLocateUtils;
import com.yiwise.dialogflow.utils.AnswerTextUtils;
import com.yiwise.dialogflow.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.assertj.core.util.Lists;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TextSearchServiceImpl implements TextSearchService {
    @Resource
    private IntentService intentService;
    @Resource
    private IntentCorpusService intentCorpusService;

    @Resource
    private AnswerManagerService answerManagerService;

    @Resource
    private BotService botService;

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private KnowledgeService knowledgeService;

    @Resource
    private StepService stepService;

    @Resource
    private StepNodeService stepNodeService;

    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    @Resource
    private IntentRuleActionService intentRuleActionService;

    @Resource
    private LlmStepConfigService llmStepConfigService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private VariableService variableService;

    @Resource
    private SourceRefService sourceRefService;

    private static final String PROMPT = "提示词";
    private static final String TEMPLATE_SENTENCE = "模板句";

    @Override
    public List<MutiBotTextSearchReplaceResultVO> search(BotTextSearchReplaceRequestVO botTextSearchReplaceRequestVO) {
        List<Long> botIdList = botTextSearchReplaceRequestVO.getBotIdList();
        Assert.notNull(botIdList, "查找的bot不能为空");
        List<BotPO> botVOList = botService.getByIdList(botIdList);
        Map<Long, String> botIdToNameMap = botVOList.stream().collect(Collectors.toMap(BotPO::getBotId, BotPO::getName));
        List<MutiBotTextSearchReplaceResultVO> mutiBotTextSearchReplaceResultVOList = new ArrayList<>();

        botIdList.forEach(botId -> {
            MutiBotTextSearchReplaceResultVO mutiBotTextSearchReplaceResultVO = new MutiBotTextSearchReplaceResultVO();
            mutiBotTextSearchReplaceResultVO.setBotId(botId);
            mutiBotTextSearchReplaceResultVO.setBotName(botIdToNameMap.get(botId));
            botTextSearchReplaceRequestVO.setBotId(botId);
            //关键、问法语料
            if (botTextSearchReplaceRequestVO.getSearchType().equals(BotTextSearchTypeEnum.KEYWORD)
                    || botTextSearchReplaceRequestVO.getSearchType().equals(BotTextSearchTypeEnum.CORPUS)) {
                IntentQuery.IntentQueryBuilder builder = IntentQuery.builder();
                builder.botId(botTextSearchReplaceRequestVO.getBotId()).intentType(IntentTypeEnum.SINGLE);
                if (!StringUtils.isEmpty(org.apache.commons.lang3.StringUtils.trimToEmpty(botTextSearchReplaceRequestVO.getSearchText()))) {
                    builder.keyword(org.apache.commons.lang3.StringUtils.trimToEmpty(botTextSearchReplaceRequestVO.getSearchText()));
                }
                IntentQuery intentQuery = builder.build();
                if (botTextSearchReplaceRequestVO.getSearchType().equals(BotTextSearchTypeEnum.KEYWORD)) {
                    //关键词
                    intentQuery.setIntentSearchScopeList(Collections.singletonList(IntentSearchScopeEnum.KEYWORD));
                } else if (botTextSearchReplaceRequestVO.getSearchType().equals(BotTextSearchTypeEnum.CORPUS)) {
                    //问法语料
                    intentQuery.setIntentSearchScopeList(Collections.singletonList(IntentSearchScopeEnum.CORPUS));
                }
                intentQuery.setWithPage(false);
                List<IntentVO> intentVOS = intentService.queryForList(intentQuery);
                mutiBotTextSearchReplaceResultVO.setBotTextSearchReplaceRequestVOList(wrapIntentVOList(intentVOS, botTextSearchReplaceRequestVO));
            } else if (isSearchAnswer(botTextSearchReplaceRequestVO.getSearchType())) {
                // 答案和变量
                mutiBotTextSearchReplaceResultVO.setBotTextSearchReplaceRequestVOList(searchAnswer(botTextSearchReplaceRequestVO));
            } else if (BotTextSearchTypeEnum.KNOWLEDGE_TITLE.equals(botTextSearchReplaceRequestVO.getSearchType())) {
                // 知识标题
                mutiBotTextSearchReplaceResultVO.setBotTextSearchReplaceRequestVOList(searchKnowledgeTitle(botTextSearchReplaceRequestVO));
            } else if (BotTextSearchTypeEnum.STEP_TITLE.equals(botTextSearchReplaceRequestVO.getSearchType())) {
                // 搜索对话流标题
                mutiBotTextSearchReplaceResultVO.setBotTextSearchReplaceRequestVOList(searchStepTitle(botTextSearchReplaceRequestVO));
            } else if (BotTextSearchTypeEnum.SMS_TEMPLATE.equals(botTextSearchReplaceRequestVO.getSearchType())) {
                // 搜索短信模板
                mutiBotTextSearchReplaceResultVO.setBotTextSearchReplaceRequestVOList(searchSmsTemplate(botTextSearchReplaceRequestVO));
            } else if (BotTextSearchTypeEnum.LLM_STEP.equals(botTextSearchReplaceRequestVO.getSearchType())) {
                // 搜索大模型流程
                mutiBotTextSearchReplaceResultVO.setBotTextSearchReplaceRequestVOList(searchLlmStep(botTextSearchReplaceRequestVO));
            } else if (BotTextSearchTypeEnum.SEND_SMS_ACTION.equals(botTextSearchReplaceRequestVO.getSearchType())) {
                // 搜索发短信动作
                mutiBotTextSearchReplaceResultVO.setBotTextSearchReplaceRequestVOList(searchSendSmsAction(botTextSearchReplaceRequestVO));
            } else if (BotTextSearchTypeEnum.ACTION.equals(botTextSearchReplaceRequestVO.getSearchType())) {
                // 搜索触发动作
                mutiBotTextSearchReplaceResultVO.setBotTextSearchReplaceRequestVOList(searchAction(botTextSearchReplaceRequestVO));
            } else if (BotTextSearchTypeEnum.TEMPLATE_VAR_PROMPT.equals(botTextSearchReplaceRequestVO.getSearchType())) {
                // 搜索模板变量提示词
                mutiBotTextSearchReplaceResultVO.setBotTextSearchReplaceRequestVOList(searchTemplateVarPrompt(botTextSearchReplaceRequestVO));
            }
            if (CollectionUtils.isNotEmpty(mutiBotTextSearchReplaceResultVO.getBotTextSearchReplaceRequestVOList())) {
                mutiBotTextSearchReplaceResultVOList.add(mutiBotTextSearchReplaceResultVO);
            }
        });

        return mutiBotTextSearchReplaceResultVOList;
    }

    private List<BotTextSearchReplaceResultVO> searchTemplateVarPrompt(BotTextSearchReplaceRequestVO request) {
        String searchText = request.getSearchText();
        if (StringUtils.isBlank(searchText)) {
            return Collections.emptyList();
        }
        List<VariablePO> variableList = variableService.getTemplateVariableByBotIdList(Collections.singletonList(request.getBotId()));
        if (CollectionUtils.isEmpty(variableList)) {
            return Collections.emptyList();
        }
        List<BotTextSearchReplaceResultVO> resultList = new ArrayList<>();
        for (VariablePO variable : variableList) {
            if (VariableTypeEnum.isTemplateVariable(variable.getType())
                    && TemplateVariableConfigTypeEnum.PROMPT.equals(variable.getTemplateVariableConfigType())) {
                boolean canDelete = CollectionUtils.isEmpty(sourceRefService.getBySourceIdList(request.getBotId(), Collections.singletonList(variable.getId()), SourceTypeEnum.VARIABLE));
                if (AnswerTextUtils.containsText(variable.getPrompt(), searchText)) {
                    BotTextSearchReplaceResultVO result = new BotTextSearchReplaceResultVO();
                    result.setSearchType(BotTextSearchTypeEnum.TEMPLATE_VAR_PROMPT);
                    result.setTargetId(variable.getId());
                    result.setTitle(variable.getName());
                    result.setDefaultAnswer("");
                    result.setCanDelete(canDelete);
                    result.setOriginText(variable.getPrompt());
                    result.setLocation(PROMPT);
                    resultList.add(result);
                }
                if (AnswerTextUtils.containsText(variable.getTemplateSentence(), searchText)) {
                    BotTextSearchReplaceResultVO result = new BotTextSearchReplaceResultVO();
                    result.setSearchType(BotTextSearchTypeEnum.TEMPLATE_VAR_PROMPT);
                    result.setTargetId(variable.getId());
                    result.setTitle(variable.getName());
                    result.setDefaultAnswer("");
                    result.setCanDelete(canDelete);
                    result.setOriginText(variable.getTemplateSentence());
                    result.setLocation(TEMPLATE_SENTENCE);
                    resultList.add(result);
                }
            }
        }
        return resultList;
    }

    private List<BotTextSearchReplaceResultVO> searchLlmStep(BotTextSearchReplaceRequestVO request) {
        String searchText = request.getSearchText();
        if (StringUtils.isBlank(searchText)) {
            return Collections.emptyList();
        }
        Long botId = request.getBotId();
        List<LlmStepConfigPO> llmStepConfigList = llmStepConfigService.getByBotId(botId);
        if (CollectionUtils.isEmpty(llmStepConfigList)) {
            return Collections.emptyList();
        }
        List<BotTextSearchReplaceResultVO> resultList = new ArrayList<>();
        for (LlmStepConfigPO llmStepConfig : llmStepConfigList) {
            String stepId = llmStepConfig.getStepId();
            StepVO step = stepService.getById(botId, stepId);
            if (Objects.isNull(step) || !step.isLlmStep()) {
                continue;
            }
            LlmStepTypeEnum llmStepType = step.getLlmStepType();
            if (LlmStepTypeEnum.isCollectTask(llmStepType)) {
                if (StringUtils.contains(llmStepConfig.getRoleDesc(), searchText)) {
                    resultList.add(buildLlmStepSearchResult(llmStepConfig.getRoleDesc(), step, LlmStepTextLocateEnum.ROLE_DESC, null, null, botId));
                }
                if (StringUtils.contains(llmStepConfig.getBackground(), searchText)) {
                    resultList.add(buildLlmStepSearchResult(llmStepConfig.getBackground(), step, LlmStepTextLocateEnum.BACKGROUND, null, null, botId));
                }
                if (CollectionUtils.isNotEmpty(llmStepConfig.getCollectTaskConfigList())) {
                    for (int i = 0; i < llmStepConfig.getCollectTaskConfigList().size(); i++) {
                        LlmStepCollectTaskConfigPO collectTaskConfig = llmStepConfig.getCollectTaskConfigList().get(i);
                        if (StringUtils.contains(collectTaskConfig.getDesc(), searchText)) {
                            resultList.add(buildLlmStepSearchResult(collectTaskConfig.getDesc(), step, LlmStepTextLocateEnum.TASK_DESC, i + 1, null, botId));
                        }
                        if (StringUtils.contains(collectTaskConfig.getGuideAnswer(), searchText)) {
                            resultList.add(buildLlmStepSearchResult(collectTaskConfig.getGuideAnswer(), step, LlmStepTextLocateEnum.GUIDE_ANSWER, i + 1, null, botId));
                        }
                        if (BooleanUtils.isTrue(collectTaskConfig.getEnableAssign()) && CollectionUtils.isNotEmpty(collectTaskConfig.getVariableAssignConfigList())) {
                            for (int j = 0; j < collectTaskConfig.getVariableAssignConfigList().size(); j++) {
                                LlmStepVariableAssignConfigPO config = collectTaskConfig.getVariableAssignConfigList().get(j);
                                if (StringUtils.contains(config.getDesc(), searchText)) {
                                    resultList.add(buildLlmStepSearchResult(config.getDesc(), step, LlmStepTextLocateEnum.COLLECT_TASK_VARIABLE_DESC, i + 1, j + 1, botId));
                                }
                            }
                        }
                    }
                }
            }
            if (LlmStepTypeEnum.isFreeConfig(llmStepType)) {
                if (StringUtils.contains(llmStepConfig.getPrompt(), searchText)) {
                    resultList.add(buildLlmStepSearchResult(llmStepConfig.getPrompt(), step, LlmStepTextLocateEnum.PROMPT, null, null, botId));
                }
                if (BooleanUtils.isTrue(llmStepConfig.getEnableAssign()) && CollectionUtils.isNotEmpty(llmStepConfig.getVariableAssignConfigList())) {
                    for (int i = 0; i < llmStepConfig.getVariableAssignConfigList().size(); i++) {
                        LlmStepVariableAssignConfigPO config = llmStepConfig.getVariableAssignConfigList().get(i);
                        if (StringUtils.contains(config.getDesc(), searchText)) {
                            resultList.add(buildLlmStepSearchResult(config.getDesc(), step, LlmStepTextLocateEnum.FREE_CONFIG_VARIABLE_DESC, null, i + 1, botId));
                        }
                    }
                }
            }
        }
        return resultList;
    }

    private List<BotTextSearchReplaceResultVO> searchAction(BotTextSearchReplaceRequestVO request) {
        List<BotAnswerSearchSourceEnum> answerSourceList = request.getAnswerSourceList();
        if (CollectionUtils.isEmpty(answerSourceList)) {
            return Collections.emptyList();
        }
        Long botId = request.getBotId();
        List<BotTextSearchReplaceResultVO> resultList = new ArrayList<>();
        boolean searchMainStep = answerSourceList.stream().anyMatch(BotAnswerSearchSourceEnum.MAIN_STEP::equals);
        boolean searchIndependentStep = answerSourceList.stream().anyMatch(BotAnswerSearchSourceEnum.INDEPENDENT_STEP::equals);
        boolean searchSpecialAnswer = answerSourceList.stream().anyMatch(BotAnswerSearchSourceEnum.SPECIAL_ANSWER::equals);
        boolean searchKnowledge = answerSourceList.stream().anyMatch(BotAnswerSearchSourceEnum.KNOWLEDGE::equals);

        ActionNameResourceBO actionNameResource = intentRuleActionService.getSourceId2NameMapByBotId(botId);
        if (searchMainStep || searchIndependentStep) {
            List<StepPO> stepList = stepService.getAllListByBotId(botId);
            if (searchMainStep) {
                resultList.addAll(searchActionFromStep(stepList, StepTypeEnum.MAIN, botId, actionNameResource));
            }
            if (searchIndependentStep) {
                resultList.addAll(searchActionFromStep(stepList, StepTypeEnum.INDEPENDENT, botId, actionNameResource));
            }
        }
        if (searchKnowledge) {
            List<KnowledgePO> knowledgeList = knowledgeService.getAllListByBotId(botId);
            if (CollectionUtils.isNotEmpty(knowledgeList)) {
                for (KnowledgePO knowledge : knowledgeList) {
                    if (BooleanUtils.isNotTrue(knowledge.getIsEnableAction())) {
                        continue;
                    }
                    resultList.add(buildActionSearchResult(knowledge.getActionList(), () -> AnswerLocateUtils.generate(knowledge), actionNameResource));
                }
            }
        }
        if (searchSpecialAnswer) {
            List<SpecialAnswerConfigPO> specialAnswerConfigList = specialAnswerConfigService.getByBotId(botId);
            if (CollectionUtils.isNotEmpty(specialAnswerConfigList)) {
                for (SpecialAnswerConfigPO specialAnswerConfig : specialAnswerConfigList) {
                    if (BooleanUtils.isNotTrue(specialAnswerConfig.getIsEnableAction())) {
                        continue;
                    }
                    resultList.add(buildActionSearchResult(specialAnswerConfig.getActionList(), () -> AnswerLocateUtils.generate(specialAnswerConfig), actionNameResource));
                }
            }
        }
        return resultList;    }

    private List<BotTextSearchReplaceResultVO> searchSendSmsAction(BotTextSearchReplaceRequestVO request) {
        List<BotAnswerSearchSourceEnum> answerSourceList = request.getAnswerSourceList();
        if (CollectionUtils.isEmpty(answerSourceList)) {
            return Collections.emptyList();
        }
        Long searchSmsId = request.getSearchSmsId();
        Long botId = request.getBotId();
        List<BotTextSearchReplaceResultVO> resultList = new ArrayList<>();
        boolean searchMainStep = answerSourceList.stream().anyMatch(BotAnswerSearchSourceEnum.MAIN_STEP::equals);
        boolean searchIndependentStep = answerSourceList.stream().anyMatch(BotAnswerSearchSourceEnum.INDEPENDENT_STEP::equals);
        boolean searchSpecialAnswer = answerSourceList.stream().anyMatch(BotAnswerSearchSourceEnum.SPECIAL_ANSWER::equals);
        boolean searchKnowledge = answerSourceList.stream().anyMatch(BotAnswerSearchSourceEnum.KNOWLEDGE::equals);

        ActionNameResourceBO actionNameResource = intentRuleActionService.getSourceId2NameMapByBotId(botId);
        if (searchMainStep || searchIndependentStep) {
            List<StepPO> stepList = stepService.getAllListByBotId(botId);
            if (searchMainStep) {
                resultList.addAll(searchSendSmsActionFromStep(stepList, StepTypeEnum.MAIN, botId, actionNameResource, searchSmsId));
            }
            if (searchIndependentStep) {
                resultList.addAll(searchSendSmsActionFromStep(stepList, StepTypeEnum.INDEPENDENT, botId, actionNameResource, searchSmsId));
            }
        }
        if (searchKnowledge) {
            List<KnowledgePO> knowledgeList = knowledgeService.getAllListByBotId(botId);
            if (CollectionUtils.isNotEmpty(knowledgeList)) {
                for (KnowledgePO knowledge : knowledgeList) {
                    if (BooleanUtils.isNotTrue(knowledge.getIsEnableAction()) || CollectionUtils.isEmpty(knowledge.getActionList())) {
                        continue;
                    }
                    Optional.ofNullable(buildSendSmsActionSearchResultFromAction(knowledge.getActionList(), () -> AnswerLocateUtils.generate(knowledge), actionNameResource, searchSmsId))
                            .ifPresent(resultList::add);
                }
            }
        }
        if (searchSpecialAnswer) {
            List<SpecialAnswerConfigPO> specialAnswerConfigList = specialAnswerConfigService.getByBotId(botId);
            if (CollectionUtils.isNotEmpty(specialAnswerConfigList)) {
                for (SpecialAnswerConfigPO specialAnswerConfig : specialAnswerConfigList) {
                    if (BooleanUtils.isNotTrue(specialAnswerConfig.getIsEnableAction()) || CollectionUtils.isEmpty(specialAnswerConfig.getActionList())) {
                        continue;
                    }
                    Optional.ofNullable(buildSendSmsActionSearchResultFromAction(specialAnswerConfig.getActionList(), () -> AnswerLocateUtils.generate(specialAnswerConfig), actionNameResource, searchSmsId))
                            .ifPresent(resultList::add);
                }
            }
        }
        return resultList;
    }

    private BotTextSearchReplaceResultVO buildLlmStepSearchResult(String text, StepPO step, LlmStepTextLocateEnum llmStepTextLocate, Integer taskConfigIndex, Integer assignConfigIndex, Long botId) {
        BotTextSearchReplaceResultVO resultVO = new BotTextSearchReplaceResultVO();
        resultVO.setOriginText(text);
        LlmStepTextLocateBO locate = LlmStepTextLocateBO.builder().stepId(step.getId()).stepName(step.getName()).stepLabel(step.getLabel())
                .locate(llmStepTextLocate).taskConfigIndex(taskConfigIndex).assignConfigIndex(assignConfigIndex).botId(botId).build();
        resultVO.setLlmStepTextLocate(locate);
        resultVO.setLocation(locate.toDisplayString());
        resultVO.setSearchType(BotTextSearchTypeEnum.LLM_STEP);
        resultVO.setDefaultAnswer("");
        resultVO.setCanDelete(false);
        return resultVO;
    }

    private List<BotTextSearchReplaceResultVO> searchSmsTemplate(BotTextSearchReplaceRequestVO request) {
        List<BotAnswerSearchSourceEnum> answerSourceList = request.getAnswerSourceList();
        if(CollectionUtils.isEmpty(answerSourceList)) {
            return Collections.emptyList();
        }
        Long botId = request.getBotId();
        Long searchSmsId = request.getSearchSmsId();
        List<BotTextSearchReplaceResultVO> resultList = new ArrayList<>();
        boolean searchMainStep = answerSourceList.stream().anyMatch(BotAnswerSearchSourceEnum.MAIN_STEP::equals);
        boolean searchIndependentStep = answerSourceList.stream().anyMatch(BotAnswerSearchSourceEnum.INDEPENDENT_STEP::equals);
        boolean searchSpecialAnswer = answerSourceList.stream().anyMatch(BotAnswerSearchSourceEnum.SPECIAL_ANSWER::equals);
        boolean searchKnowledge = answerSourceList.stream().anyMatch(BotAnswerSearchSourceEnum.KNOWLEDGE::equals);
        if (searchMainStep || searchIndependentStep) {
            List<StepPO> stepList = stepService.getAllListByBotId(botId);
            if (searchMainStep) {
                resultList.addAll(searchSmsFromStep(stepList, StepTypeEnum.MAIN, botId, searchSmsId));
            }
            if (searchIndependentStep) {
                resultList.addAll(searchSmsFromStep(stepList, StepTypeEnum.INDEPENDENT, botId, searchSmsId));
            }
        }
        if (searchKnowledge) {
            List<KnowledgePO> knowledgeList = knowledgeService.getAllListByBotId(botId);
            if (CollectionUtils.isNotEmpty(knowledgeList)) {
                for (KnowledgePO knowledge : knowledgeList) {
                    if (BooleanUtils.isNotTrue(knowledge.getIsEnableAction()) || CollectionUtils.isEmpty(knowledge.getActionList())) {
                        continue;
                    }
                    Optional.ofNullable(buildSmsSearchResultFromAction(knowledge.getActionList(), searchSmsId, () -> AnswerLocateUtils.generate(knowledge)))
                            .ifPresent(resultList::add);
                }
            }
        }
        if (searchSpecialAnswer) {
            List<SpecialAnswerConfigPO> specialAnswerConfigList = specialAnswerConfigService.getByBotId(botId);
            if (CollectionUtils.isNotEmpty(specialAnswerConfigList)) {
                for (SpecialAnswerConfigPO specialAnswerConfig : specialAnswerConfigList) {
                    if (BooleanUtils.isNotTrue(specialAnswerConfig.getIsEnableAction()) || CollectionUtils.isEmpty(specialAnswerConfig.getActionList())) {
                        continue;
                    }
                    Optional.ofNullable(buildSmsSearchResultFromAction(specialAnswerConfig.getActionList(), searchSmsId, () -> AnswerLocateUtils.generate(specialAnswerConfig)))
                            .ifPresent(resultList::add);
                }
            }
        }
        return resultList;
    }

    private BotTextSearchReplaceResultVO buildSendSmsActionSearchResultFromAction(List<RuleActionParam> actionList, Supplier<AnswerLocateBO> answerLocateSupplier,
                                                                                  ActionNameResourceBO actionNameResource, Long searchSmsId) {
        boolean match =
                actionList.stream().filter(action -> action.getActionType().equals(ActionCategoryEnum.SEND_SMS))
                        .anyMatch(
                                action -> Objects.isNull(searchSmsId)
                                        || CollectionUtils.isNotEmpty(action.getSourceIdList())
                                        && action.getSourceIdList().stream().map(IdNamePair::getId).collect(Collectors.toList()).contains(searchSmsId));
        if (match) {
            BotTextSearchReplaceResultVO vo = new BotTextSearchReplaceResultVO();
            vo.setSearchType(BotTextSearchTypeEnum.SEND_SMS_ACTION);
            vo.setDefaultAnswer("");
            vo.setCanDelete(false);
            AnswerLocateBO answerLocateBO = answerLocateSupplier.get();
            vo.setAnswerLocate(answerLocateBO);
            vo.setLocation(generateAnswerDisplayLocation(answerLocateBO));
            intentRuleActionService.addSourceName(actionList, actionNameResource);
            vo.setActionList(actionList);
            return vo;
        }
        return null;
    }

    private BotTextSearchReplaceResultVO buildActionSearchResult(List<RuleActionParam> actionList, Supplier<AnswerLocateBO> answerLocateSupplier, ActionNameResourceBO actionNameResource) {
        BotTextSearchReplaceResultVO vo = new BotTextSearchReplaceResultVO();
        vo.setSearchType(BotTextSearchTypeEnum.ACTION);
        vo.setDefaultAnswer("");
        vo.setCanDelete(false);
        AnswerLocateBO answerLocateBO = answerLocateSupplier.get();
        vo.setAnswerLocate(answerLocateBO);
        vo.setLocation(generateAnswerDisplayLocation(answerLocateBO));
        intentRuleActionService.addSourceName(actionList, actionNameResource);
        vo.setActionList(actionList);
        return vo;
    }

    private BotTextSearchReplaceResultVO buildSmsSearchResultFromAction(List<RuleActionParam> actionList, Long searchSmsId, Supplier<AnswerLocateBO> answerLocateSupplier) {
        boolean match =
                actionList.stream().filter(action -> action.getActionType().equals(ActionCategoryEnum.SEND_SMS))
                        .anyMatch(
                                action -> (Objects.isNull(searchSmsId) && CollectionUtils.isEmpty(action.getSourceIdList()))
                                        || (Objects.nonNull(searchSmsId) && CollectionUtils.isNotEmpty(action.getSourceIdList())
                                        && action.getSourceIdList().stream().map(IdNamePair::getId).collect(Collectors.toList()).contains(searchSmsId))
                        );
        if (match) {
            BotTextSearchReplaceResultVO vo = new BotTextSearchReplaceResultVO();
            vo.setSearchType(BotTextSearchTypeEnum.SMS_TEMPLATE);
            vo.setDefaultAnswer("");
            vo.setCanDelete(false);
            AnswerLocateBO answerLocateBO = answerLocateSupplier.get();
            vo.setAnswerLocate(answerLocateBO);
            vo.setLocation(generateAnswerDisplayLocation(answerLocateBO));
            return vo;
        }
        return null;
    }

    private List<BotTextSearchReplaceResultVO> searchSendSmsActionFromStep(List<StepPO> stepList, StepTypeEnum stepType, Long botId, ActionNameResourceBO actionNameResource, Long searchSmsId) {
        stepList = stepList.stream().filter(step -> stepType.equals(step.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stepList)) {
            return Collections.emptyList();
        }
        Map<String, StepPO> stepMap = MyCollectionUtils.listToMap(stepList, StepPO::getId);
        List<DialogBaseNodePO> nodeList = stepNodeService.getListByStepIdList(botId, stepMap.keySet());
        if (CollectionUtils.isEmpty(nodeList)) {
            return Collections.emptyList();
        }
        List<BotTextSearchReplaceResultVO> resultList = new ArrayList<>();
        for (DialogBaseNodePO node : nodeList) {
            if (BooleanUtils.isNotTrue(node.getIsEnableAction()) || CollectionUtils.isEmpty(node.getActionList())) {
                continue;
            }
            Optional.ofNullable(buildSendSmsActionSearchResultFromAction(node.getActionList(), () -> AnswerLocateUtils.generate(stepMap.get(node.getStepId()), node), actionNameResource, searchSmsId))
                    .ifPresent(resultList::add);
        }
        return resultList;
    }

    private List<BotTextSearchReplaceResultVO> searchActionFromStep(List<StepPO> stepList, StepTypeEnum stepType, Long botId, ActionNameResourceBO actionNameResource) {
        stepList = stepList.stream().filter(step -> stepType.equals(step.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stepList)) {
            return Collections.emptyList();
        }
        Map<String, StepPO> stepMap = MyCollectionUtils.listToMap(stepList, StepPO::getId);
        List<DialogBaseNodePO> nodeList = stepNodeService.getListByStepIdList(botId, stepMap.keySet());
        if (CollectionUtils.isEmpty(nodeList)) {
            return Collections.emptyList();
        }
        List<BotTextSearchReplaceResultVO> resultList = new ArrayList<>();
        for (DialogBaseNodePO node : nodeList) {
            if (BooleanUtils.isNotTrue(node.getIsEnableAction())) {
                continue;
            }
            resultList.add(buildActionSearchResult(node.getActionList(), () -> AnswerLocateUtils.generate(stepMap.get(node.getStepId()), node), actionNameResource));
        }
        return resultList;
    }

    private List<BotTextSearchReplaceResultVO> searchSmsFromStep(List<StepPO> stepList, StepTypeEnum stepType, Long botId, Long searchSmsId) {
        stepList = stepList.stream().filter(step -> stepType.equals(step.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stepList)) {
            return Collections.emptyList();
        }
        Map<String, StepPO> stepMap = MyCollectionUtils.listToMap(stepList, StepPO::getId);
        List<DialogBaseNodePO> nodeList = stepNodeService.getListByStepIdList(botId, stepMap.keySet());
        if (CollectionUtils.isEmpty(nodeList)) {
            return Collections.emptyList();
        }
        List<BotTextSearchReplaceResultVO> resultList = new ArrayList<>();
        for (DialogBaseNodePO node : nodeList) {
            if (BooleanUtils.isNotTrue(node.getIsEnableAction()) || CollectionUtils.isEmpty(node.getActionList())) {
                continue;
            }
            Optional.ofNullable(buildSmsSearchResultFromAction(node.getActionList(), searchSmsId, () -> AnswerLocateUtils.generate(stepMap.get(node.getStepId()), node)))
                    .ifPresent(resultList::add);
        }
        return resultList;
    }

    private List<BotTextSearchReplaceResultVO> searchKnowledgeTitle(BotTextSearchReplaceRequestVO request) {
        return knowledgeService.searchKnowledgeTitle(request.getBotId(), request.getSearchText()).stream()
                .map(knowledge -> {
                    BotTextSearchReplaceResultVO result = new BotTextSearchReplaceResultVO();
                    result.setSearchType(request.getSearchType());
                    result.setTitle(knowledge.getName());
                    result.setTargetId(knowledge.getId());
                    result.setDefaultAnswer("");
                    if (CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
                        result.setDefaultAnswer(knowledge.getAnswerList().get(0).getText());
                    }
                    result.setCanDelete(true);
                    return result;
                }).collect(Collectors.toList());

    }
    private List<BotTextSearchReplaceResultVO> searchStepTitle(BotTextSearchReplaceRequestVO request) {
        List<StepPO> stepList = stepService.getAllListByBotId(request.getBotId()).stream()
                .filter(step -> step.getName().contains(request.getSearchText()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stepList)) {
            return Collections.emptyList();
        }

        List<String> stepIdList = stepList.stream().map(StepPO::getId).collect(Collectors.toList());
        List<DialogBaseNodePO> nodeList = stepNodeService.getListByStepIdList(request.getBotId(), stepIdList);
        Map<String, List<DialogBaseNodePO>> stepNodeListMap = MyCollectionUtils.listToMapList(nodeList, DialogBaseNodePO::getStepId);
        return stepList.stream()
                .map(step -> {
                    BotTextSearchReplaceResultVO result = new BotTextSearchReplaceResultVO();
                    result.setSearchType(request.getSearchType());
                    result.setTitle(step.getName());
                    result.setTargetId(step.getId());
                    result.setDefaultAnswer("");
                    result.setCanDelete(true);
                    List<DialogBaseNodePO> nodePOList = stepNodeListMap.get(step.getId());
                    if (CollectionUtils.isNotEmpty(nodePOList)) {
                        // 获取根节点
                        stepNodeService.getStepRootNode(nodePOList).stream()
                                .filter(item -> CollectionUtils.isNotEmpty(item.getAnswerList()))
                                .findFirst()
                                .ifPresent(node -> {
                                    result.setDefaultAnswer(node.getAnswerList().get(0).getText());
                                });
                    }

                    // 判断是否可以删除
                    if (StepTypeEnum.MAIN.equals(step.getType()) && step.getOrderNum() <= 0) {
                        result.setCanDelete(false);
                    }
                    // 是否被依赖 todo 批量查询
                    // 流程只能被流程的跳转节点或者问答知识设置的重复主流程所引用
                    stepNodeService.getByDependStep(step.getBotId(), step.getId()).stream()
                            .findAny()
                            .ifPresent(node -> {
                                result.setCanDelete(false);
                            });

                    // 问答知识的检查暂时不处理, 对对话逻辑没影响
                    knowledgeService.getByDependStepId(step.getBotId(), step.getId()).stream()
                            .findAny()
                            .ifPresent(knowledge -> {
                                result.setCanDelete(false);
                            });
                    return result;
                }).collect(Collectors.toList());
    }

    private List<BotTextSearchReplaceResultVO> searchAnswer(BotTextSearchReplaceRequestVO request) {
        if (CollectionUtils.isEmpty(request.getAnswerSourceList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "搜索来源不能为空");
        }

        AnswerQuery query = new AnswerQuery();
        query.setSearch(request.getSearchText());
        List<AnswerSourceEnum> answerSourceList = new ArrayList<>();
        List<StepTypeEnum> stepTypeList = new ArrayList<>();
        for (BotAnswerSearchSourceEnum answerSource : request.getAnswerSourceList()) {
            switch (answerSource) {
                case SPECIAL_ANSWER:
                    answerSourceList.add(AnswerSourceEnum.SPECIAL_ANSWER);
                    break;
                case KNOWLEDGE:
                    answerSourceList.add(AnswerSourceEnum.KNOWLEDGE);
                    break;
                case MAIN_STEP:
                    answerSourceList.add(AnswerSourceEnum.STEP);
                    stepTypeList.add(StepTypeEnum.MAIN);
                    break;
                case INDEPENDENT_STEP:
                    answerSourceList.add(AnswerSourceEnum.STEP);
                    stepTypeList.add(StepTypeEnum.INDEPENDENT);
                    break;
                default:
                    break;
            }
        }
        query.setAnswerSourceSet(new HashSet<>(answerSourceList));
        query.setStepTypeList(stepTypeList);
        int type = AnswerQuery.SEARCH_TYPE_ANSWER;
        if (BotTextSearchTypeEnum.CUSTOM_VARIABLE.equals(request.getSearchType())) {
            type = AnswerQuery.SEARCH_TYPE_CUSTOM_VARIABLE;
        } else if (BotTextSearchTypeEnum.DYNAMIC_VARIABLE.equals(request.getSearchType())) {
            type = AnswerQuery.SEARCH_TYPE_DYNAMIC_VARIABLE;
        }
        query.setSearchType(type);
        query.setBotId(request.getBotId());
        query.setSearch(org.apache.commons.lang3.StringUtils.trimToEmpty(request.getSearchText()));
        List<BaseAnswerContentVO> resultList = answerManagerService.queryByCondition(query);

        return resultList.stream()
                .map(answer -> {
                    BotTextSearchReplaceResultVO result = new BotTextSearchReplaceResultVO();
                    result.setSearchType(request.getSearchType());
                    result.setOriginText(answer.getText());
                    result.setAnswerLocate(answer.getLocate());
                    result.setLocation(generateAnswerDisplayLocation(answer.getLocate()));
                    result.setAction(answer.getAction());
                    return result;
                }).collect(Collectors.toList());
    }

    private String generateAnswerDisplayLocation(AnswerLocateBO locate) {
        switch (locate.getAnswerSource()) {
            case STEP:
                return String.format("%s:%s", locate.getNodeLabel(), locate.getNodeName());
            case SPECIAL_ANSWER:
                return String.format("%s:%s", locate.getSpecialAnswerConfigLabel(), locate.getSpecialAnswerConfigName());
            case KNOWLEDGE:
                return String.format("%s:%s", locate.getKnowledgeLabel(), locate.getKnowledgeName());
            default:
                return "";
        }
    }

    private boolean isSearchAnswer(BotTextSearchTypeEnum searchType) {
        return BotTextSearchTypeEnum.ANSWER.equals(searchType)
                || BotTextSearchTypeEnum.CUSTOM_VARIABLE.equals(searchType)
                || BotTextSearchTypeEnum.DYNAMIC_VARIABLE.equals(searchType);
    }

    @Override
    public void replace(MutiBotTextSearchReplaceRequestVO multiRequest) {
        List<BotTextSearchReplaceRequestVO> botTextSearchReplaceRequestVOList = multiRequest.getBotTextSearchReplaceRequestVOList();
        Assert.notNull(botTextSearchReplaceRequestVOList, "需要替换的记录不能为空");
        botTextSearchReplaceRequestVOList.forEach(request -> {
            request.setReplaceText(multiRequest.getReplaceText());
            request.setSearchText(multiRequest.getSearchText());
            request.setSearchSmsId(multiRequest.getSearchSmsId());
            request.setReplaceSmsId(multiRequest.getReplaceSmsId());
            request.setReplaceRuleActionParam(multiRequest.getReplaceRuleActionParam());
            doReplace(request, multiRequest.getUserId());
            updateBotToDraft(request.getBotId());
        });
    }

    @Override
    public void batchUpdate(MutiBotTextSearchReplaceRequestVO request) {
        List<BotTextSearchReplaceRequestVO> botTextSearchReplaceRequestVOList = request.getBotTextSearchReplaceRequestVOList();
        Assert.notNull(botTextSearchReplaceRequestVOList, "需要替换的记录不能为空");
        botTextSearchReplaceRequestVOList.forEach(botTextSearchReplaceRequestVO -> {
            doBatchUpdate(botTextSearchReplaceRequestVO, request.getUserId());
            updateBotToDraft(botTextSearchReplaceRequestVO.getBotId());
        });
    }

    private void doBatchUpdate(BotTextSearchReplaceRequestVO request, Long userId) {
        if (CollectionUtils.isEmpty(request.getBotTextSearchReplaceResultVOList())) {
            return;
        }
        for (BotTextSearchReplaceResultVO item : request.getBotTextSearchReplaceResultVOList()) {
            if (Objects.nonNull(item.getSearchType())) {
                request.setSearchType(item.getSearchType());
                break;
            }
        }
        switch (request.getSearchType()) {
            case ANSWER:
            case DYNAMIC_VARIABLE:
            case CUSTOM_VARIABLE:
                doBatchSaveAnswer(request, userId);
                break;
            case CORPUS:
                List<BotTextSearchReplaceResultVO> keyWordsList = request.getBotTextSearchReplaceResultVOList().stream()
                        .filter(item -> item.getSearchType().equals(BotTextSearchTypeEnum.CORPUS))
                        .collect(Collectors.toList());
                updateOrDeleteIntent(keyWordsList, request);
                break;
            case KEYWORD:
                List<BotTextSearchReplaceResultVO> corpusList = request.getBotTextSearchReplaceResultVOList().stream()
                        .filter(item -> item.getSearchType().equals(BotTextSearchTypeEnum.KEYWORD))
                        .collect(Collectors.toList());
                updateOrDeleteIntent(corpusList, request);
                break;
            case LLM_STEP:
                List<BotTextSearchReplaceResultVO> llmStepList = request.getBotTextSearchReplaceResultVOList().stream()
                        .filter(item -> item.getSearchType().equals(BotTextSearchTypeEnum.LLM_STEP))
                        .collect(Collectors.toList());
                llmStepList.forEach(item -> updateLlmStep(item.getLlmStepTextLocate(), item.getReplaceResultText(), userId));
                break;
            case SEND_SMS_ACTION:
                List<BotTextSearchReplaceResultVO> sendSmsActionList = request.getBotTextSearchReplaceResultVOList().stream()
                        .filter(item -> item.getSearchType().equals(BotTextSearchTypeEnum.SEND_SMS_ACTION))
                        .collect(Collectors.toList());
                sendSmsActionList.forEach(item -> updateSendSmsAction(item.getAnswerLocate(), item.getReplaceRuleActionParam(), request.getBotId(), userId));
                break;
            case TEMPLATE_VAR_PROMPT:
                List<BotTextSearchReplaceResultVO> templateVarPromptList = request.getBotTextSearchReplaceResultVOList().stream()
                        .filter(item -> item.getSearchType().equals(BotTextSearchTypeEnum.TEMPLATE_VAR_PROMPT))
                        .collect(Collectors.toList());
                templateVarPromptList.forEach(item -> updateTemplateVarPrompt(item.getTargetId(), item.getLocation(), item.getReplaceResultText(), request.getBotId(), userId));
            default:
                break;
        }
    }

    private void updateTemplateVarPrompt(String varId, String location, String replaceResultText, Long botId, Long userId) {
        VariablePO variable = variableService.getById(botId, varId);
        if (Objects.isNull(variable) || VariableTypeEnum.isNotTemplateVariable(variable.getType()) || !TemplateVariableConfigTypeEnum.PROMPT.equals(variable.getTemplateVariableConfigType())) {
            return;
        }
        if (StringUtils.equals(location, PROMPT)) {
            variable.setPrompt(replaceResultText);
            variableService.update(variable, userId);
        } else if (StringUtils.equals(location, TEMPLATE_SENTENCE)) {
            variable.setTemplateSentence(replaceResultText);
            variableService.update(variable, userId);
        }
    }

    private void updateSendSmsAction(AnswerLocateBO answerLocate, RuleActionParam replaceRuleActionParam, Long botId, Long userId) {
        replaceSendSmsAction(answerLocate, botId, replaceRuleActionParam, intentRuleActionService.getSourceId2NameMapByBotId(botId), userId);
    }

    private void doBatchSaveAnswer(BotTextSearchReplaceRequestVO request, Long userId) {
        if (CollectionUtils.isEmpty(request.getBotTextSearchReplaceResultVOList())) {
            return;
        }
        answerManagerService.updateAnswer(request.getBotId(), request.getBotTextSearchReplaceResultVOList(), userId);
    }

    private void updateBotToDraft(Long botId) {
        botService.updateAuditStatus(botId, AuditStatusEnum.DRAFT);
    }

    public void doReplace(BotTextSearchReplaceRequestVO botTextSearchReplaceRequestVO, Long userId) {
        if (CollectionUtils.isEmpty(botTextSearchReplaceRequestVO.getBotTextSearchReplaceResultVOList())) {
            return;
        }
        for (BotTextSearchReplaceResultVO item : botTextSearchReplaceRequestVO.getBotTextSearchReplaceResultVOList()) {
            if (Objects.nonNull(item.getSearchType())) {
                botTextSearchReplaceRequestVO.setSearchType(item.getSearchType());
                break;
            }
        }

        //关键词替换
        List<BotTextSearchReplaceResultVO> keyWordsList = botTextSearchReplaceRequestVO.getBotTextSearchReplaceResultVOList().stream().filter(item -> item.getSearchType().equals(BotTextSearchTypeEnum.KEYWORD)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(keyWordsList)) {
            botTextSearchReplaceRequestVO.setSearchType(BotTextSearchTypeEnum.KEYWORD);
            if (StringUtils.isEmpty(botTextSearchReplaceRequestVO.getReplaceText())) {
                replaceOrDeleteIntent(keyWordsList, botTextSearchReplaceRequestVO, true);
                return;
            }
            replaceOrDeleteIntent(keyWordsList, botTextSearchReplaceRequestVO, false);
            return;
        }
        //问法语料替换
        List<BotTextSearchReplaceResultVO> corpusList = botTextSearchReplaceRequestVO.getBotTextSearchReplaceResultVOList().stream().filter(item -> item.getSearchType().equals(BotTextSearchTypeEnum.CORPUS)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(corpusList)) {
            botTextSearchReplaceRequestVO.setSearchType(BotTextSearchTypeEnum.CORPUS);
            if (StringUtils.isEmpty(botTextSearchReplaceRequestVO.getReplaceText())) {
                replaceOrDeleteIntent(corpusList, botTextSearchReplaceRequestVO, true);
                return;
            }
            replaceOrDeleteIntent(corpusList, botTextSearchReplaceRequestVO, false);
            return;
        }
        // 短信替换
        List<BotTextSearchReplaceResultVO> smsList = botTextSearchReplaceRequestVO.getBotTextSearchReplaceResultVOList().stream().filter(item -> item.getSearchType().equals(BotTextSearchTypeEnum.SMS_TEMPLATE)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(smsList)) {
            botTextSearchReplaceRequestVO.setSearchType(BotTextSearchTypeEnum.SMS_TEMPLATE);
            if (!Objects.equals(botTextSearchReplaceRequestVO.getSearchSmsId(), botTextSearchReplaceRequestVO.getReplaceSmsId())) {
                smsList.forEach(sms -> replaceSms(sms.getAnswerLocate(), botTextSearchReplaceRequestVO.getBotId(), botTextSearchReplaceRequestVO.getSearchSmsId(),
                        botTextSearchReplaceRequestVO.getReplaceSmsId(), intentRuleActionService.getSourceId2NameMapByBotId(botTextSearchReplaceRequestVO.getBotId()), userId));
            }
            return;
        }
        // 发短信动作替换
        List<BotTextSearchReplaceResultVO> sendSmsActionList = botTextSearchReplaceRequestVO.getBotTextSearchReplaceResultVOList().stream().filter(item -> item.getSearchType().equals(BotTextSearchTypeEnum.SEND_SMS_ACTION)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sendSmsActionList)) {
            botTextSearchReplaceRequestVO.setSearchType(BotTextSearchTypeEnum.SEND_SMS_ACTION);
            sendSmsActionList.forEach(sendSmsAction -> replaceSendSmsAction(sendSmsAction.getAnswerLocate(), botTextSearchReplaceRequestVO.getBotId(),
                    botTextSearchReplaceRequestVO.getReplaceRuleActionParam(), intentRuleActionService.getSourceId2NameMapByBotId(botTextSearchReplaceRequestVO.getBotId()), userId));
            return;
        }
        // 触发动作替换
        List<BotTextSearchReplaceResultVO> actionList = botTextSearchReplaceRequestVO.getBotTextSearchReplaceResultVOList().stream().filter(item -> item.getSearchType().equals(BotTextSearchTypeEnum.ACTION)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(actionList)) {
            botTextSearchReplaceRequestVO.setSearchType(BotTextSearchTypeEnum.ACTION);
            actionList.forEach(action -> disableAction(action.getAnswerLocate(), botTextSearchReplaceRequestVO.getBotId(), userId));
            return;
        }
        // 大模型流程内容替换
        List<BotTextSearchReplaceResultVO> llmStepList = botTextSearchReplaceRequestVO.getBotTextSearchReplaceResultVOList().stream().filter(item -> item.getSearchType().equals(BotTextSearchTypeEnum.LLM_STEP)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(llmStepList)) {
            botTextSearchReplaceRequestVO.setSearchType(BotTextSearchTypeEnum.LLM_STEP);
            if (StringUtils.isNotEmpty(botTextSearchReplaceRequestVO.getReplaceText())) {
                llmStepList.forEach(llmStep -> replaceLlmStep(llmStep.getLlmStepTextLocate(), botTextSearchReplaceRequestVO.getSearchText(), botTextSearchReplaceRequestVO.getReplaceText(), userId));
            }
            return;
        }
        // 模板变量提示词替换
        List<BotTextSearchReplaceResultVO> templateVarPromptList = botTextSearchReplaceRequestVO.getBotTextSearchReplaceResultVOList().stream().filter(item -> item.getSearchType().equals(BotTextSearchTypeEnum.TEMPLATE_VAR_PROMPT)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(templateVarPromptList)) {
            botTextSearchReplaceRequestVO.setSearchType(BotTextSearchTypeEnum.TEMPLATE_VAR_PROMPT);
            templateVarPromptList.forEach(templateVarPrompt -> replaceTemplateVarPrompt(botTextSearchReplaceRequestVO.getSearchText(), botTextSearchReplaceRequestVO.getReplaceText(), botTextSearchReplaceRequestVO.getBotId(), templateVarPrompt.getTargetId(), templateVarPrompt.getLocation(), userId));
            return;
        }
        if (isSearchAnswer(botTextSearchReplaceRequestVO.getSearchType())) {
            replaceAnswer(botTextSearchReplaceRequestVO, userId);
        }
    }

    private void replaceTemplateVarPrompt(String searchText, String replaceText, Long botId, String varId, String location, Long userId) {
        VariablePO variable = variableService.getById(botId, varId);
        if (Objects.isNull(variable) || VariableTypeEnum.isNotTemplateVariable(variable.getType()) || !TemplateVariableConfigTypeEnum.PROMPT.equals(variable.getTemplateVariableConfigType())) {
            return;
        }
        if (StringUtils.equals(location, PROMPT) && StringUtils.contains(variable.getPrompt(), searchText)) {
            variable.setPrompt(AnswerTextUtils.replaceText(variable.getPrompt(), searchText, replaceText));
            variableService.update(variable, userId);
        } else if (StringUtils.equals(location, TEMPLATE_SENTENCE) && StringUtils.contains(variable.getTemplateSentence(), searchText)) {
            variable.setTemplateSentence(AnswerTextUtils.replaceText(variable.getTemplateSentence(), searchText, replaceText));
            variableService.update(variable, userId);
        }
    }

    private void updateLlmStep(LlmStepTextLocateBO locate, String newText, Long userId) {
        if (StringUtils.isBlank(newText)) {
            return;
        }
        LlmStepConfigPO llmStepConfig = llmStepConfigService.getByStepId(locate.getBotId(), locate.getStepId());
        if (Objects.isNull(llmStepConfig)) {
            return;
        }
        switch (locate.getLocate()) {
            case PROMPT: llmStepConfig.setPrompt(newText); break;
            case ROLE_DESC: llmStepConfig.setRoleDesc(newText); break;
            case BACKGROUND: llmStepConfig.setBackground(newText); break;
            case TASK_DESC: {
                if (CollectionUtils.isNotEmpty(llmStepConfig.getCollectTaskConfigList()) && llmStepConfig.getCollectTaskConfigList().size() >= locate.getTaskConfigIndex()) {
                    LlmStepCollectTaskConfigPO taskConfig = llmStepConfig.getCollectTaskConfigList().get(locate.getTaskConfigIndex() - 1);
                    taskConfig.setDesc(newText);
                }
                break;
            }
            case GUIDE_ANSWER: {
                if (CollectionUtils.isNotEmpty(llmStepConfig.getCollectTaskConfigList()) && llmStepConfig.getCollectTaskConfigList().size() >= locate.getTaskConfigIndex()) {
                    LlmStepCollectTaskConfigPO taskConfig = llmStepConfig.getCollectTaskConfigList().get(locate.getTaskConfigIndex() - 1);
                    taskConfig.setGuideAnswer(newText);
                }
                break;
            }
            case COLLECT_TASK_VARIABLE_DESC: {
                if (CollectionUtils.isNotEmpty(llmStepConfig.getCollectTaskConfigList()) && llmStepConfig.getCollectTaskConfigList().size() >= locate.getTaskConfigIndex()) {
                    LlmStepCollectTaskConfigPO taskConfig = llmStepConfig.getCollectTaskConfigList().get(locate.getTaskConfigIndex() - 1);
                    if (CollectionUtils.isNotEmpty(taskConfig.getVariableAssignConfigList()) && taskConfig.getVariableAssignConfigList().size() >= locate.getAssignConfigIndex()) {
                        LlmStepVariableAssignConfigPO assignConfig = taskConfig.getVariableAssignConfigList().get(locate.getAssignConfigIndex() - 1);
                        assignConfig.setDesc(newText);
                    }
                }
                break;
            }
            case FREE_CONFIG_VARIABLE_DESC: {
                if (BooleanUtils.isTrue(llmStepConfig.getEnableAssign()) && CollectionUtils.isNotEmpty(llmStepConfig.getVariableAssignConfigList()) && llmStepConfig.getVariableAssignConfigList().size() >= locate.getAssignConfigIndex()) {
                    LlmStepVariableAssignConfigPO assignConfig = llmStepConfig.getVariableAssignConfigList().get(locate.getAssignConfigIndex() - 1);
                    assignConfig.setDesc(newText);
                }
                break;
            }
        }
        llmStepConfigService.save(llmStepConfig, userId);
    }

    private void replaceLlmStep(LlmStepTextLocateBO locate, String searchText, String replaceText, Long userId) {
        LlmStepConfigPO llmStepConfig = llmStepConfigService.getByStepId(locate.getBotId(), locate.getStepId());
        if (Objects.isNull(llmStepConfig)) {
            return;
        }
        switch (locate.getLocate()) {
            case PROMPT: llmStepConfig.setPrompt(StringUtils.replace(llmStepConfig.getPrompt(), searchText, replaceText)); break;
            case ROLE_DESC: llmStepConfig.setRoleDesc(StringUtils.replace(llmStepConfig.getRoleDesc(), searchText, replaceText)); break;
            case BACKGROUND: llmStepConfig.setBackground(StringUtils.replace(llmStepConfig.getBackground(), searchText, replaceText)); break;
            case TASK_DESC: {
                if (CollectionUtils.isNotEmpty(llmStepConfig.getCollectTaskConfigList()) && llmStepConfig.getCollectTaskConfigList().size() >= locate.getTaskConfigIndex()) {
                    LlmStepCollectTaskConfigPO taskConfig = llmStepConfig.getCollectTaskConfigList().get(locate.getTaskConfigIndex() - 1);
                    taskConfig.setDesc(StringUtils.replace(taskConfig.getDesc(), searchText, replaceText));
                }
                break;
            }
            case GUIDE_ANSWER: {
                if (CollectionUtils.isNotEmpty(llmStepConfig.getCollectTaskConfigList()) && llmStepConfig.getCollectTaskConfigList().size() >= locate.getTaskConfigIndex()) {
                    LlmStepCollectTaskConfigPO taskConfig = llmStepConfig.getCollectTaskConfigList().get(locate.getTaskConfigIndex() - 1);
                    taskConfig.setGuideAnswer(StringUtils.replace(taskConfig.getGuideAnswer(), searchText, replaceText));
                }
                break;
            }
            case COLLECT_TASK_VARIABLE_DESC: {
                if (CollectionUtils.isNotEmpty(llmStepConfig.getCollectTaskConfigList()) && llmStepConfig.getCollectTaskConfigList().size() >= locate.getTaskConfigIndex()) {
                    LlmStepCollectTaskConfigPO taskConfig = llmStepConfig.getCollectTaskConfigList().get(locate.getTaskConfigIndex() - 1);
                    if (BooleanUtils.isTrue(taskConfig.getEnableAssign()) && CollectionUtils.isNotEmpty(taskConfig.getVariableAssignConfigList()) && taskConfig.getVariableAssignConfigList().size() >= locate.getAssignConfigIndex()) {
                        LlmStepVariableAssignConfigPO assignConfig = taskConfig.getVariableAssignConfigList().get(locate.getAssignConfigIndex() - 1);
                        assignConfig.setDesc(StringUtils.replace(assignConfig.getDesc(), searchText, replaceText));
                    }
                }
                break;
            }
            case FREE_CONFIG_VARIABLE_DESC: {
                if (BooleanUtils.isTrue(llmStepConfig.getEnableAssign()) && CollectionUtils.isNotEmpty(llmStepConfig.getVariableAssignConfigList()) && llmStepConfig.getVariableAssignConfigList().size() >= locate.getAssignConfigIndex()) {
                    LlmStepVariableAssignConfigPO assignConfig = llmStepConfig.getVariableAssignConfigList().get(locate.getAssignConfigIndex() - 1);
                    assignConfig.setDesc(StringUtils.replace(assignConfig.getDesc(), searchText, replaceText));
                }
                break;
            }
        }
        llmStepConfigService.save(llmStepConfig, userId);
    }

    private void replaceSendSmsAction(AnswerLocateBO answerLocate, Long botId, RuleActionParam replaceRuleActionParam, ActionNameResourceBO actionNameResourceBO, Long userId) {
        switch (answerLocate.getAnswerSource()) {
            case STEP:
                replaceSendSmsAction(answerLocate, botId, replaceRuleActionParam, actionNameResourceBO, userId,
                        () -> stepNodeService.getById(botId, answerLocate.getStepId(), answerLocate.getNodeId()),
                        DialogBaseNodePO::getIsEnableAction, DialogBaseNodePO::getActionList,
                        node -> stepNodeService.updateActionList(node)
                );
                break;
            case KNOWLEDGE:
                replaceSendSmsAction(answerLocate, botId, replaceRuleActionParam, actionNameResourceBO, userId,
                        () -> knowledgeService.getById(botId, answerLocate.getKnowledgeId()),
                        KnowledgePO::getIsEnableAction, KnowledgePO::getActionList,
                        knowledge -> knowledgeService.updateActionList(knowledge)
                );
                break;
            case SPECIAL_ANSWER:
                replaceSendSmsAction(answerLocate, botId, replaceRuleActionParam, actionNameResourceBO, userId,
                        () -> specialAnswerConfigService.getById(botId, answerLocate.getSpecialAnswerConfigId()),
                        SpecialAnswerConfigPO::getIsEnableAction, SpecialAnswerConfigPO::getActionList,
                        config -> specialAnswerConfigService.updateActionList(config)
                );
                break;
            default:
                break;
        }
    }

    private void disableAction(AnswerLocateBO answerLocate, Long botId, Long userId) {
        switch (answerLocate.getAnswerSource()) {
            case STEP:
                disableAction(answerLocate, botId, userId,
                        () -> stepNodeService.getById(botId, answerLocate.getStepId(), answerLocate.getNodeId()),
                        DialogBaseNodePO::getIsEnableAction, DialogBaseNodePO::getActionList,
                        node -> {
                            node.setIsEnableAction(false);
                            node.setActionList(Collections.emptyList());
                            mongoTemplate.save(node, DialogBaseNodePO.COLLECTION_NAME);
                        }
                );
                break;
            case KNOWLEDGE:
                disableAction(answerLocate, botId, userId,
                        () -> knowledgeService.getById(botId, answerLocate.getKnowledgeId()),
                        KnowledgePO::getIsEnableAction, KnowledgePO::getActionList,
                        knowledge -> {
                            knowledge.setIsEnableAction(false);
                            knowledge.setActionList(Collections.emptyList());
                            mongoTemplate.save(knowledge, KnowledgePO.COLLECTION_NAME);
                        }
                );
                break;
            case SPECIAL_ANSWER:
                disableAction(answerLocate, botId, userId,
                        () -> specialAnswerConfigService.getById(botId, answerLocate.getSpecialAnswerConfigId()),
                        SpecialAnswerConfigPO::getIsEnableAction, SpecialAnswerConfigPO::getActionList,
                        config -> {
                            config.setIsEnableAction(false);
                            config.setActionList(Collections.emptyList());
                            mongoTemplate.save(config, SpecialAnswerConfigPO.COLLECTION_NAME);
                        }
                );
                break;
            default:
                break;
        }
    }

    private <T> void disableAction(AnswerLocateBO answerLocate, Long botId, Long userId,
                                   Supplier<T> sourceProvider, Function<T, Boolean> isEnableActionProvider, Function<T, List<RuleActionParam>> actionListProvider, Consumer<T> sourceUpdater) {
        T entity = sourceProvider.get();
        if (Objects.isNull(entity)) {
            return;
        }
        boolean isEnableAction = isEnableActionProvider.apply(entity);
        if (BooleanUtils.isNotTrue(isEnableAction)) {
            return;
        }
        String log = String.format("【%s】停用触发动作配置", generateAnswerDisplayLocation(answerLocate));
        sourceUpdater.accept(entity);
        operationLogService.save(botId, OperationLogTypeEnum.SEARCH_REPLACE, OperationLogResourceTypeEnum.REPLACE, log, userId);
    }

    private void replaceSms(AnswerLocateBO answerLocate, Long botId, Long searchSmsId, Long replaceSmsId, ActionNameResourceBO actionNameResourceBO, Long userId) {
        switch (answerLocate.getAnswerSource()) {
            case STEP:
                replaceSms(answerLocate, botId, searchSmsId, replaceSmsId, actionNameResourceBO, userId,
                        () -> stepNodeService.getById(botId, answerLocate.getStepId(), answerLocate.getNodeId()),
                        DialogBaseNodePO::getIsEnableAction, DialogBaseNodePO::getActionList,
                        node -> stepNodeService.updateActionList(node)
                );
                break;
            case KNOWLEDGE:
                replaceSms(answerLocate, botId, searchSmsId, replaceSmsId, actionNameResourceBO, userId,
                        () -> knowledgeService.getById(botId, answerLocate.getKnowledgeId()),
                        KnowledgePO::getIsEnableAction, KnowledgePO::getActionList,
                        knowledge -> knowledgeService.updateActionList(knowledge)
                );
                break;
            case SPECIAL_ANSWER:
                replaceSms(answerLocate, botId, searchSmsId, replaceSmsId, actionNameResourceBO, userId,
                        () -> specialAnswerConfigService.getById(botId, answerLocate.getSpecialAnswerConfigId()),
                        SpecialAnswerConfigPO::getIsEnableAction, SpecialAnswerConfigPO::getActionList,
                        config -> specialAnswerConfigService.updateActionList(config)
                );
                break;
            default:
                break;
        }
    }

    private <T> void replaceSendSmsAction(AnswerLocateBO answerLocate, Long botId, RuleActionParam replaceRuleActionParam, ActionNameResourceBO actionNameResourceBO, Long userId,
                                Supplier<T> sourceProvider, Function<T, Boolean> isEnableActionProvider, Function<T, List<RuleActionParam>> actionListProvider, Consumer<T> sourceUpdater) {
        T entity = sourceProvider.get();
        if (Objects.isNull(entity)) {
            return;
        }
        boolean isEnableAction = isEnableActionProvider.apply(entity);
        List<RuleActionParam> actionList = actionListProvider.apply(entity);
        if (BooleanUtils.isNotTrue(isEnableAction) || CollectionUtils.isEmpty(actionList)) {
            return;
        }
        RuleActionParam action = actionList.stream().filter(e -> ActionCategoryEnum.SEND_SMS.equals(e.getActionType())).findFirst().orElse(null);
        if (Objects.isNull(action)) {
            return;
        }
        String log;
        if (Objects.isNull(replaceRuleActionParam)) {
            actionList.removeIf(e -> ActionCategoryEnum.SEND_SMS.equals(e.getActionType()));
            log = String.format("【%s】动作配置删除发短信", generateAnswerDisplayLocation(answerLocate));
        } else {
            actionList.removeIf(e -> ActionCategoryEnum.SEND_SMS.equals(e.getActionType()));
            actionList.add(replaceRuleActionParam);
            log = String.format("【%s】动作配置发短信配置【%s】替换为【%s】", generateAnswerDisplayLocation(answerLocate), IntentActionConfigCompareHelper.generateActionInfo(action, actionNameResourceBO.getSmsTempId2NameMap()),
                    IntentActionConfigCompareHelper.generateActionInfo(replaceRuleActionParam, actionNameResourceBO.getSmsTempId2NameMap()));
        }
        sourceUpdater.accept(entity);
        operationLogService.save(botId, OperationLogTypeEnum.SEARCH_REPLACE, OperationLogResourceTypeEnum.REPLACE, log, userId);
    }

    private <T> void replaceSms(AnswerLocateBO answerLocate, Long botId, Long searchSmsId, Long replaceSmsId, ActionNameResourceBO actionNameResourceBO, Long userId,
                                Supplier<T> sourceProvider, Function<T, Boolean> isEnableActionProvider, Function<T, List<RuleActionParam>> actionListProvider, Consumer<T> sourceUpdater) {
        T entity = sourceProvider.get();
        if (Objects.isNull(entity)) {
            return;
        }
        boolean isEnableAction = isEnableActionProvider.apply(entity);
        List<RuleActionParam> actionList = actionListProvider.apply(entity);
        if (BooleanUtils.isNotTrue(isEnableAction) || CollectionUtils.isEmpty(actionList)) {
            return;
        }
        RuleActionParam action = actionList.stream().filter(e -> ActionCategoryEnum.SEND_SMS.equals(e.getActionType())).findFirst().orElse(null);
        if (Objects.isNull(action)) {
            return;
        }
        boolean replaced = false;
        String log = "";
        if (Objects.isNull(searchSmsId) && CollectionUtils.isEmpty(action.getSourceIdList())) {
            action.setSourceIdList(Collections.singletonList(IdNamePair.of(replaceSmsId, "")));
            log = String.format("【%s】动作配置发短信增加短信模板【%s】", generateAnswerDisplayLocation(answerLocate), actionNameResourceBO.getSmsTempId2NameMap().getOrDefault(replaceSmsId, ""));
            replaced = true;
        }
        if (Objects.nonNull(searchSmsId) && CollectionUtils.isNotEmpty(action.getSourceIdList())) {
            List<Long> smsIdList = action.getSourceIdList().stream().map(IdNamePair::getId).collect(Collectors.toList());
            if (smsIdList.contains(searchSmsId)) {
                smsIdList.remove(searchSmsId);
                smsIdList.add(replaceSmsId);
                List<IdNamePair<Long, String>> smsList = smsIdList.stream().distinct().map(id -> IdNamePair.of(id, "")).collect(Collectors.toList());
                action.setSourceIdList(smsList);
                log = String.format("【%s】短信模板【%s】替换为【%s】", generateAnswerDisplayLocation(answerLocate), actionNameResourceBO.getSmsTempId2NameMap().getOrDefault(searchSmsId, ""),
                        actionNameResourceBO.getSmsTempId2NameMap().getOrDefault(replaceSmsId, ""));
                replaced = true;
            }
        }
        if (replaced) {
            sourceUpdater.accept(entity);
            operationLogService.save(botId, OperationLogTypeEnum.SEARCH_REPLACE, OperationLogResourceTypeEnum.REPLACE, log, userId);
        }
    }

    private void replaceAnswer(BotTextSearchReplaceRequestVO request, Long userId) {
        if (CollectionUtils.isEmpty(request.getBotTextSearchReplaceResultVOList())) {
            return;
        }
        List<AnswerLocateBO> locateList = request.getBotTextSearchReplaceResultVOList().stream()
                .map(BotTextSearchReplaceResultVO::getAnswerLocate)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (BotTextSearchTypeEnum.ANSWER.equals(request.getSearchType())) {
            answerManagerService.replaceAnswerContent(request.getBotId(), locateList, request.getSearchText(), request.getReplaceText(), userId);
        } else {
            answerManagerService.replaceAnswerVariable(request.getBotId(), locateList, request.getSearchText(), request.getReplaceText(), userId, request.getSearchType());
        }
    }

    @Override
    public void delete(MutiBotTextSearchReplaceRequestVO mutiBotTextSearchReplaceRequestVO) {
        List<BotTextSearchReplaceRequestVO> botTextSearchReplaceRequestVOList = mutiBotTextSearchReplaceRequestVO.getBotTextSearchReplaceRequestVOList();
        Assert.notNull(botTextSearchReplaceRequestVOList, "需要删除的就不能为空");
        botTextSearchReplaceRequestVOList.forEach(botTextSearchReplaceRequestVO -> {

            if (CollectionUtils.isEmpty(botTextSearchReplaceRequestVO.getBotTextSearchReplaceResultVOList())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "删除的记录不能为空");
            }
            //关键词
            List<BotTextSearchReplaceResultVO> keyWordsList = botTextSearchReplaceRequestVO.getBotTextSearchReplaceResultVOList().stream().filter(item -> item.getSearchType().equals(BotTextSearchTypeEnum.KEYWORD)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(keyWordsList)) {
                botTextSearchReplaceRequestVO.setSearchType(BotTextSearchTypeEnum.KEYWORD);
                replaceOrDeleteIntent(keyWordsList, botTextSearchReplaceRequestVO, true);
            }
            //问法语料
            List<BotTextSearchReplaceResultVO> corpusList = botTextSearchReplaceRequestVO.getBotTextSearchReplaceResultVOList().stream().filter(item -> item.getSearchType().equals(BotTextSearchTypeEnum.CORPUS)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(corpusList)) {
                botTextSearchReplaceRequestVO.setSearchType(BotTextSearchTypeEnum.CORPUS);
                replaceOrDeleteIntent(corpusList, botTextSearchReplaceRequestVO, true);
            }
            updateBotToDraft(botTextSearchReplaceRequestVO.getBotId());
        });

    }

    /**
     * @param botTextSearchReplaceResultVOList
     * @param botTextSearchReplaceRequestVO
     * @param isDelete                         true-删除 false-替换
     */
    private void replaceOrDeleteIntent(List<BotTextSearchReplaceResultVO> botTextSearchReplaceResultVOList,
                                       BotTextSearchReplaceRequestVO botTextSearchReplaceRequestVO,
                                       Boolean isDelete) {
        List<String> keywordReplaceDetailList = Lists.newArrayList();
        List<String> keywordDeleteDetailList = Lists.newArrayList();
        List<String> corpusReplaceDetailList = Lists.newArrayList();
        List<String> corpusDeleteDetailList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(botTextSearchReplaceResultVOList)) {
            Map<String, List<BotTextSearchReplaceResultVO>> replaceMap = botTextSearchReplaceResultVOList.stream().collect(Collectors.groupingBy(BotTextSearchReplaceResultVO::getIntentId));
            if (MapUtils.isNotEmpty(replaceMap)) {
                IntentCorpusQuery intentCorpusQuery = IntentCorpusQuery.builder().intentIdList(replaceMap.keySet()).botId(botTextSearchReplaceRequestVO.getBotId()).build();
                List<IntentCorpusPO> intentCorpusPOList = intentCorpusService.scroll(intentCorpusQuery);
                if (botTextSearchReplaceRequestVO.getSearchType().equals(BotTextSearchTypeEnum.KEYWORD)) {
                    //关键词
                    intentCorpusPOList.forEach(intentCorpusPO -> {
                        if (CollectionUtils.isNotEmpty(intentCorpusPO.getRegexList())) {
                            List<String> replacedOrDeletedRegexList = replaceMap.get(intentCorpusPO.getIntentId()).stream().map(BotTextSearchReplaceResultVO::getOriginText).collect(Collectors.toList());
                            List<String> updateRegexList = intentCorpusPO.getRegexList().stream().filter(item -> !replacedOrDeletedRegexList.contains(item)).collect(Collectors.toList());
                            if (!isDelete) {
                                replaceMap.get(intentCorpusPO.getIntentId()).forEach(item -> {
                                    if (intentCorpusPO.getRegexList().contains(item.getOriginText())) {
                                        String finalText = item.getOriginText().replace(botTextSearchReplaceRequestVO.getSearchText(), botTextSearchReplaceRequestVO.getReplaceText());
                                        updateRegexList.add(finalText);
                                        keywordReplaceDetailList.add(String.format("【%s】【%s】，修改为【%s】", intentCorpusPO.getName(),item.getOriginText(),finalText));
                                    }
                                });
                            }else {
                                keywordDeleteDetailList.addAll(replacedOrDeletedRegexList.stream().map(s -> String.format("【%s】【%s】", intentCorpusPO.getName(),s)).collect(Collectors.toList()));
                            }
                            intentCorpusPO.setRegexList(updateRegexList);
                            intentCorpusService.save(intentCorpusPO);
                        }
                    });
                } else if (botTextSearchReplaceRequestVO.getSearchType().equals(BotTextSearchTypeEnum.CORPUS)) {
                    //语料
                    intentCorpusPOList.forEach(intentCorpusPO -> {
                        if (CollectionUtils.isNotEmpty(intentCorpusPO.getCorpusList())) {
                            List<String> replacedOrDeletedCorpusList = replaceMap.get(intentCorpusPO.getIntentId()).stream().map(BotTextSearchReplaceResultVO::getOriginText).collect(Collectors.toList());
                            List<String> updateCorpusList = intentCorpusPO.getCorpusList().stream().filter(item -> !replaceMap.get(intentCorpusPO.getIntentId()).stream().map(BotTextSearchReplaceResultVO::getOriginText).collect(Collectors.toList()).contains(item)).collect(Collectors.toList());
                            if (!isDelete) {
                                replaceMap.get(intentCorpusPO.getIntentId()).forEach(item -> {
                                    if (intentCorpusPO.getCorpusList().contains(item.getOriginText())) {
                                        String finalText = item.getOriginText().replace(botTextSearchReplaceRequestVO.getSearchText(), botTextSearchReplaceRequestVO.getReplaceText());
                                        updateCorpusList.add(finalText);
                                        corpusReplaceDetailList.add(String.format("【%s】【%s】，修改为【%s】", intentCorpusPO.getName(),item.getOriginText(),finalText));
                                    }
                                });
                            }else {
                                corpusDeleteDetailList.addAll(replacedOrDeletedCorpusList.stream().map(s -> String.format("【%s】【%s】", intentCorpusPO.getName(),s)).collect(Collectors.toList()));
                            }
                            intentCorpusPO.setCorpusList(updateCorpusList);
                            intentCorpusService.save(intentCorpusPO);
                        }
                    });
                }
            }
        }

        buildLogAndSave(botTextSearchReplaceRequestVO, keywordReplaceDetailList, keywordDeleteDetailList, corpusReplaceDetailList, corpusDeleteDetailList);
    }

    private void updateOrDeleteIntent(List<BotTextSearchReplaceResultVO> replaceList,
                                       BotTextSearchReplaceRequestVO request) {

        if (CollectionUtils.isEmpty(replaceList)) {
            return;
        }
        List<String> keywordReplaceDetailList = Lists.newArrayList();
        List<String> keywordDeleteDetailList = Lists.newArrayList();
        List<String> corpusReplaceDetailList = Lists.newArrayList();
        List<String> corpusDeleteDetailList = Lists.newArrayList();

        Map<String, List<BotTextSearchReplaceResultVO>> replaceMap = MyCollectionUtils.listToMapList(replaceList, BotTextSearchReplaceResultVO::getIntentId);
        IntentCorpusQuery intentCorpusQuery = IntentCorpusQuery.builder()
                .intentIdList(replaceMap.keySet())
                .botId(request.getBotId())
                .build();
        List<IntentCorpusPO> intentCorpusPOList = intentCorpusService.scroll(intentCorpusQuery);
        intentCorpusPOList.forEach(intentCorpus -> {
            if (BotTextSearchTypeEnum.KEYWORD.equals(request.getSearchType())) {
                processIntentCorpus(intentCorpus, intentCorpus::getRegexList, intentCorpus::setRegexList,
                        replaceMap.get(intentCorpus.getIntentId()), keywordReplaceDetailList, keywordDeleteDetailList);
            } else if (BotTextSearchTypeEnum.CORPUS.equals(request.getSearchType())) {
                processIntentCorpus(intentCorpus, intentCorpus::getCorpusList, intentCorpus::setCorpusList,
                        replaceMap.get(intentCorpus.getIntentId()), corpusReplaceDetailList, corpusDeleteDetailList);
            } else {
                log.warn("不支持的搜索类型: {}", request.getSearchType());
            }
        });

        buildLogAndSave(request, keywordReplaceDetailList, keywordDeleteDetailList, corpusReplaceDetailList, corpusDeleteDetailList);
    }

    private void processIntentCorpus(IntentCorpusPO corpus,
                                     Supplier<List<String>> supplier,
                                     Consumer<List<String>> consumer,
                                     List<BotTextSearchReplaceResultVO> changeList,
                                     List<String> changeLogList,
                                     List<String> deleteLogList) {
        List<String> originTextList = supplier.get();
        if (CollectionUtils.isEmpty(originTextList) || CollectionUtils.isEmpty(changeList)) {
            return;
        }
        // 对于原始重复的数据, 只保留一个
        Map<String, BotTextSearchReplaceResultVO> changeMap = changeList.stream().collect(Collectors.toMap(BotTextSearchReplaceResultVO::getOriginText, item -> item));
        List<String> newTextList = originTextList.stream()
                .map(originText -> {
                    if (changeMap.containsKey(originText)) {
                        // 要更新的数据
                        BotTextSearchReplaceResultVO item = changeMap.get(originText);
                        if (StringUtils.isNotBlank(item.getReplaceResultText())) {
                            changeLogList.add(String.format("【%s】【%s】，修改为【%s】", corpus.getName(),item.getOriginText(),item.getReplaceResultText()));
                            return item.getReplaceResultText();
                        }
                        deleteLogList.add(String.format("【%s】【%s】", corpus.getName(),item.getOriginText()));
                        return null;
                    } else {
                        return originText;
                    }
                }).filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        consumer.accept(newTextList);
        intentCorpusService.save(corpus);
    }

    private void buildLogAndSave(BotTextSearchReplaceRequestVO botTextSearchReplaceRequestVO, List<String> keywordReplaceDetailList, List<String> keywordDeleteDetailList, List<String> corpusReplaceDetailList, List<String> corpusDeleteDetailList) {
        List<String> detailList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(keywordReplaceDetailList)) {
            detailList.add(String.format("关键词替换：%s", String.join("；", keywordReplaceDetailList)));
        }
        if (CollectionUtils.isNotEmpty(keywordDeleteDetailList)) {
            detailList.add(String.format("关键词删除：%s", String.join("；", keywordDeleteDetailList)));
        }
        if (CollectionUtils.isNotEmpty(corpusReplaceDetailList)) {
            detailList.add(String.format("问法语料替换：%s", String.join("；", corpusReplaceDetailList)));
        }
        if (CollectionUtils.isNotEmpty(corpusDeleteDetailList)) {
            detailList.add(String.format("问法语料删除：%s", String.join("；", corpusDeleteDetailList)));
        }
        operationLogService.save(botTextSearchReplaceRequestVO.getBotId(), OperationLogTypeEnum.SEARCH_REPLACE, OperationLogResourceTypeEnum.REPLACE, detailList, SecurityUtils.getUserId());
    }

    private List<BotTextSearchReplaceResultVO> wrapIntentVOList(List<IntentVO> intentVOS, BotTextSearchReplaceRequestVO botTextSearchReplaceRequestVO) {
        if (CollectionUtils.isEmpty(intentVOS)) {
            return Lists.emptyList();
        }
        //组装返回信息
        List<BotTextSearchReplaceResultVO> botTextSearchReplaceResultVOList = new ArrayList<>();
        intentVOS.forEach(intentVO -> {
            if (botTextSearchReplaceRequestVO.getSearchType().equals(BotTextSearchTypeEnum.KEYWORD)) {
                if (CollectionUtils.isNotEmpty(intentVO.getRegexList())) {
                    intentVO.getRegexList().forEach(regex -> {
                        if ((!StringUtils.isEmpty(regex) && regex.contains(botTextSearchReplaceRequestVO.getSearchText())) ||
                                StringUtils.isEmpty(org.apache.commons.lang3.StringUtils.trimToEmpty(botTextSearchReplaceRequestVO.getSearchText()))) {
                            BotTextSearchReplaceResultVO botTextSearchReplaceResultVO = new BotTextSearchReplaceResultVO();
                            botTextSearchReplaceResultVO.setOriginText(regex);
                            botTextSearchReplaceResultVO.setLocation("意图：" + intentVO.getName());
                            botTextSearchReplaceResultVO.setIntentId(intentVO.getId());
                            botTextSearchReplaceResultVO.setSearchType(BotTextSearchTypeEnum.KEYWORD);
                            botTextSearchReplaceResultVOList.add(botTextSearchReplaceResultVO);
                        }
                    });
                }
            } else if ((botTextSearchReplaceRequestVO.getSearchType().equals(BotTextSearchTypeEnum.CORPUS))) {
                if (CollectionUtils.isNotEmpty(intentVO.getCorpusList())) {
                    intentVO.getCorpusList().forEach(corpus -> {
                        if ((!StringUtils.isEmpty(corpus) && corpus.contains(botTextSearchReplaceRequestVO.getSearchText())) ||
                                StringUtils.isEmpty(org.apache.commons.lang3.StringUtils.trimToEmpty(botTextSearchReplaceRequestVO.getSearchText()))) {
                            BotTextSearchReplaceResultVO botTextSearchReplaceResultVO = new BotTextSearchReplaceResultVO();
                            botTextSearchReplaceResultVO.setOriginText(corpus);
                            botTextSearchReplaceResultVO.setLocation("意图：" + intentVO.getName());
                            botTextSearchReplaceResultVO.setIntentId(intentVO.getId());
                            botTextSearchReplaceResultVO.setSearchType(BotTextSearchTypeEnum.CORPUS);
                            botTextSearchReplaceResultVOList.add(botTextSearchReplaceResultVO);
                        }
                    });
                }
            }
        });
        return botTextSearchReplaceResultVOList;
    }
}
