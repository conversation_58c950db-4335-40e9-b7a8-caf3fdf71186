package com.yiwise.dialogflow.service.impl.operationlog;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import com.yiwise.dialogflow.entity.enums.llm.DocumentSplitTypeEnum;
import com.yiwise.dialogflow.entity.po.llm.DocumentSplitConfigPO;
import com.yiwise.dialogflow.entity.po.llm.RagDocumentPO;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.operationlog.RagDocumentOperationLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class RagDocumentOperationLogServiceImpl implements RagDocumentOperationLogService {

    @Resource
    private OperationLogService operationLogService;

    @Override
    public void createDoc(RagDocumentPO po) {
        operationLogService.save(po.getBotId(), OperationLogTypeEnum.KNOWLEDGE, OperationLogResourceTypeEnum.RAG_DOC,
                String.format("新增文档【%s】", po.getDocName()), po.getCreateUserId());
    }

    @Override
    public void reSplitDoc(RagDocumentPO po) {
        operationLogService.save(po.getBotId(), OperationLogTypeEnum.KNOWLEDGE, OperationLogResourceTypeEnum.RAG_DOC,
                String.format("文档【%s】重新分段【%s】", po.getDocName(), generateSplitInfo(po.getSplitConfig())),
                po.getUpdateUserId());
    }

    private String generateSplitInfo(DocumentSplitConfigPO splitConfig) {
        if (DocumentSplitTypeEnum.isAuto(splitConfig.getSplitType())) {
            return splitConfig.getSplitType().getDesc();
        }
        return splitConfig.getSplitType().getDesc() + "," + splitConfig.getSplitMethod().getDesc() + ",上限" + splitConfig.getSegmentMaxWords() + "字";
    }

    @Override
    public void updateEnabledStatus(RagDocumentPO po) {
        operationLogService.save(po.getBotId(), OperationLogTypeEnum.KNOWLEDGE, OperationLogResourceTypeEnum.RAG_DOC,
                String.format("【%s】文档【%s】", EnabledStatusEnum.ENABLE.equals(po.getEnabledStatus()) ? "启用" : "停用", po.getDocName()), po.getUpdateUserId());
    }

    @Override
    public void updateDocName(Long botId, String oldName, String newName, Long userId) {
        operationLogService.save(botId, OperationLogTypeEnum.KNOWLEDGE, OperationLogResourceTypeEnum.RAG_DOC,
                String.format("文档【%s】改名为【%s】", oldName, newName), userId);
    }

    @Override
    public void deleteDoc(Long botId, List<RagDocumentPO> docList, Long userId) {
        List<String> docNameList = MyCollectionUtils.listToConvertList(docList, RagDocumentPO::getDocName);
        operationLogService.save(botId, OperationLogTypeEnum.KNOWLEDGE, OperationLogResourceTypeEnum.RAG_DOC,
                String.format("删除文档【%s】", String.join(",", docNameList)), userId);
    }

    @Override
    public void updateSegment(Long botId, String docName, String oldContent, String newContent, Long userId) {
        operationLogService.save(botId, OperationLogTypeEnum.KNOWLEDGE, OperationLogResourceTypeEnum.RAG_DOC,
                String.format("文档【%s】片段【%s】修改为【%s】", docName, oldContent, newContent), userId);
    }

    @Override
    public void deleteSegment(Long botId, String docName, String content, Long userId) {
        operationLogService.save(botId, OperationLogTypeEnum.KNOWLEDGE, OperationLogResourceTypeEnum.RAG_DOC,
                String.format("文档【%s】片段【%s】删除", docName, content), userId);
    }
}