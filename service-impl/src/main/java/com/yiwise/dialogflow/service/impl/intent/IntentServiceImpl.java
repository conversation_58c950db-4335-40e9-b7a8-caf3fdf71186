package com.yiwise.dialogflow.service.impl.intent;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yiwise.base.common.thread.decorator.MDCDecoratorCallable;
import com.yiwise.base.common.utils.bean.DeepCopyUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.entity.CheckKeywordExistsRequestVO;
import com.yiwise.dialogflow.entity.bo.*;
import com.yiwise.dialogflow.entity.bo.algorithm.ModelTrainKey;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.dto.OperationLogDTO;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.intent.IntentConfigPO;
import com.yiwise.dialogflow.entity.po.intent.IntentCorpusPO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.query.IntentCorpusQuery;
import com.yiwise.dialogflow.entity.query.IntentQuery;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.IntentSyncVO;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.impl.GroupServiceImpl;
import com.yiwise.dialogflow.service.intent.IntentConfigService;
import com.yiwise.dialogflow.service.intent.IntentCorpusService;
import com.yiwise.dialogflow.service.intent.IntentService;
import com.yiwise.dialogflow.service.train.TrainService;
import com.yiwise.dialogflow.service.train.TrainSyncService;
import com.yiwise.dialogflow.thread.BotSyncThreadExecutorHelper;
import com.yiwise.dialogflow.utils.ParseUtil;
import javaslang.Tuple;
import javaslang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/2/14
 */
@Slf4j
@Service
public class IntentServiceImpl implements IntentService, RobotResourceService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private IntentCorpusService intentCorpusService;

    @Resource
    private BotService botService;

    @Resource
    private TrainService trainService;

    @Resource
    private SourceRefService sourceRefService;

    @Resource
    private IntentConfigService intentConfigService;

    @Resource
    private DependResourceService dependResourceService;

    @Resource
    private GroupService groupService;

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private TrainSyncService trainSyncService;

    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    @Resource
    private KnowledgeService knowledgeService;

    @Resource
    private BotSyncOperationLogService botSyncOperationLogService;

    @Resource
    private VariableService variableService;

    private DependentResourceBO prepareDependentResource(Long botId) {
        DependentResourceBO.Condition condition = new DependentResourceBO.Condition(botId);
        return dependResourceService.generateByCondition(condition.group());
    }

    void validAndThrow(IntentPO intent, DependentResourceBO dependentResource) {
        // 单个意图校验分组
        if (IntentTypeEnum.SINGLE.equals(intent.getIntentType())) {
            String groupId = intent.getGroupId();
            if (StringUtils.isBlank(groupId)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "分组不能为空");
            }
            if (!GroupTypeEnum.SINGLE_INTENT.equals(dependentResource.getGroupIdTypeMap().get(groupId))) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "分组不存在");
            }
        }
    }

    /**
     * 创建/更新意图
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public IntentPO save(IntentVO intentVO) {

        if (nameExists(intentVO.getId(), Lists.newArrayList(intentVO.getName()), Lists.newArrayList(intentVO.getBotId()))) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "意图名称已存在");
        }

        if (CollectionUtils.isNotEmpty(intentVO.getRegexList())) {
            ParseUtil.checkRegExValid(intentVO.getRegexList());
        }

        validAndThrow(intentVO, prepareDependentResource(intentVO.getBotId()));

        IntentVO oldIntentVO = Optional.ofNullable(intentVO.getId()).map(this::detail).orElse(null);

        // 校验描述重复
        if (CollectionUtils.isNotEmpty(intentVO.getDescList())) {
            List<String> newDescList;
            if (Objects.nonNull(oldIntentVO) && CollectionUtils.isNotEmpty(oldIntentVO.getDescList())) {
                newDescList = ListUtils.subtract(intentVO.getDescList(), oldIntentVO.getDescList());
            } else {
                newDescList = intentVO.getDescList();
            }
            if (CollectionUtils.isNotEmpty(newDescList)) {
                Map<String, Set<String>> descIntentNameSetMap = findExistsDesc(intentVO.getBotId(), newDescList);
                if (MapUtils.isNotEmpty(descIntentNameSetMap)) {
                    StringJoiner sj = new StringJoiner(";");
                    descIntentNameSetMap.forEach((desc, intentNameSet) -> sj.add(String.format("描述[%s]已存在于意图[%s]", desc, String.join(",", intentNameSet))));
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, sj.toString());
                }
            }
        }

        IntentPO createPO = new IntentPO();
        BeanUtils.copyProperties(intentVO, createPO);
        if (StringUtils.isEmpty(createPO.getId())) {
            createPO.setCreateTime(LocalDateTime.now());
            createPO.setCreateUserId(createPO.getUpdateUserId());
        }
        if (Objects.isNull(createPO.getCorpusType())) {
            createPO.setCorpusType(CorpusTypeEnum.CUSTOMIZED);
        }

        // 问法是否改变
        boolean hasChanged = true;
        IntentCorpusPO intentCorpusPO = intentCorpusService.findByIntentId(createPO.getId());
        if (Objects.isNull(intentCorpusPO)) {
            intentCorpusPO = new IntentCorpusPO();
        } else {
            // 内置意图的新增不在这边，这边只有内置意图的更新
            if (CorpusTypeEnum.BUILD_IN.equals(createPO.getCorpusType())) {
                hasChanged = false;
            } else {
                hasChanged = MyCollectionUtils.hasChanged(intentCorpusPO.getCorpusList(), intentVO.getCorpusList());
            }
        }

        if (hasChanged) {
            createPO.setTrainStatus(TrainStatusEnum.CREATED);
        }
        createPO.setUpdateTime(LocalDateTime.now());
        createPO.setUpdateUserId(intentVO.getUpdateUserId());
        mongoTemplate.save(createPO);

        // 保存问法
        intentCorpusPO.setBotId(intentVO.getBotId());
        intentCorpusPO.setIntentId(createPO.getId());
        intentCorpusPO.setName(intentVO.getName());
        intentCorpusPO.setCorpusList(intentVO.getCorpusList());
        // 内置意图不应该有自定义问法
        if (CorpusTypeEnum.BUILD_IN.equals(createPO.getCorpusType())) {
            intentCorpusPO.setCorpusList(null);
        }
        intentCorpusPO.setRegexList(intentVO.getRegexList());
        intentCorpusPO.setDescList(intentVO.getDescList());
        intentCorpusService.save(intentCorpusPO);

        // 如果是组合意图，添加关联
        if (IntentTypeEnum.COMPOSITE.equals(intentVO.getIntentType())) {
            Set<String> intentIdSet = Sets.newHashSet();
            for (CompositeIntentCondition compositeIntentCondition : intentVO.getCompositeConditionList()) {
                intentIdSet.addAll(compositeIntentCondition.getIntentIdList());
            }
            // 已存在的关联
            sourceRefService.deleteSourceByRefId(intentVO.getBotId(), createPO.getId());
            if (CollectionUtils.isNotEmpty(intentIdSet)) {
                intentVO.setId(createPO.getId());
                sourceRefService.saveSourceRef(buildParam(intentVO, intentIdSet));
            }
        }

        // 自动创建变量，生成引用路径
        autoCreateVarAndSourceRef(intentVO.getBotId(), createPO.getId(), intentVO.getName(), intentVO.getDescList(), intentVO.getUpdateUserId());

        // 更新Bot状态
        botService.updateAuditStatus(intentVO.getBotId(), AuditStatusEnum.DRAFT);

        buildLogAndSave(oldIntentVO, intentVO, intentVO.getUpdateUserId());
        return createPO;
    }

    private void autoCreateVarAndSourceRef(Long botId, String intentId, String intentName, List<String> descList, Long userId) {
        if (CollectionUtils.isNotEmpty(descList)) {
            Tuple2<Boolean, Map<String, String>> tuple2 =
                    variableService.autoCreateIfNotExists(botId, descList, VariableTypeEnum.CUSTOM, userId);
            Map<String, String> varNameIdMap = tuple2._2();
            sourceRefService.deleteSourceByRefId(botId, intentId);
            varNameIdMap.forEach((varName, varId) -> {
                SourceRefPO sourceRefBO = new SourceRefPO();
                sourceRefBO.setBotId(botId);
                sourceRefBO.setSourceType(SourceTypeEnum.VARIABLE);
                sourceRefBO.setSourceId(varId);
                sourceRefBO.setRefId(intentId);
                sourceRefBO.setRefType(IntentRefTypeEnum.SINGLE_INTENT);
                sourceRefBO.setRefLabel(intentName);
                sourceRefBO.setCreateTime(LocalDateTime.now());
                sourceRefBO.setUpdateTime(LocalDateTime.now());
                sourceRefBO.setCreateUserId(userId);
                sourceRefBO.setUpdateUserId(userId);
                sourceRefService.addSourceRef(sourceRefBO);
            });
        }
    }

    private void autoSyncVarAndCreateSourceRef(List<IntentCorpusPO> targetCorpusList, Long targetBotId, Long userId, Long srcBotId) {
        Set<String> dependentVarNameSet = new HashSet<>();
        Map<String, Set<String>> intentIdVarNameSetMap = new HashMap<>();
        for (IntentCorpusPO corpus : targetCorpusList) {
            sourceRefService.deleteSourceByRefId(targetBotId, corpus.getIntentId());
            if (CollectionUtils.isNotEmpty(corpus.getDescList())) {
                Set<String> varNameSet = corpus.getDescList().stream()
                        .flatMap(text -> new AnswerPlaceholderSplitter(text, false).getVariableSet().stream()).collect(Collectors.toSet());
                dependentVarNameSet.addAll(varNameSet);
                intentIdVarNameSetMap.put(corpus.getIntentId(), varNameSet);
            }
        }
        if (CollectionUtils.isNotEmpty(dependentVarNameSet)) {
            List<VariablePO> srcVariableList = variableService.getListByBotId(srcBotId)
                    .stream().filter(v -> dependentVarNameSet.contains(v.getName())).collect(Collectors.toList());
            Map<String, String> srcVarNameIdMap = MyCollectionUtils.listToMap(srcVariableList, VariablePO::getName, VariablePO::getId);
            Map<String, String> varIdMap = variableService.syncDependVariable(srcVariableList, targetBotId, userId);
            for (IntentCorpusPO corpus : targetCorpusList) {
                Set<String> varNameSet = intentIdVarNameSetMap.get(corpus.getIntentId());
                if (CollectionUtils.isEmpty(varNameSet)) {
                    continue;
                }
                List<String> varIdList = varNameSet.stream().map(varName -> varIdMap.get(srcVarNameIdMap.get(varName)))
                        .filter(StringUtils::isNotBlank).collect(Collectors.toList());
                for (String varId : varIdList) {
                    SourceRefPO sourceRefBO = new SourceRefPO();
                    sourceRefBO.setBotId(targetBotId);
                    sourceRefBO.setSourceType(SourceTypeEnum.VARIABLE);
                    sourceRefBO.setSourceId(varId);
                    sourceRefBO.setRefId(corpus.getIntentId());
                    sourceRefBO.setRefType(IntentRefTypeEnum.SINGLE_INTENT);
                    sourceRefBO.setRefLabel(corpus.getName());
                    sourceRefBO.setCreateTime(LocalDateTime.now());
                    sourceRefBO.setUpdateTime(LocalDateTime.now());
                    sourceRefBO.setCreateUserId(userId);
                    sourceRefBO.setUpdateUserId(userId);
                    sourceRefService.addSourceRef(sourceRefBO);
                }
            }
        }
    }

    @Override
    public List<IntentPO> batchCreateSingleIntent(Long botId, List<String> intentNameList, String groupId, Long userId) {
        if (CollectionUtils.isEmpty(intentNameList)) {
            return Collections.emptyList();
        }

        List<IntentPO> intentList = new ArrayList<>();
        for (String intentName : intentNameList) {
            IntentVO intent = new IntentVO();
            intent.setName(intentName);
            intent.setBotId(botId);
            intent.setIntentType(IntentTypeEnum.SINGLE);
            intent.setCorpusType(CorpusTypeEnum.CUSTOMIZED);
            intent.setIntentProperties(IntentPropertiesEnum.NEUTRAL);
            intent.setGroupId(groupId);
            intentList.add(intent);
        }
        mongoTemplate.insert(intentList, MongoCollectionNameCenter.INTENT);

        List<IntentCorpusPO> intentCorpusList = new ArrayList<>();
        for (IntentPO intent : intentList) {
            IntentCorpusPO intentCorpus = new IntentCorpusPO();
            intentCorpus.setName(intent.getName());
            intentCorpus.setBotId(botId);
            intentCorpus.setIntentId(intent.getId());
            intentCorpus.setCorpusList(Collections.emptyList());
            intentCorpus.setBuildInCorpusList(Collections.emptyList());
            intentCorpus.setRegexList(Collections.emptyList());
            intentCorpus.setBuildInRegexList(Collections.emptyList());
            intentCorpusList.add(intentCorpus);
        }
        intentCorpusService.saveAll(intentCorpusList);

        List<String> detailList = new ArrayList<>();
        for (IntentPO intent : intentList) {
            detailList.addAll(buildLog(null, (IntentVO) intent));
        }

        saveLog(botId, OperationLogResourceTypeEnum.SINGLE_INTENT, userId, detailList);
        return intentList;
    }

    private void buildLogAndSave(IntentVO oldIntentVO, IntentVO newIntentVO, Long userId) {
        List<String> detailList = buildLog(oldIntentVO, newIntentVO);
        saveLog(newIntentVO.getBotId(), IntentTypeEnum.SINGLE.equals(newIntentVO.getIntentType()) ? OperationLogResourceTypeEnum.SINGLE_INTENT : OperationLogResourceTypeEnum.COMPOSITE_INTENT, userId, detailList);
    }

    private void saveLog(Long botId, OperationLogResourceTypeEnum resourceType, Long userId, List<String> detailList) {
        operationLogService.save(botId, OperationLogTypeEnum.INTENT, resourceType, detailList, userId);
    }

    private List<String> buildLog(IntentVO oldIntentVO, IntentVO newIntentVO) {
        boolean isSingleIntent = IntentTypeEnum.SINGLE.equals(newIntentVO.getIntentType());
        String intentTypeName = isSingleIntent ? "单个意图" : "组合意图";
        List<String> detailList = Lists.newArrayList();
        if (Objects.isNull(oldIntentVO)) {
            detailList.add(String.format("新增%s【%s】", intentTypeName, newIntentVO.getName()));
        } else {
            if (!StringUtils.equals(oldIntentVO.getName(), newIntentVO.getName())) {
                detailList.add(String.format("编辑%s：原【%s】意图名称修改为【%s】", intentTypeName, oldIntentVO.getName(), newIntentVO.getName()));
            }
            if (!Objects.equals(oldIntentVO.getIntentProperties(), newIntentVO.getIntentProperties())) {
                detailList.add(String.format("编辑%s：原【%s】意图属性【%s】，修改为【%s】", intentTypeName, oldIntentVO.getName(), oldIntentVO.getIntentProperties().getDesc(), newIntentVO.getIntentProperties().getDesc()));
            }
            if (isSingleIntent) {
                List<String> oldRegexList = Optional.ofNullable(oldIntentVO.getRegexList()).orElse(Collections.emptyList());
                List<String> newRegexList = Optional.ofNullable(newIntentVO.getRegexList()).orElse(Collections.emptyList());
                // 新增的关键词
                Collection<String> addRegexList = CollectionUtils.subtract(newRegexList, oldRegexList);
                if (CollectionUtils.isNotEmpty(addRegexList)) {
                    detailList.add(String.format("编辑单个意图：原【%s】增加关键词：%s", oldIntentVO.getName(), String.join("、", addRegexList)));
                }
                // 删除的关键词
                Collection<String> deleteRegexList = CollectionUtils.subtract(oldRegexList, newRegexList);
                if (CollectionUtils.isNotEmpty(deleteRegexList)) {
                    detailList.add(String.format("编辑单个意图：原【%s】删除关键词：%s", oldIntentVO.getName(), String.join("、", deleteRegexList)));
                }
                List<String> oldCorpusList = Optional.ofNullable(oldIntentVO.getCorpusList()).orElse(Collections.emptyList());
                List<String> newCorpusList = Optional.ofNullable(newIntentVO.getCorpusList()).orElse(Collections.emptyList());
                // 新增的语料问法
                Collection<String> addCorpusList = CollectionUtils.subtract(newCorpusList, oldCorpusList);
                if (CollectionUtils.isNotEmpty(addCorpusList)) {
                    detailList.add(String.format("编辑单个意图：原【%s】增加语料问法：%s", oldIntentVO.getName(), String.join("、", addCorpusList)));
                }
                // 删除的语料问法
                Collection<String> deleteCorpusList = CollectionUtils.subtract(oldCorpusList, newCorpusList);
                if (CollectionUtils.isNotEmpty(deleteCorpusList)) {
                    detailList.add(String.format("编辑单个意图：原【%s】删除语料问法：%s", oldIntentVO.getName(), String.join("、", deleteCorpusList)));
                }
                List<String> oldDescList = Optional.ofNullable(oldIntentVO.getDescList()).orElse(Collections.emptyList());
                List<String> newDescList = Optional.ofNullable(newIntentVO.getDescList()).orElse(Collections.emptyList());
                // 新增的描述
                Collection<String> addDescList = CollectionUtils.subtract(newDescList, oldDescList);
                if (CollectionUtils.isNotEmpty(addDescList)) {
                    detailList.add(String.format("编辑单个意图：原【%s】增加描述：%s", oldIntentVO.getName(), String.join("、", addDescList)));
                }
                // 删除的描述
                Collection<String> deleteDescList = CollectionUtils.subtract(oldDescList, newDescList);
                if (CollectionUtils.isNotEmpty(deleteDescList)) {
                    detailList.add(String.format("编辑单个意图：原【%s】删除描述：%s", oldIntentVO.getName(), String.join("、", deleteDescList)));
                }
            } else {
                List<CompositeIntentCondition> oldCompositeConditionList = oldIntentVO.getCompositeConditionList();
                List<CompositeIntentCondition> newCompositeConditionList = newIntentVO.getCompositeConditionList();
                Map<String, String> intentIdNameMap = MyCollectionUtils.listToMap(getAllByBotId(newIntentVO.getBotId()), IntentPO::getId, IntentPO::getName);
                if (hasCompositeConditionChanged(oldCompositeConditionList, newCompositeConditionList)) {
                    detailList.add(String.format("编辑组合意图：原【%s】条件【%s】，修改为【%s】", oldIntentVO.getName(),
                            oldCompositeConditionList.stream().map(s -> s.getIntentIdList().stream().map(intentIdNameMap::get).map(name -> "[" + name + "]").collect(Collectors.joining("且"))).collect(Collectors.joining(";")),
                            newCompositeConditionList.stream().map(s -> s.getIntentIdList().stream().map(intentIdNameMap::get).map(name -> "[" + name + "]").collect(Collectors.joining("且"))).collect(Collectors.joining(";"))
                    ));
                }
            }
        }
        return detailList;
    }

    private boolean hasCompositeConditionChanged(List<CompositeIntentCondition> oldCompositeConditionList, List<CompositeIntentCondition> newCompositeConditionList) {
        if (oldCompositeConditionList.size() != newCompositeConditionList.size()) {
            return true;
        }
        for (int i = 0; i < oldCompositeConditionList.size(); i++) {
            List<String> oldIntentIdList = oldCompositeConditionList.get(i).getIntentIdList();
            List<String> newIntentIdList = newCompositeConditionList.get(i).getIntentIdList();
            if (oldIntentIdList.size() != newIntentIdList.size()) {
                return true;
            }
            for (int i1 = 0; i1 < oldIntentIdList.size(); i1++) {
                if (!StringUtils.equals(oldIntentIdList.get(i1), newIntentIdList.get(i1))) {
                    return true;
                }
            }
        }
        return false;
    }

    private SourceRefBO buildParam(IntentVO intentVO, Set<String> intentIdSet) {
        SourceRefBO sourceRefBO = new SourceRefBO();
        sourceRefBO.setBotId(intentVO.getBotId());
        sourceRefBO.setRefId(intentVO.getId());
        sourceRefBO.setRefLabel(intentVO.getName());
        sourceRefBO.setRefType(IntentRefTypeEnum.COMPOSITE_INTENT);
        sourceRefBO.setSourceIdSet(intentIdSet);
        sourceRefBO.setSourceType(SourceTypeEnum.INTENT);
        sourceRefBO.setUpdateUserId(intentVO.getUpdateUserId());
        return sourceRefBO;
    }

    /**
     * 批量插入内置意图
     * 对非同名意图进行创建，同名意图进行更新或跳过
     */
    @Override
    public void batchInsert(List<Long> botIdList, List<DomainIntentPO> domainIntentPOList, SyncModeEnum syncMode, Long currentUserId, String targetGroupId) {
        if (CollectionUtils.isEmpty(botIdList) || CollectionUtils.isEmpty(domainIntentPOList)) {
            return;
        }
        String domainName = domainIntentPOList.get(0).getDomainName();

        for (Long botId : botIdList) {
            // 多个bot默认同步到'全部意图'分组
            if (StringUtils.isBlank(targetGroupId)) {
                targetGroupId = groupService.initAllIntentGroupIfNotExists(botId, currentUserId).getId();
            }
            String groupId = targetGroupId;

            List<String> nameList = MyCollectionUtils.listToConvertList(domainIntentPOList, DomainIntentPO::getName);
            Query query = Query.query(Criteria.where("botId").is(botId).and("name").in(nameList));
            List<IntentPO> existsIntentPOList = mongoTemplate.find(query, IntentPO.class);
            List<String> existsIntentNameList = MyCollectionUtils.listToConvertList(existsIntentPOList, IntentPO::getName);
            // 更新列表
            List<DomainIntentPO> updateList = domainIntentPOList.stream().filter(domainIntentPO -> existsIntentNameList.contains(domainIntentPO.getName())).collect(Collectors.toList());
            // 插入列表
            List<DomainIntentPO> insertList = domainIntentPOList.stream().filter(domainIntentPO -> !existsIntentNameList.contains(domainIntentPO.getName())).collect(Collectors.toList());

            // 如果是覆盖，则先删后插
            if (SyncModeEnum.COVER.equals(syncMode) && CollectionUtils.isNotEmpty(updateList)) {
                Map<String, IntentPropertiesEnum> intentPropertiesEnumMap = MyCollectionUtils.listToMap(updateList, DomainIntentPO::getName, DomainIntentPO::getIntentProperties);
                // 这边保留了原先intent的intentId，不用再处理关联关系了
                existsIntentPOList.forEach(intentPO -> {
                    intentPO.setIntentProperties(intentPropertiesEnumMap.get(intentPO.getName()));
                    intentPO.setIntentType(IntentTypeEnum.SINGLE);
                    intentPO.setCorpusType(CorpusTypeEnum.BUILD_IN);
                    intentPO.setTrainStatus(TrainStatusEnum.CREATED);
                    intentPO.setUpdateTime(LocalDateTime.now());
                    intentPO.setUpdateUserId(currentUserId);
                    intentPO.setGroupId(groupId);
                });
                mongoTemplate.remove(query, IntentPO.class);
                mongoTemplate.insertAll(existsIntentPOList);

                List<IntentCorpusPO> existsIntentCorpusPOList = updateList.stream().map(domainIntentPO -> {
                    IntentCorpusPO intentCorpusPO = new IntentCorpusPO();
                    intentCorpusPO.setBotId(botId);
                    intentCorpusPO.setName(domainIntentPO.getName());
                    intentCorpusPO.setBuildInCorpusList(domainIntentPO.getBuiltInCorpusList());
                    intentCorpusPO.setBuildInRegexList(domainIntentPO.getBuiltInRegexList());
                    return intentCorpusPO;
                }).collect(Collectors.toList());

                Map<String, String> intentMap = MyCollectionUtils.listToMap(existsIntentPOList, IntentPO::getName, IntentPO::getId);
                existsIntentCorpusPOList = existsIntentCorpusPOList.stream()
                        .peek(intentCorpusPO -> intentCorpusPO.setIntentId(intentMap.get(intentCorpusPO.getName())))
                        .filter(intentCorpusPO -> StringUtils.isNotEmpty(intentCorpusPO.getIntentId()))
                        .collect(Collectors.toList());
                List<String> existsIntentIdList = MyCollectionUtils.listToConvertList(existsIntentPOList, IntentPO::getId);
                intentCorpusService.deleteByIntentIdList(existsIntentIdList);
                intentCorpusService.saveAll(existsIntentCorpusPOList);
            }

            if (CollectionUtils.isEmpty(insertList)) {
                continue;
            }

            List<IntentPO> createPOList = insertList.stream().map(domainIntentPO -> {
                IntentPO intentPO = new IntentPO();
                intentPO.setId(null);
                intentPO.setBotId(botId);
                intentPO.setName(domainIntentPO.getName());
                intentPO.setIntentProperties(domainIntentPO.getIntentProperties());
                intentPO.setIntentType(IntentTypeEnum.SINGLE);
                intentPO.setCorpusType(CorpusTypeEnum.BUILD_IN);
                intentPO.setTrainStatus(TrainStatusEnum.CREATED);
                intentPO.setCreateUserId(currentUserId);
                intentPO.setUpdateUserId(currentUserId);
                intentPO.setCreateTime(LocalDateTime.now());
                intentPO.setUpdateTime(LocalDateTime.now());
                intentPO.setGroupId(groupId);
                return intentPO;
            }).collect(Collectors.toList());
            List<IntentCorpusPO> intentCorpusPOList = insertList.stream().map(domainIntentPO -> {
                IntentCorpusPO intentCorpusPO = new IntentCorpusPO();
                intentCorpusPO.setBotId(botId);
                intentCorpusPO.setName(domainIntentPO.getName());
                intentCorpusPO.setBuildInCorpusList(domainIntentPO.getBuiltInCorpusList());
                intentCorpusPO.setBuildInRegexList(domainIntentPO.getBuiltInRegexList());
                return intentCorpusPO;
            }).collect(Collectors.toList());
            mongoTemplate.insertAll(createPOList);

            Map<String, String> intentMap = MyCollectionUtils.listToMap(createPOList, IntentPO::getName, IntentPO::getId);
            intentCorpusPOList.forEach(intentCorpusPO -> intentCorpusPO.setIntentId(intentMap.get(intentCorpusPO.getName())));
            intentCorpusService.saveAll(intentCorpusPOList);

            //自动关联特殊语境
            autoFillIntentIdForSpecialAnswer(botId, intentMap, currentUserId);

            // 更新Bot状态
            botService.updateAuditStatus(botId, AuditStatusEnum.DRAFT);

            //更新bot领域名称
            if (SyncModeEnum.COVER.equals(syncMode) && CollectionUtils.isNotEmpty(updateList) || CollectionUtils.isNotEmpty(insertList)) {
                botService.updateDomainName(botId, domainName);
            }

        }

        Map<String, String> domainMap = MyCollectionUtils.listToMap(trainSyncService.domainList(), NameDescVO::getName, NameDescVO::getDesc);
        String detail = "调用内置意图" + domainIntentPOList.stream().map(d -> "【" + domainMap.getOrDefault(d.getDomainName(), "") + "：" + d.getName() + "】").collect(Collectors.joining("、"));
        for (Long botId : botIdList) {
            operationLogService.save(botId, OperationLogTypeEnum.INTENT, OperationLogResourceTypeEnum.SINGLE_INTENT, detail, currentUserId);
        }
    }

    /**
     * 调用内置意图 听不清楚AI说话、语音助手、AI重复上句语音 ,自动关联特殊语境
     */
    private void autoFillIntentIdForSpecialAnswer(Long botId, Map<String, String> intentMap, Long userId) {
        Map<String, String> specialAnswerNameTriggerIntentNameMap = new HashMap<>(3);
        specialAnswerNameTriggerIntentNameMap.put(SpecialAnswerConfigPO.INAUDIBLE, SpecialAnswerConfigPO.INAUDIBLE);
        specialAnswerNameTriggerIntentNameMap.put(SpecialAnswerConfigPO.ASSISTANT, SpecialAnswerConfigPO.ASSISTANT);
        specialAnswerNameTriggerIntentNameMap.put(SpecialAnswerConfigPO.AI_REPEAT, SpecialAnswerConfigPO.AI_REPEAT);

        Map<String, List<String>> specialAnswerNameExcludeIntentNameListMap = Collections.singletonMap(SpecialAnswerConfigPO.HANGUP_DELAY, Arrays.asList("明确肯定", "明确拒绝"));

        Set<String> intentNameSet = new HashSet<>(specialAnswerNameTriggerIntentNameMap.values());
        specialAnswerNameExcludeIntentNameListMap.values().forEach(intentNameSet::addAll);

        if (intentNameSet.stream().anyMatch(intentMap::containsKey)) {
            for (SpecialAnswerConfigPO specialAnswerConfigPO : specialAnswerConfigService.getByBotId(botId)) {
                String specialAnswerName = specialAnswerConfigPO.getName();

                boolean hasChanged = false;

                String triggerIntentName = specialAnswerNameTriggerIntentNameMap.get(specialAnswerName);
                if (triggerIntentName != null && intentMap.get(triggerIntentName) != null) {
                    List<String> triggerIntentIdList = specialAnswerConfigPO.getTriggerIntentIdList();
                    triggerIntentIdList.add(intentMap.get(triggerIntentName));
                    specialAnswerConfigPO.setTriggerIntentIdList(triggerIntentIdList);
                    hasChanged = true;
                }

                List<String> excludeIntentNameList = specialAnswerNameExcludeIntentNameListMap.get(specialAnswerName);
                if (CollectionUtils.isNotEmpty(excludeIntentNameList) && excludeIntentNameList.stream().anyMatch(intentMap::containsKey)) {
                    List<String> excludeIntentIdList = specialAnswerConfigPO.getExcludeIntentIdList();
                    if (CollectionUtils.isEmpty(excludeIntentIdList)) {
                        excludeIntentIdList = new ArrayList<>();
                    }
                    excludeIntentIdList.addAll(excludeIntentNameList.stream().map(intentMap::get).filter(Objects::nonNull).collect(Collectors.toList()));
                    specialAnswerConfigPO.setExcludeIntentIdList(excludeIntentIdList);
                    hasChanged = true;
                }

                if (hasChanged) {
                    specialAnswerConfigService.update(specialAnswerConfigPO, userId);
                }
            }
        }
    }


    /**
     * 意图同步
     */
    @Override
    public BotSyncResultVO sync(IntentSyncVO syncVO) {
        Long srcBotId = syncVO.getSrcBotId();
        List<Long> targetBotIdList = syncVO.getTargetBotIdList();
        Assert.notNull(targetBotIdList, "目标Bot不能为空");
        Assert.isTrue(!targetBotIdList.contains(srcBotId), "目标bot不能包含自己");
        Long currentUserId = syncVO.getCurrentUserId();
        SystemEnum systemType = syncVO.getSystemType();
        SyncModeEnum sameIntent = syncVO.getSameIntent();
        Boolean syncIntentConfig = syncVO.getSyncIntentConfig();
        Assert.notNull(syncVO.getIntentQuery(), "查询条件不能为空");

        List<IntentVO> intentVOList = listWithoutPages(syncVO.getIntentQuery());
        if (CollectionUtils.isEmpty(intentVOList)) {
            return BotSyncResultVO.defaultInstance();
        }
        List<String> intentIdList = MyCollectionUtils.listToConvertList(intentVOList, IntentVO::getId);

        // 组合意图需要同步关联意图
        Set<String> additionalIntentIdSet = Sets.newHashSet(intentIdList);
        intentVOList.forEach(intentVO -> {
            if (IntentTypeEnum.COMPOSITE.equals(intentVO.getIntentType())) {
                List<CompositeIntentCondition> compositeConditionList = intentVO.getCompositeConditionList();
                if (CollectionUtils.isNotEmpty(compositeConditionList)) {
                    compositeConditionList.forEach(compositeIntentCondition -> additionalIntentIdSet.addAll(compositeIntentCondition.getIntentIdList()));
                }
            }
        });
        if (CollectionUtils.isNotEmpty(additionalIntentIdSet)) {
            IntentQuery intentQuery = IntentQuery.builder().intentIdList(additionalIntentIdSet).build();
            intentQuery.setWithPage(false);
            intentVOList = listWithoutPages(intentQuery);
            intentIdList.addAll(MyCollectionUtils.listToConvertList(intentVOList, IntentVO::getId));
        }

        List<IntentCorpusPO> intentCorpusPOList = intentCorpusService.findByIntentIdIn(intentIdList);
        IntentConfigPO intentConfigPO = intentConfigService.detail(srcBotId);

        List<IntentVO> finalIntentVOList = intentVOList;
        AtomicInteger successNum = new AtomicInteger(0);
        AtomicInteger failNum = new AtomicInteger(0);
        List<Long> successBotIdList = new ArrayList<>();
        List<Long> failBotIdList = new ArrayList<>();
        try {
            List<Future<Long>> futureList = Lists.newArrayList();
            for (Long botId : targetBotIdList) {
                Future<Long> future = BotSyncThreadExecutorHelper.submit("意图同步", new MDCDecoratorCallable<>(() -> {
                    try {
                        // 同步语料
                        singleSync(botId, sameIntent, currentUserId, DeepCopyUtils.copyList(finalIntentVOList), DeepCopyUtils.copyList(intentCorpusPOList));

                        // 同步意图设置
                        IntentConfigPO targetIntentConfig = intentConfigService.detail(botId);
                        if (BooleanUtils.isNotFalse(syncIntentConfig)) {
                            BeanUtils.copyProperties(intentConfigPO, targetIntentConfig, "id", "botId", "createTime", "createUserId");
                            targetIntentConfig.setUpdateUserId(syncVO.getCurrentUserId());
                            intentConfigService.save(targetIntentConfig);
                        }

                        // 如果启用了问法，自动训练
                        if (BooleanUtils.isTrue(targetIntentConfig.getEnableAlgorithm())) {
                            trainService.train(botId, currentUserId, null, systemType);
                        }
                    } catch (Exception e) {
                        log.error("singleSync错误", e);
                        return 0L;
                    }
                    return botId;
                }));
                futureList.add(future);
            }

            for (Future<Long> future : futureList) {
                if (future.get() > 0L) {
                    successNum.getAndIncrement();
                    successBotIdList.add(future.get());
                } else {
                    failNum.getAndIncrement();
                    failBotIdList.add(future.get());
                }
            }
        } catch (Exception e) {
            log.error("意图同步异步线程池出错", e);
        }
        botSyncOperationLogService.intentSync(syncVO, intentVOList, successBotIdList, failBotIdList);
        return BotSyncResultVO.builder().successNum(successNum.get()).failNum(failNum.get()).build();
    }

    /**
     * 获取意图集合中与目标bot的重名意图和不重名意图集合
     *
     * @param targetBotId
     * @param intentPOList
     * @return
     */
    public Tuple2<List<? extends IntentPO>, List<? extends IntentPO>> getCreateAndUpdateIntentList(Long targetBotId, List<? extends IntentPO> intentPOList) {
        if (CollectionUtils.isEmpty(intentPOList)) {
            return new Tuple2<>(Collections.emptyList(), Collections.emptyList());
        }
        List<String> nameList = MyCollectionUtils.listToConvertList(intentPOList, IntentPO::getName);
        Query query = Query.query(Criteria.where("botId").is(targetBotId).and("name").in(nameList));
        List<IntentPO> existsIntentPOList = mongoTemplate.find(query, IntentPO.class);
        List<String> existsIntentNameList = MyCollectionUtils.listToConvertList(existsIntentPOList, IntentPO::getName);
        // 更新列表
        List<IntentPO> updateList = intentPOList.stream().filter(intentPO -> existsIntentNameList.contains(intentPO.getName())).collect(Collectors.toList());
        // 插入列表
        List<IntentPO> createList = intentPOList.stream().filter(intentPO -> !existsIntentNameList.contains(intentPO.getName())).collect(Collectors.toList());
        return new Tuple2<>(createList, updateList);
    }

    /**
     * 单个bot同步
     */
    @Override
    public List<? extends IntentPO> singleSync(Long targetBotId, SyncModeEnum syncMode, Long currentUserId, List<? extends IntentPO> intentPOList, List<IntentCorpusPO> intentCorpusPOList) {
        if (CollectionUtils.isEmpty(intentPOList)) {
            return Lists.newArrayList();
        }
        Long srcBotId = intentPOList.get(0).getBotId();
        List<? extends IntentPO> copiedIntentPOList = DeepCopyUtils.copyList(intentPOList);

        List<String> nameList = MyCollectionUtils.listToConvertList(intentPOList, IntentPO::getName);
        Query query = Query.query(Criteria.where("botId").is(targetBotId).and("name").in(nameList));
        List<IntentPO> existsIntentPOList = mongoTemplate.find(query, IntentPO.class);
        List<String> existsIntentNameList = MyCollectionUtils.listToConvertList(existsIntentPOList, IntentPO::getName);
        // 更新列表
        List<IntentPO> updateList = intentPOList.stream().filter(intentPO -> existsIntentNameList.contains(intentPO.getName())).collect(Collectors.toList());
        // 插入列表
        List<IntentPO> createList = intentPOList.stream().filter(intentPO -> !existsIntentNameList.contains(intentPO.getName())).collect(Collectors.toList());

        List<IntentCorpusPO> finalCorpusList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<String> existsIntentIdList = MyCollectionUtils.listToConvertList(existsIntentPOList, IntentPO::getId);
            Map<String, IntentCorpusPO> corpusNameMap = MyCollectionUtils.listToMap(intentCorpusPOList, IntentCorpusPO::getName);
            List<IntentCorpusPO> existsIntentCorpusPOList = intentCorpusService.findByIntentIdIn(existsIntentIdList);
            switch (syncMode) {
                case COVER:
                    // 清除组合意图的关联关系
                    existsIntentPOList.stream()
                            .filter(intentPO -> IntentTypeEnum.COMPOSITE.equals(intentPO.getIntentType()))
                            .forEach(intentPO -> {
                                List<CompositeIntentCondition> compositeConditionList = intentPO.getCompositeConditionList();
                                if (CollectionUtils.isNotEmpty(compositeConditionList)) {
                                    compositeConditionList.forEach(compositeIntentCondition -> sourceRefService.deleteBySourceIdList(targetBotId, compositeIntentCondition.getIntentIdList(), IntentRefTypeEnum.COMPOSITE_INTENT));
                                }
                            });

                    // 这边保留了原先intent的intentId，不用再处理关联关系了
                    Map<String, String> nameMap = MyCollectionUtils.listToMap(existsIntentPOList, IntentPO::getName, IntentPO::getId);
                    Map<String, String> groupMap = MyCollectionUtils.listToMap(existsIntentPOList, IntentPO::getName, IntentPO::getGroupId);
                    updateList.forEach(intentPO -> {
                        intentPO.setId(nameMap.get(intentPO.getName()));
                        intentPO.setBotId(targetBotId);
                        if (intentPO.getIntentType().equals(IntentTypeEnum.SINGLE)) {
                            intentPO.setTrainStatus(TrainStatusEnum.CREATED);
                            intentPO.setGroupId(groupMap.get(intentPO.getName()));
                        }
                        intentPO.setUpdateTime(LocalDateTime.now());
                        intentPO.setUpdateUserId(currentUserId);
                    });
                    mongoTemplate.remove(query, IntentPO.class);
                    mongoTemplate.insert(updateList, IntentPO.class);

                    // 当同名的意图是单个意图覆盖组合意图时，会存在意图语料不存在的情况
                    ArrayList<IntentCorpusPO> toBeSavedList = Lists.newArrayList();
                    Map<String, IntentCorpusPO> existsCorpusNameMap = MyCollectionUtils.listToMap(existsIntentCorpusPOList, IntentCorpusPO::getName);
                    existsIntentPOList.forEach(existsIntentPO -> {
                        String intentName = existsIntentPO.getName();
                        IntentCorpusPO intentCorpusPO = existsCorpusNameMap.get(intentName);
                        if (Objects.isNull(intentCorpusPO)) {
                            intentCorpusPO = new IntentCorpusPO();
                            intentCorpusPO.setBotId(targetBotId);
                            intentCorpusPO.setIntentId(existsIntentPO.getId());
                            intentCorpusPO.setName(intentName);
                        }
                        IntentCorpusPO originalIntentCorpusPO = corpusNameMap.get(intentCorpusPO.getName());
                        if (Objects.nonNull(originalIntentCorpusPO)) {
                            intentCorpusPO.setCorpusList(originalIntentCorpusPO.getCorpusList());
                            intentCorpusPO.setRegexList(originalIntentCorpusPO.getRegexList());
                            intentCorpusPO.setDescList(originalIntentCorpusPO.getDescList());
                            // 兼容内置意图调用
                            intentCorpusPO.setBuildInCorpusList(originalIntentCorpusPO.getBuildInCorpusList());
                            intentCorpusPO.setBuildInRegexList(originalIntentCorpusPO.getBuildInRegexList());
                        }
                        toBeSavedList.add(intentCorpusPO);
                    });
                    intentCorpusService.saveAll(toBeSavedList);
                    finalCorpusList.addAll(toBeSavedList);

                    break;
                case COMPLETE:
                    // 清除组合意图的关联关系
                    existsIntentPOList.stream()
                            .filter(intentPO -> IntentTypeEnum.COMPOSITE.equals(intentPO.getIntentType()))
                            .forEach(intentPO -> {
                                List<CompositeIntentCondition> compositeConditionList = intentPO.getCompositeConditionList();
                                if (CollectionUtils.isNotEmpty(compositeConditionList)) {
                                    compositeConditionList.forEach(compositeIntentCondition -> sourceRefService.deleteBySourceIdList(targetBotId, compositeIntentCondition.getIntentIdList(), IntentRefTypeEnum.COMPOSITE_INTENT));
                                }
                            });

                    existsIntentCorpusPOList.forEach(intentCorpusPO -> {
                        IntentCorpusPO originalIntentCorpusPO = corpusNameMap.get(intentCorpusPO.getName());
                        if (Objects.nonNull(originalIntentCorpusPO)) {
                            intentCorpusPO.setCorpusList(MyCollectionUtils.mergeAndDeduplicate(intentCorpusPO.getCorpusList(), originalIntentCorpusPO.getCorpusList()));
                            intentCorpusPO.setRegexList(MyCollectionUtils.mergeAndDeduplicate(intentCorpusPO.getRegexList(), originalIntentCorpusPO.getRegexList()));
                            intentCorpusPO.setDescList(MyCollectionUtils.mergeAndDeduplicate(intentCorpusPO.getDescList(), originalIntentCorpusPO.getDescList()));
                        }
                    });
                    intentCorpusService.saveAll(existsIntentCorpusPOList);
                    finalCorpusList.addAll(existsIntentCorpusPOList);

                    break;
                case SKIP:
                default:
                    break;
            }
        }

        // 获取根目录
        GroupPO rootGroup = groupService.getRootGroup(targetBotId, GroupTypeEnum.SINGLE_INTENT);
        Assert.notNull(rootGroup, "数据错误！根目录不能为空");

        createList.forEach(intentPO -> {
            intentPO.setId(null);
            intentPO.setBotId(targetBotId);
            if (intentPO.getIntentType().equals(IntentTypeEnum.SINGLE)) {
                intentPO.setGroupId(rootGroup.getId());
            }
            intentPO.setTrainStatus(TrainStatusEnum.CREATED);
            intentPO.setCreateTime(LocalDateTime.now());
            intentPO.setUpdateTime(LocalDateTime.now());
            intentPO.setCreateUserId(currentUserId);
            intentPO.setUpdateUserId(currentUserId);
        });
        mongoTemplate.insert(createList, IntentPO.class);

        Map<String, String> intentMap = MyCollectionUtils.listToMap(createList, IntentPO::getName, IntentPO::getId);
        intentCorpusPOList = intentCorpusPOList.stream()
                .peek(intentCorpusPO -> {
                    intentCorpusPO.setId(null);
                    intentCorpusPO.setBotId(targetBotId);
                    intentCorpusPO.setIntentId(intentMap.get(intentCorpusPO.getName()));
                })
                .filter(intentCorpusPO -> StringUtils.isNotEmpty(intentCorpusPO.getIntentId()))
                .collect(Collectors.toList());
        intentCorpusService.saveAll(intentCorpusPOList);
        finalCorpusList.addAll(intentCorpusPOList);

        autoSyncVarAndCreateSourceRef(finalCorpusList, targetBotId, currentUserId, srcBotId);

        if (SyncModeEnum.COVER.equals(syncMode)) {
            handleCompositeIntent(targetBotId, copiedIntentPOList, createList);
        }

        // 更新Bot状态
        botService.updateAuditStatus(targetBotId, AuditStatusEnum.DRAFT);

        return createList;
    }

    private void handleCompositeIntent(Long targetBotId, List<? extends IntentPO> intentPOList, List<IntentPO> createList) {
        Map<String, String> intentIdMapping = new HashMap<>();
        List<IntentPO> allIntentPOList = getAllByBotId(targetBotId);
        // 目标Bot原先的意图名称列表
        Map<String, String> existsIntentNameIdMap = MyCollectionUtils.listToMap(allIntentPOList, IntentPO::getName, IntentPO::getId);
        Set<String> existsIntentNameKeySet = existsIntentNameIdMap.keySet();

        Map<String, String> createNodeIntentNameIdMap = MyCollectionUtils.listToMap(createList, IntentPO::getName, IntentPO::getId);
        intentPOList.forEach(intentPO -> {
            if (existsIntentNameKeySet.contains(intentPO.getName())) {
                // 如果含有重名意图，就用目标Bot内该意图的ID
                intentIdMapping.put(intentPO.getId(), existsIntentNameIdMap.get(intentPO.getName()));
            } else {
                // 如果不重名，说明需要新建意图，用新建的意图ID代替
                intentIdMapping.put(intentPO.getId(), createNodeIntentNameIdMap.get(intentPO.getName()));
            }
        });

        intentPOList.forEach(intentPO -> {
            if (intentPO.getIntentType().equals(IntentTypeEnum.COMPOSITE)) {
                // 清除原关联
                String refId = intentIdMapping.get(intentPO.getId());
                sourceRefService.deleteSourceByRefIdList(intentPO.getBotId(), Lists.newArrayList(refId), IntentRefTypeEnum.COMPOSITE_INTENT);

                Set<String> refIntentIdList = Sets.newHashSet();
                List<CompositeIntentCondition> compositeConditionList = intentPO.getCompositeConditionList();
                if (CollectionUtils.isNotEmpty(compositeConditionList)) {
                    compositeConditionList.forEach(
                            compositeIntentCondition -> {
                                List<String> collect = compositeIntentCondition.getIntentIdList()
                                        .stream()
                                        .map(intentIdMapping::get)
                                        .filter(StringUtils::isNotEmpty)
                                        .collect(Collectors.toList());
                                compositeIntentCondition.setIntentIdList(collect);
                                refIntentIdList.addAll(collect);
                            }
                    );
                    // 创建组合意图的关联
                    List<String> collect = refIntentIdList.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        SourceRefBO sourceRefBO = new SourceRefBO();
                        sourceRefBO.setBotId(targetBotId);
                        sourceRefBO.setSourceType(SourceTypeEnum.INTENT);
                        sourceRefBO.setRefId(refId);
                        sourceRefBO.setRefType(IntentRefTypeEnum.COMPOSITE_INTENT);
                        sourceRefBO.setSourceIdSet(refIntentIdList);
                        sourceRefService.saveSourceRef(sourceRefBO);

                        IntentPO targetIntentPO = mongoTemplate.findOne(Query.query(Criteria.where("botId").is(targetBotId).and("name").is(intentPO.getName())), IntentPO.class);
                        targetIntentPO.setCompositeConditionList(compositeConditionList);
                        mongoTemplate.save(targetIntentPO);
                    }
                }
            }
        });
    }

    @Override
    public List<IntentPO> delete(IntentQuery intentQuery, Long userId) {
        Query query = toQuery(intentQuery);
        List<IntentPO> intentPOList = mongoTemplate.find(query, IntentPO.class);
        List<IntentPO> notDeletedIntentList = null;
        if (CollectionUtils.isNotEmpty(intentPOList)) {
            List<String> intentIdList = MyCollectionUtils.listToConvertList(intentPOList, IntentPO::getId);

            // 校验关联
            List<SourceRefPO> refIdList = sourceRefService.getBySourceIdList(intentQuery.getBotId(), intentIdList, SourceTypeEnum.INTENT);
            if (CollectionUtils.isNotEmpty(refIdList)) {
                List<String> collisionIdList = MyCollectionUtils.listToConvertList(refIdList, SourceRefPO::getSourceId);
                notDeletedIntentList = intentPOList.stream().filter(intentPO -> collisionIdList.contains(intentPO.getId())).collect(Collectors.toList());
                intentPOList = intentPOList.stream().filter(intentPO -> !collisionIdList.contains(intentPO.getId())).collect(Collectors.toList());
                intentIdList = MyCollectionUtils.listToConvertList(intentPOList, IntentPO::getId);
            }

            if (CollectionUtils.isNotEmpty(intentIdList)) {
                // 删除语料
                intentCorpusService.deleteByIntentIdList(intentIdList);

                // 如果是组合意图，删除关联-+
                List<String> compositeIntentIdList = intentPOList.stream()
                        .filter(intentPO -> IntentTypeEnum.COMPOSITE.equals(intentPO.getIntentType()))
                        .map(IntentPO::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(compositeIntentIdList)) {
                    sourceRefService.deleteSourceByRefIdList(intentQuery.getBotId(), compositeIntentIdList, IntentRefTypeEnum.COMPOSITE_INTENT);
                }

                intentIdList.forEach(intentId -> sourceRefService.deleteSourceByRefId(intentQuery.getBotId(), intentId));

                Query query1 = Query.query(Criteria.where("_id").in(intentIdList));
                mongoTemplate.remove(query1, IntentPO.class);

                //判断内置意图剩余数量
                Query buildInQuery = Query.query(Criteria.where("botId").is(intentQuery.getBotId()).and("corpusType").is("BUILD_IN"));
                List<IntentPO> buildInIntentPOList = mongoTemplate.find(buildInQuery, IntentPO.class);
                if (CollectionUtils.isEmpty(buildInIntentPOList)) {
                    botService.updateDomainName(intentQuery.getBotId(), "");
                }

                // 更新Bot状态
                botService.updateAuditStatus(intentPOList.get(0).getBotId(), AuditStatusEnum.DRAFT);

                // TODO 设置为可以训练

                buildLogAndSave(intentPOList, userId);
            }
        }
        return notDeletedIntentList;
    }

    private void buildLogAndSave(List<IntentPO> deletedIntentList, Long userId) {
        if (CollectionUtils.isEmpty(deletedIntentList)) {
            return;
        }
        List<OperationLogDTO> list = Lists.newArrayList();
        Map<Long, List<IntentPO>> botIntentMap = deletedIntentList.stream().collect(Collectors.groupingBy(IntentPO::getBotId));
        for (Map.Entry<Long, List<IntentPO>> botEntry : botIntentMap.entrySet()) {
            Map<IntentTypeEnum, List<IntentPO>> intentTypeMap = botEntry.getValue().stream().collect(Collectors.groupingBy(IntentPO::getIntentType));
            for (Map.Entry<IntentTypeEnum, List<IntentPO>> entry : intentTypeMap.entrySet()) {
                OperationLogResourceTypeEnum resourceType = IntentTypeEnum.SINGLE.equals(entry.getKey()) ? OperationLogResourceTypeEnum.SINGLE_INTENT : OperationLogResourceTypeEnum.COMPOSITE_INTENT;
                String detail = String.format("删除%s意图%s", IntentTypeEnum.SINGLE.equals(entry.getKey()) ? "单个" : "组合",
                        entry.getValue().stream().map(s -> "【" + s.getName() + "】").collect(Collectors.joining("、")));
                list.add(OperationLogDTO.builder().botId(botEntry.getKey())
                        .type(OperationLogTypeEnum.INTENT)
                        .resourceType(resourceType)
                        .detail(detail)
                        .operatorId(userId)
                        .build()
                );
            }
        }
        operationLogService.batchSave(list);
    }

    @Override
    public PageResultObject<IntentVO> list(IntentQuery intentQuery) {
        buildAllSearchScope(intentQuery);
        Query query = toUnPagedQuery(intentQuery);
        long count = mongoTemplate.count(query, IntentPO.class);

        if (intentQuery.getWithPage()) {
            query.with(PageRequest.of(intentQuery.getPageNum() - 1, intentQuery.getPageSize()));
        }
        query.with(Sort.by(Sort.Order.desc("updateTime")));
        List<IntentPO> intentPOList = mongoTemplate.find(query, IntentPO.class);
        List<IntentVO> intentVOList = wrapVOList(intentPOList);

        return PageResultObject.of(intentVOList, intentQuery.getPageNum(), intentQuery.getPageSize(), (int) count);
    }

    @Override
    public List<IdNamePair<String, String>> simpleList(Long botId) {
        Query query = Query.query(Criteria.where("botId").is(botId));
        query.fields().include("_id").include("name");
        query.with(Sort.by(Sort.Direction.DESC, "_id"));
        return mongoTemplate.find(query, IntentPO.class, MongoCollectionNameCenter.INTENT)
                .stream().map(intent -> new IdNamePair<>(intent.getId(), intent.getName()))
                .collect(Collectors.toList());
    }

    private void buildAllSearchScope(IntentQuery intentQuery) {
        intentQuery.setIntentSearchScopeList(Arrays.asList(IntentSearchScopeEnum.INTENT_NAME,
                IntentSearchScopeEnum.KEYWORD, IntentSearchScopeEnum.CORPUS, IntentSearchScopeEnum.DESC));
    }

    @Override
    public List<IntentVO> queryForList(IntentQuery intentQuery) {
        Query query = toUnPagedQuery(intentQuery);
        if (intentQuery.getWithPage()) {
            query.with(PageRequest.of(intentQuery.getPageNum() - 1, intentQuery.getPageSize()));
        }
        query.with(Sort.by(Sort.Order.desc("updateTime")));
        List<IntentPO> intentPOList = mongoTemplate.find(query, IntentPO.class);
        List<IntentVO> intentVOList = wrapVOList(intentPOList);

        return intentVOList;
    }

    @Override
    public IntentVO detail(String intentId) {
        IntentPO intentPO = mongoTemplate.findById(intentId, IntentPO.class);
        return wrapVO(intentPO);
    }

    @Override
    public List<IntentVO> listWithoutPages(IntentQuery intentQuery) {
        Query query = toQuery(intentQuery);
        List<IntentPO> intentPOList = mongoTemplate.find(query, IntentPO.class);
        return wrapVOList(intentPOList);
    }

    @Override
    public List<IntentPO> getIntentPOList(IntentQuery intentQuery) {
        Query query = toQuery(intentQuery);
        return mongoTemplate.find(query, IntentPO.class);
    }

    @Override
    public Optional<IntentVO> getByName(Long botId, String name) {
        Query query = Query.query(Criteria.where("name").is(name).and("botId").is(botId));
        IntentPO intentPO = mongoTemplate.findOne(query, IntentPO.class);
        if (Objects.nonNull(intentPO)) {
            IntentVO intentVO = new IntentVO();
            BeanUtils.copyProperties(intentPO, intentVO);
//            List<IntentRefPO> intentRefPOList = intentRefService.getByIntentIdList(Collections.singletonList(intentVO.getId()));
            List<SourceRefPO> intentRefPOList = sourceRefService.getBySourceIdList(botId, Collections.singletonList(intentVO.getId()), SourceTypeEnum.INTENT);
            intentVO.setIntentRefPOList(intentRefPOList);
            return Optional.of(intentVO);
        }
        return Optional.empty();
    }

    @Override
    public List<IntentPO> getAllByBotId(Long botId) {
        Query query = Query.query(Criteria.where("botId").is(botId));
        return mongoTemplate.find(query, IntentPO.class);
    }

    @Override
    public Boolean hasIntent(Long botId) {
        Query query = Query.query(Criteria.where("botId").is(botId));
        return mongoTemplate.exists(query, IntentPO.class);
    }

    @Override
    public List<IntentPO> getByIdList(Collection<String> intentIdList) {
        if (CollectionUtils.isEmpty(intentIdList)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(intentIdList));
        return mongoTemplate.find(query, IntentPO.class, MongoCollectionNameCenter.INTENT);
    }

    @Override
    public Map<String, String> getNameByIdList(Collection<String> intentIdList) {
        return MyCollectionUtils.listToConvertMap(getByIdList(intentIdList), IntentPO::getId, IntentPO::getName);
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        List<IntentPO> intentPOList = getAllByBotId(context.getSrcBotId());
        context.getSnapshot().setIntentList(intentPOList);
    }

    @Override
    public void validateResource(RobotResourceContext context) {
        for (IntentPO intent : context.getSnapshot().getIntentList()) {
            try {
                validAndThrow(intent, context.getDependentResource());
            } catch (ComException e) {
                SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg.builder()
                        .resourceId(intent.getId())
                        .resourceType(BotResourceTypeEnum.INTENT)
                        .failMsg(e.getDetailMsg())
                        .build();
                context.getInvalidMsgList().add(msg);
            }
        }

        // 校验意图名称是否重复

        List<IntentPO> intentList = context.getSnapshot().getIntentList();
        Set<String> names = new HashSet<>();
        intentList.forEach(intent -> {
            if (names.contains(intent.getName())) {
                SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg.builder()
                        .resourceId(intent.getId())
                        .resourceType(BotResourceTypeEnum.INTENT)
                        .failMsg(String.format("意图名称重复:%s", intent.getName()))
                        .build();
                context.getInvalidMsgList().add(msg);
            }
            names.add(intent.getName());
        });

        // 校验意图是否存在冲突
        context.getInvalidMsgList().addAll(validIntentConflict(context.getSnapshot(), context.getDependentResource()));

        // 校验意图描述是否冲突
        context.getInvalidMsgList().addAll(validDescConflict(context.getSnapshot()));

        // 校验模型群里情况
        SnapshotValidateConfigBO validateConfig = context.getValidateConfig();
        RobotSnapshotPO snapshot = context.getSnapshot();
        IntentConfigPO intentConfig = snapshot.getIntentConfig();
        ModelTrainKey modelTrainKey = ModelTrainKey.of(snapshot.getBotId());
        // 非轻量版 bot, 需要校验模型是否训练
        if (!V3BotTypeEnum.isMagic(snapshot.getBot().getType())
                && Objects.nonNull(intentConfig)
                && BooleanUtils.isTrue(intentConfig.getEnableAlgorithm())) {
            if (BooleanUtils.isTrue(validateConfig.getRequireExistIntentModel())) {
                TrainResultPO trainResult = trainService.lastSuccess(modelTrainKey);
                if (Objects.isNull(trainResult)) {
                    SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                            .builder()
                            .resourceType(BotResourceTypeEnum.INTENT_MODEL)
                            .failMsg("NLP模型未训练")
                            .build();
                    context.getInvalidMsgList().add(msg);
                }
            }

            if (!V3BotTypeEnum.isMagic(snapshot.getBot().getType())
                    && BooleanUtils.isTrue(validateConfig.getIntentModelNotTraining())) {
                if (BooleanUtils.isTrue(trainService.queryIsTraining(modelTrainKey))) {
                    SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                            .builder()
                            .resourceType(BotResourceTypeEnum.INTENT_MODEL)
                            .failMsg("NLP模型训练中")
                            .build();
                    context.getInvalidMsgList().add(msg);
                }
            }
        }
    }

    private List<SnapshotInvalidFailItemMsg> validDescConflict(RobotSnapshotPO snapshot) {
        List<IntentCorpusPO> corpusList = snapshot.getIntentCorpusList();
        if (CollectionUtils.isEmpty(corpusList)) {
            return Collections.emptyList();
        }
        Map<String, List<Tuple2<String, String>>> descIntentListMap = new HashMap<>();
        for (IntentCorpusPO corpus : corpusList) {
            List<String> descList = corpus.getDescList();
            if (CollectionUtils.isEmpty(descList)) {
                continue;
            }
            descList.forEach(desc -> descIntentListMap.computeIfAbsent(desc, k -> new ArrayList<>()).add(Tuple.of(corpus.getIntentId(), corpus.getName())));
        }
        List<SnapshotInvalidFailItemMsg> errMsgList = new ArrayList<>();
        descIntentListMap.forEach((desc, intentList) -> {
            if (intentList.size() > 1) {
                SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg.builder()
                        .resourceType(BotResourceTypeEnum.INTENT)
                        .resourceId(intentList.get(0)._1)
                        .failMsg(String.format("意图[%s]同时存在描述[%s]", intentList.stream().map(Tuple2::_2).collect(Collectors.joining(",")), desc))
                        .build();
                errMsgList.add(msg);
            }
        });
        return errMsgList;
    }

    private List<SnapshotInvalidFailItemMsg> validIntentConflict(RobotSnapshotPO snapshot, DependentResourceBO dependentResource) {
        // 问答知识
        // 流程
        // 特殊语境
        Map<String, List<String>> intentDependMap = new HashMap<>();
        snapshot.getStepList().forEach(step -> {
            if (BooleanUtils.isTrue(step.getTriggerByIntent())) {
                step.getTriggerIntentIdList().forEach(intentId -> {
                    List<String> dependNameList = intentDependMap.computeIfAbsent(intentId, (id) -> new ArrayList<>());
                    dependNameList.add(String.format("流程:%s", step.getName()));
                });
            }
        });

        snapshot.getKnowledgeList().forEach(knowledge -> {
            knowledge.getTriggerIntentIdList().forEach((intentId) -> {
                List<String> dependNameList = intentDependMap.computeIfAbsent(intentId, (id) -> new ArrayList<>());
                dependNameList.add(String.format("知识:%s", knowledge.getName()));
            });
        });

        snapshot.getSpecialAnswerConfigList().forEach(specialAnswer -> {
            if (EnabledStatusEnum.ENABLE.equals(specialAnswer.getEnabledStatus()) && CollectionUtils.isNotEmpty(specialAnswer.getTriggerIntentIdList())) {
                specialAnswer.getTriggerIntentIdList().forEach(intentId -> {
                    List<String> dependNameList = intentDependMap.computeIfAbsent(intentId, (id) -> new ArrayList<>());
                    dependNameList.add(String.format("特殊语境:%s", specialAnswer.getName()));
                });
            }
        });

        List<SnapshotInvalidFailItemMsg> errMsgList = new ArrayList<>();
        intentDependMap.forEach((intentId, dependList) -> {
            if (CollectionUtils.size(dependList) > 1) {
                String intentName = dependentResource.getIntentIdNameMap().get(intentId);
                SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg.builder()
                        .resourceType(BotResourceTypeEnum.INTENT)
                        .resourceId(intentId)
                        .resourceName(intentName)
                        .failMsg(String.format("意图: %s 被多次选中: [%s]", intentName, String.join(",", dependList)))
                        .build();
                errMsgList.add(msg);
            }
        });
        return errMsgList;
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        Long targetBotId = context.getTargetBotId();
        Long currentUserId = context.getCurrentUserId();
        ResourceCopyReferenceMappingBO mapping = context.getResourceCopyReferenceMapping();
        List<IntentPO> intentPOList = context.getSnapshot().getIntentList();
        Map<String, String> groupIdMapping = mapping.getGroupIdMapping();
        if (context.isCopy()) {
            Map<String, String> intentIdMapping = mapping.getIntentIdMapping();
            intentPOList.forEach(intentPO -> {
                String oldId = intentPO.getId();
                String newId = new ObjectId().toString();
                intentIdMapping.put(oldId, newId);
            });

            List<IntentPO> newList = intentPOList.stream()
                    .peek(intentPO -> {
                        intentPO.setId(intentIdMapping.get(intentPO.getId()));
                        intentPO.setBotId(targetBotId);
                        intentPO.setTrainStatus(TrainStatusEnum.CREATED);
                        intentPO.setCreateTime(LocalDateTime.now());
                        intentPO.setUpdateTime(LocalDateTime.now());
                        intentPO.setCreateUserId(currentUserId);
                        intentPO.setCreateUserId(currentUserId);
                        intentPO.setGroupId(groupIdMapping.get(intentPO.getGroupId()));
                        if (IntentTypeEnum.COMPOSITE.equals(intentPO.getIntentType())
                                && CollectionUtils.isNotEmpty(intentPO.getCompositeConditionList())) {
                            List<CompositeIntentCondition> newCompositeIntentList = intentPO.getCompositeConditionList().stream().peek(p -> {
                                List<String> intentIdList = new ArrayList<>();
                                if (CollectionUtils.isNotEmpty(p.getIntentIdList())) {
                                    p.getIntentIdList().forEach(oldId -> intentIdList.add(intentIdMapping.get(oldId)));
                                }
                                p.setIntentIdList(intentIdList);
                            }).collect(Collectors.toList());
                            intentPO.setCompositeConditionList(newCompositeIntentList);
                        }
                    }).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(newList)) {
                mongoTemplate.insert(newList, MongoCollectionNameCenter.INTENT);
            }
        }
    }

    @Override
    public List<Class<? extends RobotResourceService>> dependsOn() {
        return Collections.singletonList(GroupServiceImpl.class);
    }

    @Override
    public void updateTrainingStatus(Long botId) {
        updateTrainingStatus(botId, TrainStatusEnum.TRAINING_SUCCESS);
    }

    @Override
    public void updateTrainingStatus(Long botId, TrainStatusEnum status) {
        Query query = Query.query(Criteria.where("botId").is(botId).and("trainStatus").is(TrainStatusEnum.CREATED));
        Update update = Update.update("trainStatus", status);
        update.set("updateTime", LocalDateTime.now());
        mongoTemplate.updateMulti(query, update, IntentPO.class);
    }

    @Override
    public boolean enableTrain(Long botId) {
        Query query = Query.query(Criteria.where("botId").is(botId).and("trainStatus").in(TrainStatusEnum.CREATED, TrainStatusEnum.TRAINING_FAIL, TrainStatusEnum.TRAINING_TIMEOUT));
        return mongoTemplate.exists(query, IntentPO.class);
    }

    @Override
    public boolean nameExists(String intentId, Collection<String> nameList, Collection<Long> botIdList) {
        Query query = Query.query(Criteria.where("botId").in(botIdList).and("name").in(nameList));
        if (StringUtils.isNotEmpty(intentId)) {
            query.addCriteria(Criteria.where("_id").ne(intentId));
        }
        return mongoTemplate.exists(query, IntentPO.class);
    }

    @Override
    public Long nameExistsCount(Collection<String> nameList, Long botId) {
        Query query = Query.query(Criteria.where("botId").is(botId).and("name").in(nameList));
        return mongoTemplate.count(query, IntentPO.class);
    }

    private Query toQuery(IntentQuery intentQuery) {
        buildAllSearchScope(intentQuery);
        Query query = toUnPagedQuery(intentQuery);
        if (intentQuery.getWithPage()) {
            query.with(PageRequest.of(intentQuery.getPageNum() - 1, intentQuery.getPageSize()));
        }
        return query;
    }

    private Query toUnPagedQuery(IntentQuery intentQuery) {
        Query query = new Query();

        // ES搜索问法
        if (StringUtils.isEmpty(intentQuery.getIntentId())
                && CollectionUtils.isEmpty(intentQuery.getIntentIdList())
                && StringUtils.isNotEmpty(intentQuery.getKeyword())) {
            Set<String> intentIdList;
            IntentCorpusQuery intentCorpusQuery = IntentCorpusQuery.builder()
                    .botId(intentQuery.getBotId())
                    .keyword(intentQuery.getKeyword())
                    .intentSearchScopeList(intentQuery.getIntentSearchScopeList())
                    .build();
            List<IntentCorpusPO> intentCorpusPOList = intentCorpusService.scroll(intentCorpusQuery);
            if (CollectionUtils.isEmpty(intentCorpusPOList)) {
                // 如果es的查询结果为空，需要将mongo的查询结果也置空
                intentIdList = Sets.newHashSet("-1");
            } else {
                intentIdList = intentCorpusPOList.stream().map(IntentCorpusPO::getIntentId).collect(Collectors.toSet());
            }
            intentQuery.setIntentIdList(intentIdList);
        }

        if (StringUtils.isNotEmpty(intentQuery.getIntentId())) {
            query.addCriteria(Criteria.where("_id").is(intentQuery.getIntentId()));
        } else {
            if (CollectionUtils.isNotEmpty(intentQuery.getIntentIdList())) {
                query.addCriteria(Criteria.where("_id").in(intentQuery.getIntentIdList()));
            }
            if (Objects.nonNull(intentQuery.getBotId())) {
                query.addCriteria(Criteria.where("botId").is(intentQuery.getBotId()));
            }
            if (Objects.nonNull(intentQuery.getIntentType())) {
                query.addCriteria(Criteria.where("intentType").is(intentQuery.getIntentType().name()));
            }
            if (Objects.nonNull(intentQuery.getCorpusType())) {
                query.addCriteria(Criteria.where("corpusType").is(intentQuery.getCorpusType().name()));
            }
            if (Objects.nonNull(intentQuery.getIntentProperties())) {
                query.addCriteria(Criteria.where("intentProperties").is(intentQuery.getIntentProperties().name()));
            }
            if (StringUtils.isNotBlank(intentQuery.getGroupId())) {
                List<String> groupIdList = groupService.listCurrentAndDescendantGroupId(intentQuery.getGroupId());
                query.addCriteria(Criteria.where("groupId").in(groupIdList));
            }
        }

        return query;
    }

    private IntentVO wrapVO(IntentPO intentPO) {
        if (Objects.isNull(intentPO)) {
            return null;
        }
        IntentCorpusPO intentCorpusPO = intentCorpusService.findByIntentId(intentPO.getId());
        IntentVO intentVO = new IntentVO();
        BeanUtils.copyProperties(intentPO, intentVO);
        if (Objects.nonNull(intentCorpusPO)) {
            intentVO.setCorpusList(CollectionUtils.isNotEmpty(intentCorpusPO.getCorpusList()) ? intentCorpusPO.getCorpusList() : Collections.emptyList());
            intentVO.setRegexList(CollectionUtils.isNotEmpty(intentCorpusPO.getRegexList()) ? intentCorpusPO.getRegexList() : Collections.emptyList());
            intentVO.setDescList(CollectionUtils.isNotEmpty(intentCorpusPO.getDescList()) ? intentCorpusPO.getDescList() : Collections.emptyList());
            intentVO.setCorpusCount(CollectionUtils.isEmpty(intentCorpusPO.getCorpusList()) ? 0 : intentCorpusPO.getCorpusList().size());
            intentVO.setRegexCount(CollectionUtils.isEmpty(intentCorpusPO.getRegexList()) ? 0 : intentCorpusPO.getRegexList().size());
            intentVO.setDescCount(CollectionUtils.isEmpty(intentCorpusPO.getDescList()) ? 0 : intentCorpusPO.getDescList().size());
        } else {
            intentVO.setCorpusList(Collections.emptyList());
            intentVO.setRegexList(Collections.emptyList());
            intentVO.setDescList(Collections.emptyList());
            intentVO.setCorpusCount(0);
            intentVO.setRegexCount(0);
            intentVO.setDescCount(0);
        }
        List<SourceRefPO> intentRefPOList = sourceRefService.getBySourceIdList(intentPO.getBotId(), Collections.singletonList(intentPO.getId()), SourceTypeEnum.INTENT);
        intentRefPOList = intentRefPOList.stream().filter(intent -> !IntentRefTypeEnum.COMPOSITE_INTENT.equals(intent.getRefType())).collect(Collectors.toList());
        intentVO.setIntentRefPOList(intentRefPOList);
        intentVO.setGroupPath(Optional.ofNullable(intentVO.getGroupId()).map(groupService::getById).map(GroupPO::getPath).orElse(null));
        return intentVO;
    }

    private List<IntentVO> wrapVOList(List<IntentPO> intentPOList) {
        if (CollectionUtils.isEmpty(intentPOList)) {
            return Lists.newArrayList();
        }

        List<String> intentIdList = intentPOList.stream().map(IntentPO::getId).collect(Collectors.toList());
        IntentCorpusQuery intentCorpusQuery = IntentCorpusQuery.builder()
                .intentIdList(intentIdList)
                .build();
        List<IntentCorpusPO> intentCorpusPOList = intentCorpusService.scroll(intentCorpusQuery);
        Map<String, IntentCorpusPO> intentCorpusPOMap = MyCollectionUtils.listToMap(intentCorpusPOList, IntentCorpusPO::getIntentId);

        Long botId = intentPOList.stream().map(IntentPO::getBotId).filter(Objects::nonNull).findFirst().orElse(0L);
        List<SourceRefPO> intentRefPOList = sourceRefService.getBySourceIdList(botId, intentIdList, SourceTypeEnum.INTENT);
        List<String> sourceIdList = intentRefPOList.stream().map(SourceRefPO::getSourceId).collect(Collectors.toList());
        // 组合意图的历史数据，没有就补上
        intentRefPOList.stream().filter(intent -> IntentRefTypeEnum.COMPOSITE_INTENT.equals(intent.getRefType())).forEach(sourceRefPO -> {
            IntentPO compositeIntent = mongoTemplate.findById(sourceRefPO.getRefId(), IntentPO.class);
            if (Objects.nonNull(compositeIntent)) {
                sourceRefPO.setRefLabel(compositeIntent.getName());
            }
        });
        Map<String, List<SourceRefPO>> intentRefPOMap = MyCollectionUtils.listToMapList(intentRefPOList, SourceRefPO::getSourceId);

        List<String> groupIdList = intentPOList.stream().map(IntentPO::getGroupId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, String> groupMap = MyCollectionUtils.listToMap(groupService.listByIds(groupIdList), GroupPO::getId, GroupPO::getPath);

        Map<Long, Set<String>> existsKnowledgeNameMap = new HashMap<>();

        return intentPOList.stream().map(intentPO -> {
            IntentVO intentVO = new IntentVO();
            BeanUtils.copyProperties(intentPO, intentVO);
            IntentCorpusPO intentCorpusPO = intentCorpusPOMap.get(intentPO.getId());
            if (Objects.nonNull(intentCorpusPO)) {
                List<String> corpusList = intentCorpusPO.getCorpusList();
                List<String> regexList = intentCorpusPO.getRegexList();
                List<String> descList = intentCorpusPO.getDescList();
                if (CollectionUtils.isEmpty(corpusList)) {
                    intentVO.setCorpusList(subList(intentCorpusPO.getBuildInCorpusList()));
                    intentVO.setCorpusCount(CollectionUtils.isEmpty(intentCorpusPO.getBuildInCorpusList()) ? 0 : intentCorpusPO.getBuildInCorpusList().size());
                } else {
                    intentVO.setCorpusList(corpusList);
                    intentVO.setCorpusCount(corpusList.size());
                }
                if (CollectionUtils.isEmpty(regexList)) {
                    intentVO.setRegexList(subList(intentCorpusPO.getBuildInRegexList()));
                    intentVO.setRegexCount(CollectionUtils.isEmpty(intentCorpusPO.getBuildInRegexList()) ? 0 : intentCorpusPO.getBuildInRegexList().size());
                } else {
                    intentVO.setRegexList(regexList);
                    intentVO.setRegexCount(regexList.size());
                }
                if (CollectionUtils.isEmpty(descList)) {
                    descList = Collections.emptyList();
                }
                intentVO.setDescList(descList);
                intentVO.setDescCount(descList.size());
            }
            intentVO.setIntentRefPOList(intentRefPOMap.get(intentPO.getId()));
            intentVO.setHasReference(sourceIdList.contains(intentPO.getId()));
            if (CollectionUtils.isNotEmpty(intentVO.getCompositeConditionList())) {
                intentVO.getCompositeConditionList().forEach(compositeIntentCondition -> {
                    List<IntentPO> byIdList = getByIdList(compositeIntentCondition.getIntentIdList());
                    List<String> intentNameList = MyCollectionUtils.listToConvertList(byIdList, IntentPO::getName);
                    compositeIntentCondition.setIntentNameList(intentNameList);
                });
            }
            intentVO.setGroupPath(Optional.ofNullable(intentPO.getGroupId()).map(groupMap::get).orElse(null));
            Set<String> existsKnowledgeNameList = existsKnowledgeNameMap.computeIfAbsent(intentPO.getBotId(), tmpBotId -> knowledgeService.getAllListByBotId(tmpBotId).stream().map(KnowledgePO::getName).collect(Collectors.toSet()));
            intentVO.setExistsSameNameKnowledge(existsKnowledgeNameList.contains(intentPO.getName()));
            return intentVO;
        }).collect(Collectors.toList());
    }

    /**
     * ArrayList$SubList无法被序列化，所以需要在外面包裹一层ArrayList
     */
    private List<String> subList(List<String> builtInCorpusList) {
        if (CollectionUtils.isNotEmpty(builtInCorpusList) && builtInCorpusList.size() > 1) {
            return Lists.newArrayList(builtInCorpusList.subList(0, 2));
        } else if (CollectionUtils.isNotEmpty(builtInCorpusList)) {
            return Lists.newArrayList(builtInCorpusList.subList(0, 1));
        }
        return Lists.newArrayList();
    }

    @Override
    public List<String> listIdByBotIdAndGroupIds(Long botId, List<String> groupIds) {
        Query query = Query.query(Criteria.where("botId").is(botId).and("groupId").in(groupIds));
        return mongoTemplate.findDistinct(query, "_id", MongoCollectionNameCenter.INTENT, IntentPO.class, ObjectId.class)
                .stream().map(ObjectId::toString).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeGroup(IntentChangeGroupVO changeGroupVO) {
        Assert.notNull(changeGroupVO.getBotId(), "botId不能为空");
        String targetGroupId = changeGroupVO.getTargetGroupId();
        Assert.isTrue(StringUtils.isNotBlank(targetGroupId), "目标分组不能为空");
        GroupPO targetGroup = groupService.selectOne(targetGroupId, GroupTypeEnum.SINGLE_INTENT, changeGroupVO.getBotId());
        Assert.notNull(targetGroup, "目标分组不存在");
        List<IntentPO> intentList = mongoTemplate.find(toQuery(changeGroupVO), IntentPO.class, MongoCollectionNameCenter.INTENT);
        if (CollectionUtils.isEmpty(intentList)) {
            return;
        }
        for (IntentPO intent : intentList) {
            intent.setGroupId(targetGroupId);
            intent.setUpdateTime(LocalDateTime.now());
            intent.setUpdateUserId(changeGroupVO.getUserId());
            mongoTemplate.save(intent, MongoCollectionNameCenter.INTENT);
        }
        botService.updateAuditStatus(changeGroupVO.getBotId(), AuditStatusEnum.DRAFT);

        operationLogService.save(changeGroupVO.getBotId(), OperationLogTypeEnum.GROUP, OperationLogResourceTypeEnum.INTENT_GROUP,
                String.format("移动意图%s至分组【%s】", intentList.stream().map(s -> String.format("【%s】", s.getName())).collect(Collectors.joining("、")), targetGroup.getPath()),
                changeGroupVO.getUserId());
    }

    @Override
    public Map<String, Set<String>> findExistsKeyword(CheckKeywordExistsRequestVO request) {
        List<String> keywordList = request.getKeywordList();
        List<IntentCorpusPO> corpusList = intentCorpusService.findByBotIdAndKeywordList(request.getBotId(), keywordList);
        if (CollectionUtils.isEmpty(corpusList)) {
            return Collections.emptyMap();
        }
        Map<String, Set<String>> resultMap = new HashMap<>(keywordList.size());
        for (IntentCorpusPO corpus : corpusList) {
            String intentName = corpus.getName();
            for (String regex : corpus.getRegexList()) {
                if (resultMap.containsKey(regex) || keywordList.contains(regex)) {
                    resultMap.computeIfAbsent(regex, k -> new HashSet<>()).add(intentName);
                }
            }
        }
        return resultMap;
    }

    public Map<String, Set<String>> findExistsDesc(Long botId, List<String> descList) {
        List<IntentCorpusPO> corpusList = intentCorpusService.findByBotIdAndDescList(botId, descList);
        if (CollectionUtils.isEmpty(corpusList)) {
            return Collections.emptyMap();
        }
        Map<String, Set<String>> resultMap = new HashMap<>(descList.size());
        for (IntentCorpusPO corpus : corpusList) {
            String intentName = corpus.getName();
            for (String desc : corpus.getDescList()) {
                if (resultMap.containsKey(desc) || descList.contains(desc)) {
                    resultMap.computeIfAbsent(desc, k -> new HashSet<>()).add(intentName);
                }
            }
        }
        return resultMap;
    }

    @Override
    public void deleteKeyword(CheckKeywordExistsRequestVO request, Long userId) {
        List<String> keywordList = request.getKeywordList();
        Long botId = request.getBotId();
        List<IntentCorpusPO> corpusList = intentCorpusService.findByBotIdAndKeywordList(botId, keywordList);
        if (CollectionUtils.isEmpty(corpusList)) {
            return;
        }
        // 意图名称-被删除的关键词列表
        Map<String, List<String>> intentNameDeletedRegexListMap = new HashMap<>();
        for (IntentCorpusPO corpus : corpusList) {
            List<String> regexList = corpus.getRegexList();
            if (CollectionUtils.isEmpty(regexList)) {
                continue;
            }

            intentNameDeletedRegexListMap.put(corpus.getName(), ListUtils.intersection(regexList, keywordList));

            regexList.removeAll(keywordList);
            intentCorpusService.save(corpus);
        }
        // 日志
        intentNameDeletedRegexListMap.forEach((intentName, deletedRegexList) ->
                operationLogService.save(botId, OperationLogTypeEnum.INTENT, OperationLogResourceTypeEnum.SINGLE_INTENT,
                        String.format("编辑单个意图：原【%s】删除关键词：%s", intentName, String.join("、", deletedRegexList)), userId));
        // 更新Bot状态
        botService.updateAuditStatus(botId, AuditStatusEnum.DRAFT);
    }

    @Override
    public Map<String, String> getIntentIdNameMapWithDefault(Long botId) {
        Map<String, String> intentIdNameMap = new HashMap<>(MyCollectionUtils.listToMap(getAllByBotId(botId), IntentPO::getId, IntentPO::getName));
        intentIdNameMap.put(ApplicationConstant.DEFAULT_INTENT_ID, ApplicationConstant.DEFAULT_INTENT_ID);
        intentIdNameMap.put(ApplicationConstant.USER_SILENCE_INTENT_ID, ApplicationConstant.USER_SILENCE_INTENT_ID);
        intentIdNameMap.put(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID, ApplicationConstant.COLLECT_SUCCESS_INTENT_ID);
        intentIdNameMap.put(ApplicationConstant.COLLECT_FAILED_INTENT_ID, ApplicationConstant.COLLECT_FAILED_INTENT_ID);
        return intentIdNameMap;
    }
}
