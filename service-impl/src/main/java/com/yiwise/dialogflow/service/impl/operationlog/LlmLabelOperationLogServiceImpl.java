package com.yiwise.dialogflow.service.impl.operationlog;

import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import com.yiwise.dialogflow.entity.po.LlmLabelPO;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.operationlog.LlmLabelOperationLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class LlmLabelOperationLogServiceImpl implements LlmLabelOperationLogService {

    @Resource
    private OperationLogService operationLogService;

    @Override
    public void create(Long botId, String name, Long userId) {
        operationLogService.save(botId, OperationLogTypeEnum.LLM, OperationLogResourceTypeEnum.LLM_LABEL,
                String.format("创建大模型分类[%s]", name), userId);
    }

    private String prefix(String name) {
        return String.format("编辑大模型分类：原分类【%s】", name);
    }

    private String generateDescInfo(List<String> descList) {
        return String.join(";", descList);
    }

    @Override
    public void update(LlmLabelPO oldLabel, LlmLabelPO newLabel, Long userId) {
        List<String> logList = new ArrayList<>();
        if (!StringUtils.equals(oldLabel.getName(), newLabel.getName())) {
            logList.add(String.format("%s名称【%s】,修改为【%s】", prefix(oldLabel.getName()), oldLabel.getName(), newLabel.getName()));
        }
        String oldDescInfo = generateDescInfo(oldLabel.getDescList());
        String newDescInfo = generateDescInfo(newLabel.getDescList());
        if (!StringUtils.equals(oldDescInfo, newDescInfo)) {
            logList.add(String.format("%s描述【%s】,修改为【%s】", prefix(oldLabel.getName()), oldDescInfo, newDescInfo));
        }
        for (String log : logList) {
            operationLogService.save(oldLabel.getBotId(), OperationLogTypeEnum.LLM, OperationLogResourceTypeEnum.LLM_LABEL, log, userId);
        }
    }

    @Override
    public void delete(LlmLabelPO label, Long userId) {
        operationLogService.save(label.getBotId(), OperationLogTypeEnum.LLM, OperationLogResourceTypeEnum.LLM_LABEL,
                String.format("删除大模型分类[%s]", label.getName()), userId);
    }
}