package com.yiwise.dialogflow.service.impl.asrmodel;

import com.yiwise.dialogflow.engine.share.enums.AsrProviderEnum;
import com.yiwise.dialogflow.entity.enums.asr.AsrSlefLearningTrainStatusEnum;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrSelfLearningProviderRelationPO;
import com.yiwise.dialogflow.mapper.AsrSelfLearningProviderRelationPOMapper;
import com.yiwise.dialogflow.service.asrmodel.AsrSelfLearningProviderRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/2 14:33:34
 */
@Slf4j
@Service
public class AsrSelfLearningProviderRelationServiceImpl implements AsrSelfLearningProviderRelationService {
    @Resource
    private AsrSelfLearningProviderRelationPOMapper asrSelfLearningProviderRelationPOMapper;

    @Override
    public List<AsrSelfLearningProviderRelationPO> getByAsrSelfLearningId(Long asrSelfLearningId) {
        return asrSelfLearningProviderRelationPOMapper.getByAsrSelfLearningId(asrSelfLearningId);
    }

    @Override
    public AsrSelfLearningProviderRelationPO getByAsrSelfLearningIdAndProvider(Long asrSelfLearningId, AsrProviderEnum provider) {
        return asrSelfLearningProviderRelationPOMapper.getByAsrSelfLearningIdAndProvider(asrSelfLearningId, provider);
    }

    @Override
    public void add(Long asrSelfLearningId, AsrProviderEnum provider) {
        AsrSelfLearningProviderRelationPO asrSelfLearningProviderRelationPO = new AsrSelfLearningProviderRelationPO();
        asrSelfLearningProviderRelationPO.setAsrSelfLearningId(asrSelfLearningId);
        asrSelfLearningProviderRelationPO.setProvider(provider);
        asrSelfLearningProviderRelationPO.setTrainStatus(AsrSlefLearningTrainStatusEnum.NOT_TRAIN);
        asrSelfLearningProviderRelationPOMapper.insertSelective(asrSelfLearningProviderRelationPO);
    }

    @Override
    public void updateByCondition(Long asrSelfLearningId, String providerModelId, String datatId, AsrProviderEnum provider) {
        AsrSelfLearningProviderRelationPO asrSelfLearningProviderRelationPO = asrSelfLearningProviderRelationPOMapper.getByAsrSelfLearningIdAndProvider(asrSelfLearningId, provider);
        AsrSelfLearningProviderRelationPO updatePO = new AsrSelfLearningProviderRelationPO();
        updatePO.setAsrSelfLearningProviderRelationId(asrSelfLearningProviderRelationPO.getAsrSelfLearningProviderRelationId());
        boolean needUpdate = false;
        if (StringUtils.isNotEmpty(providerModelId)) {
            updatePO.setProviderModelId(providerModelId);
            needUpdate = true;
        }
        if (StringUtils.isNotEmpty(datatId)) {
            updatePO.setDataId(datatId);
            needUpdate = true;
        }
        if (needUpdate) {
            asrSelfLearningProviderRelationPOMapper.updateByPrimaryKeySelective(updatePO);
        } else {
            log.warn("updateByCondition no need update, asrSelfLearningId={}, provider={}", asrSelfLearningId, provider);
        }
    }

    @Override
    public void updateTrainStatusById(Long asrSelfLearningProviderRelationId, AsrSlefLearningTrainStatusEnum status) {
        AsrSelfLearningProviderRelationPO updatePO = new AsrSelfLearningProviderRelationPO();
        updatePO.setTrainStatus(status);
        updatePO.setAsrSelfLearningProviderRelationId(asrSelfLearningProviderRelationId);
        asrSelfLearningProviderRelationPOMapper.updateByPrimaryKeySelective(updatePO);
    }

    @Override
    public void delete(AsrSelfLearningProviderRelationPO asrSelfLearningProviderRelationPO) {
        asrSelfLearningProviderRelationPOMapper.delete(asrSelfLearningProviderRelationPO);
    }
}