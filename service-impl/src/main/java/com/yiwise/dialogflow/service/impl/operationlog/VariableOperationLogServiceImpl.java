package com.yiwise.dialogflow.service.impl.operationlog;

import com.google.common.collect.Lists;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.customer.data.platform.rpc.api.service.dto.CustomerAttributeMetaDataDTO;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.VariablePO;
import com.yiwise.dialogflow.service.BotRefService;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.operationlog.VariableOperationLogService;
import com.yiwise.dialogflow.service.remote.customer.CustomerAttributeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2023/7/11
 */
@Slf4j
@Service
public class VariableOperationLogServiceImpl implements VariableOperationLogService {

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private BotRefService botRefService;

    @Resource
    private CustomerAttributeService customerAttributeService;

    @Override
    public void addVar(VariablePO variable) {
        addVar(variable.getBotId(), variable.getType(), variable.getName(), variable.getUpdateUserId());
    }

    private void addVar(Long botId, VariableTypeEnum variableType, String varName, Long userId) {
        String detail = buildSingleAddLog(variableType, varName);
        operationLogService.save(botId, OperationLogTypeEnum.VARIABLE, OperationLogResourceTypeEnum.VARIABLE, detail, userId);
    }

    private String buildSingleAddLog(VariableTypeEnum variableType, String varName) {
        return String.format("新增%s【%s】", variableType.getDesc(), varName);
    }

    @Override
    public void batchCreateVar(Long botId, List<String> varNameList, Long userId) {
        if (CollectionUtils.isEmpty(varNameList)) {
            return;
        }
        List<String> detailList = varNameList.stream().map(varName -> buildSingleAddLog(VariableTypeEnum.CUSTOM, varName)).collect(toList());
        operationLogService.save(botId, OperationLogTypeEnum.VARIABLE, OperationLogResourceTypeEnum.VARIABLE, detailList, userId);
    }

    private String generateSaveInfo(Boolean enableSave, Long customerAttributeId, Map<Long, String> customerAttributeIdNameMap) {
        if (!BooleanUtils.isTrue(enableSave)) {
            return "未启用";
        }
        return "保存字段:" + customerAttributeIdNameMap.getOrDefault(customerAttributeId, "");
    }

    @Override
    public void updateVar(VariablePO oldVar, VariablePO newVar) {
        List<String> detailList = Lists.newArrayList();
        // 变量名称修改
        if (!StringUtils.equals(oldVar.getName(), newVar.getName())) {
            detailList.add(String.format("编辑%s:原【%s】变量名称修改为【%s】", oldVar.getType().getDesc(), oldVar.getName(), newVar.getName()));
        }
        // 变量说明修改
        if (!StringUtils.equals(oldVar.getDesc(), newVar.getDesc())) {
            detailList.add(String.format("编辑%s:原【%s】变量说明【%s】,修改为【%s】", oldVar.getType().getDesc(), oldVar.getName(), oldVar.getDesc(), newVar.getDesc()));
        }
        // 变量类型修改
        if (!Objects.equals(oldVar.getType(), newVar.getType())) {
            detailList.add(String.format("编辑%s:原【%s】变量类型【%s】,修改为【%s】", oldVar.getType().getDesc(), oldVar.getName(), oldVar.getType().getDesc(), newVar.getType().getDesc()));
        }
        // 动态变量保存字段修改
        if (VariableTypeEnum.isDynamicVariable(oldVar.getType()) && VariableTypeEnum.isDynamicVariable(newVar.getType())) {
            if (!Objects.equals(BooleanUtils.isTrue(oldVar.getEnableSave()), BooleanUtils.isTrue(newVar.getEnableSave()))
                    || !Objects.equals(oldVar.getCustomerAttributeId(), newVar.getCustomerAttributeId())) {

                List<Long> customerAttributeIdList = Stream.of(oldVar.getCustomerAttributeId(), newVar.getCustomerAttributeId())
                        .filter(Objects::nonNull).distinct().collect(toList());
                Long tenantId = botRefService.getTenantIdByBotId(newVar.getBotId());
                // <客户属性id,name>
                Map<Long, String> customerAttributeIdNameMap = MyCollectionUtils.listToConvertMap(customerAttributeService.getCustomerAttributeByIdList(tenantId, customerAttributeIdList),
                        CustomerAttributeMetaDataDTO::getMcCustomerAttributeMetaDataId, CustomerAttributeMetaDataDTO::getAttributeName);

                detailList.add(String.format("编辑%s:原【%s】采集信息保存至客户属性字段【%s】,修改为【%s】", oldVar.getType().getDesc(), oldVar.getName(),
                        generateSaveInfo(oldVar.getEnableSave(), oldVar.getCustomerAttributeId(), customerAttributeIdNameMap),
                        generateSaveInfo(newVar.getEnableSave(), newVar.getCustomerAttributeId(), customerAttributeIdNameMap)
                ));
            }
            if (!Objects.equals(oldVar.getSaveMode(), newVar.getSaveMode())) {
                detailList.add(String.format("编辑%s:原【%s】对话中多次采集【%s】,修改为【%s】", oldVar.getType().getDesc(), oldVar.getName(), oldVar.getSaveMode().getDesc(), newVar.getSaveMode().getDesc()));
            }
            if (!Objects.equals(oldVar.getEnableDeduplicate(), newVar.getEnableDeduplicate())) {
                detailList.add(String.format("编辑%s:原【%s】相同实体值去重【%s】", oldVar.getType().getDesc(), oldVar.getName(), newVar.getEnableDeduplicate() ? "启用" : "停用"));
            }
        }
        // 变量读法修改
        if (!Objects.equals(oldVar.getInterpretType(), newVar.getInterpretType())) {
            detailList.add(String.format("编辑%s:原【%s】变量读法【%s】,修改为【%s】", oldVar.getType().getDesc(), oldVar.getName(), oldVar.getInterpretType().getDesc(), newVar.getInterpretType().getDesc()));
        }
        // 模板变量默认值修改
        if (VariableTypeEnum.isTemplateVariable(oldVar.getType()) && VariableTypeEnum.isTemplateVariable(newVar.getType())) {
            if (!Objects.equals(oldVar.getTemplateVariableConfigType(), newVar.getTemplateVariableConfigType())) {
                detailList.add(String.format("编辑%s: 原【%s】变量配置【%s】,修改为【%s】", oldVar.getType().getDesc(), oldVar.getName(), oldVar.getTemplateVariableConfigType().getDesc(), newVar.getTemplateVariableConfigType().getDesc()));
            }
            if (TemplateVariableConfigTypeEnum.DEFAULT_VALUE.equals(newVar.getTemplateVariableConfigType())) {
                if (!StringUtils.equals(oldVar.getDefaultValue(), newVar.getDefaultValue())) {
                    detailList.add(String.format("编辑%s:原【%s】默认值【%s】,修改为【%s】", oldVar.getType().getDesc(), oldVar.getName(), oldVar.getDefaultValue(), newVar.getDefaultValue()));
                }
            }
            if (TemplateVariableConfigTypeEnum.PROMPT.equals(newVar.getTemplateVariableConfigType())) {
                if (!StringUtils.equals(oldVar. getTemplateSentence(), newVar.getTemplateSentence())) {
                    detailList.add(String.format("编辑%s:原【%s】模板句【%s】,修改为【%s】", oldVar.getType().getDesc(), oldVar.getName(), oldVar.getTemplateSentence(), newVar.getTemplateSentence()));
                }
                if (!StringUtils.equals(oldVar.getPrompt(), newVar.getPrompt())) {
                    detailList.add(String.format("编辑%s:原【%s】提示词【%s】,修改为【%s】", oldVar.getType().getDesc(), oldVar.getName(), oldVar.getPrompt(), newVar.getPrompt()));
                }
            }
        }
        // AI自动信息对齐
        if (!Objects.equals(BooleanUtils.isTrue(oldVar.getEnableAutoAlign()), BooleanUtils.isTrue(newVar.getEnableAutoAlign()))) {
            detailList.add(String.format("编辑%s:原【%s】AI自动信息对齐【%s】", oldVar.getType().getDesc(), oldVar.getName(), BooleanUtils.isTrue(newVar.getEnableAutoAlign()) ? "开启" : "关闭"));
        }
        // 模板变量字段
        if (VariableTypeEnum.isTemplateVariable(newVar.getType())) {
            if (!Objects.equals(oldVar.getTemplateVariableConfigType(), newVar.getTemplateVariableConfigType())) {
                detailList.add(String.format("编辑%s:原【%s】配置方式【%s】,修改为【%s】", oldVar.getType().getDesc(), oldVar.getName(), oldVar.getTemplateVariableConfigType().getDesc(), newVar.getTemplateVariableConfigType().getDesc()));
            } else {
                if (!StringUtils.equals(oldVar.getPrompt(), newVar.getPrompt())) {
                    detailList.add(String.format("编辑%s:原【%s】提示词【%s】,修改为【%s】", oldVar.getType().getDesc(), oldVar.getName(), oldVar.getPrompt(), newVar.getPrompt()));
                }
                if (!StringUtils.equals(oldVar.getTemplateSentence(), newVar.getTemplateSentence())) {
                    detailList.add(String.format("编辑%s:原【%s】提示词【%s】,修改为【%s】", oldVar.getType().getDesc(), oldVar.getName(), oldVar.getTemplateSentence(), newVar.getTemplateSentence()));
                }
            }
        }

        if (!Objects.equals(newVar.getIsOptional(), oldVar.getIsOptional())) {
            detailList.add(String.format("编辑%s:原【%s】是否必填修改为【%s】",
                    oldVar.getType().getDesc(), oldVar.getName(), BooleanUtils.isTrue(newVar.getIsOptional()) ? "非必填" : "必填"));
        }

        for (String detail : detailList) {
            operationLogService.save(oldVar.getBotId(), OperationLogTypeEnum.VARIABLE, OperationLogResourceTypeEnum.VARIABLE, detail, newVar.getUpdateUserId());
        }

    }

    @Override
    public void deleteVar(VariablePO variable, Long curUserId) {
        String detail = String.format("删除%s【%s】", variable.getType().getDesc(), variable.getName());
        operationLogService.save(variable.getBotId(), OperationLogTypeEnum.VARIABLE, OperationLogResourceTypeEnum.VARIABLE, detail, curUserId);
    }

    @Override
    public void batchDeleteVar(Long botId, List<VariablePO> varList, Long curUserId) {
        if (CollectionUtils.isEmpty(varList)) {
            return;
        }
        operationLogService.save(botId, OperationLogTypeEnum.VARIABLE, OperationLogResourceTypeEnum.VARIABLE,
                String.format("批量删除变量%s", varList.stream().map(s -> "【" + s.getName() + "】").collect(Collectors.joining("、"))),
                curUserId);
    }

    @Override
    public void syncVariable(BotPO srcBot, List<VariablePO> syncVarList, List<BotPO> targetBotList, SyncModeEnum syncMode, Long userId) {
        if (CollectionUtils.isEmpty(syncVarList) || CollectionUtils.isEmpty(targetBotList)) {
            return;
        }

        String syncType = SyncModeEnum.COVER == syncMode ? "覆盖" : "跳过";
        String targetBotNames = targetBotList.stream().map(BotPO::getName).collect(Collectors.joining(","));
        String log = String.format("源 bot: %s; 变量列表: 【%s】; 变量名称及类型相同处理: %s; 目标 bot: %s",
                srcBot.getName(),
                syncVarList.stream().map(VariablePO::getName).collect(Collectors.joining(", ")),
                syncType,
                targetBotNames
                );
        operationLogService.save(srcBot.getBotId(), OperationLogTypeEnum.VARIABLE, OperationLogResourceTypeEnum.VARIABLE,
                log,
                userId);
        for (BotPO targetBot : targetBotList) {
            operationLogService.save(targetBot.getBotId(), OperationLogTypeEnum.VARIABLE, OperationLogResourceTypeEnum.VARIABLE,
                    log,
                    userId);
        }
    }
}
