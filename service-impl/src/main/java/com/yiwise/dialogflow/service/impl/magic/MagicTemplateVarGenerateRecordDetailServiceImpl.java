package com.yiwise.dialogflow.service.impl.magic;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.po.magic.MagicTemplateVarGenerateRecordDetailPO;
import com.yiwise.dialogflow.entity.po.magic.MagicTemplateVarGenerateRecordPO;
import com.yiwise.dialogflow.entity.vo.magic.MagicTemplateVarGenerateRecordQueryVO;
import com.yiwise.dialogflow.service.magic.MagicTemplateVarGenerateRecordDetailService;
import com.yiwise.dialogflow.service.magic.MagicTemplateVarGenerateRecordService;


@Service
public class MagicTemplateVarGenerateRecordDetailServiceImpl implements MagicTemplateVarGenerateRecordDetailService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private MagicTemplateVarGenerateRecordService magicTemplateVarGenerateRecordService;

    @Override
    public void create(MagicTemplateVarGenerateRecordDetailPO detail) {
        if (detail == null) {
            return;
        }
        mongoTemplate.save(detail, MagicTemplateVarGenerateRecordDetailPO.COLLECTION_NAME);
    }

    @Override
    public void batchCreate(List<MagicTemplateVarGenerateRecordDetailPO> details) {
        if (CollectionUtils.isNotEmpty(details)) {
            mongoTemplate.insert(details, MagicTemplateVarGenerateRecordDetailPO.COLLECTION_NAME);
        }
    }

    @Override
    public MagicTemplateVarGenerateRecordDetailPO findById(Long botId, String recordId, String detailId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId));
        query.addCriteria(Criteria.where("recordId").is(recordId));
        query.addCriteria(Criteria.where("_id").is(detailId));
        return mongoTemplate.findOne(query, MagicTemplateVarGenerateRecordDetailPO.class, MagicTemplateVarGenerateRecordDetailPO.COLLECTION_NAME);
    }

    @Override
    public List<MagicTemplateVarGenerateRecordDetailPO> findByRecordId(Long botId,String recordId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("recordId").is(recordId));
        query.addCriteria(Criteria.where("botId").is(botId));

        return mongoTemplate.find(query, MagicTemplateVarGenerateRecordDetailPO.class, MagicTemplateVarGenerateRecordDetailPO.COLLECTION_NAME);
    }

    @Override
    public PageResultObject<MagicTemplateVarGenerateRecordDetailPO> queryLastListByCondition(MagicTemplateVarGenerateRecordQueryVO query) {
        
        // 需要查询最后一条 record 信息
        MagicTemplateVarGenerateRecordPO record = magicTemplateVarGenerateRecordService.getLastRecordByBotId(query.getBotId());

        if (record == null) {
            return PageResultObject.of(Collections.emptyList());
        }

        Query mongoQuery = new Query();
        mongoQuery.addCriteria(Criteria.where("botId").is(query.getBotId()));
        mongoQuery.addCriteria(Criteria.where("recordId").is(record.getId()));

        if (StringUtils.isNotBlank(query.getSearch())) {
            mongoQuery.addCriteria(Criteria.where("variableName").regex(query.getSearch()));
        }
    
        // 增加分页信息
        


        List<MagicTemplateVarGenerateRecordDetailPO> details = mongoTemplate.find(mongoQuery, MagicTemplateVarGenerateRecordDetailPO.class, MagicTemplateVarGenerateRecordDetailPO.COLLECTION_NAME);

        return null;
//        return new PageResultObject<>(details, query.getPage());
//        return mongoTemplate.find(query, MagicTemplateVarGenerateRecordDetailPO.class, MagicTemplateVarGenerateRecordDetailPO.COLLECTION_NAME);
    }
} 