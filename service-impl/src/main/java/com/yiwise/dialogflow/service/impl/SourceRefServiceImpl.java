package com.yiwise.dialogflow.service.impl;

import com.google.common.collect.Lists;
import com.yiwise.dialogflow.entity.bo.ResourceCopyReferenceMappingBO;
import com.yiwise.dialogflow.entity.bo.SourceRefBO;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.enums.IntentRefTypeEnum;
import com.yiwise.dialogflow.entity.enums.SourceTypeEnum;
import com.yiwise.dialogflow.entity.po.SourceRefPO;
import com.yiwise.dialogflow.mapper.SourceRefPOMapper;
import com.yiwise.dialogflow.service.RobotResourceService;
import com.yiwise.dialogflow.service.SourceRefService;
import com.yiwise.dialogflow.service.impl.entitycollect.EntityServiceImpl;
import com.yiwise.dialogflow.service.impl.intent.IntentServiceImpl;
import com.yiwise.dialogflow.service.impl.llm.LlmStepConfigServiceImpl;
import com.yiwise.middleware.mysql.service.impl.BasicServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/7/20
 * @class <code>SourceRefServiceImpl</code>
 * @see
 * @since JDK1.8
 */
@Slf4j
@Service
public class SourceRefServiceImpl extends BasicServiceImpl<SourceRefPO> implements SourceRefService, RobotResourceService {

    @Resource
    private SourceRefPOMapper sourceRefPOMapper;

    @Override
    public void deleteSourceByParentRefId(Long botId, String parentRefId, IntentRefTypeEnum parentRefType) {
        sourceRefPOMapper.deleteSourceByParentRefId(botId, parentRefId, parentRefType.getCode());
    }

    @Override
    public void deleteSourceByParentRefIdList(Long botId, List<String> parentRefIdList, IntentRefTypeEnum parentRefType) {
        if (CollectionUtils.isEmpty(parentRefIdList)) {
            return;
        }
        sourceRefPOMapper.deleteSourceByParentRefIdList(botId, parentRefIdList, parentRefType.getCode());
    }

    @Override
    public SourceRefPO addSourceRef(SourceRefPO sourceRefPO) {
        saveNotNull(sourceRefPO);
        return sourceRefPO;
    }

    @Override
    public void saveSourceRef(SourceRefBO sourceRefBO) {
        if (CollectionUtils.isEmpty(sourceRefBO.getSourceIdSet())) {
            return;
        }
        sourceRefPOMapper.batchAddSourceRef(sourceRefBO, sourceRefBO.getSourceType().getCode(),
                sourceRefBO.getRefType().getCode(), sourceRefBO.getParentRefType() == null ? null : sourceRefBO.getParentRefType().getCode());
    }

    @Override
    public void deleteSourceByRefId(Long botId, String id) {
        sourceRefPOMapper.deleteSourceByRefId(botId, id);
    }

    @Override
    public void deleteSourceByRefIds(Long botId, List<String> refIdList) {
        if (CollectionUtils.isEmpty(refIdList)) {
            return;
        }
        sourceRefPOMapper.deleteSourceByRefIds(botId, refIdList);
    }

    @Override
    public void deleteSourceByRefIds(Long botId, List<String> refIdList, SourceTypeEnum sourceType) {
        if (CollectionUtils.isEmpty(refIdList)) {
            return;
        }
        sourceRefPOMapper.deleteByRefIdsAndSourceType(botId, refIdList, sourceType.getCode());
    }

    @Override
    public void deleteSourceByRefIdList(Long botId, List<String> refIdList, IntentRefTypeEnum refType) {
        if (CollectionUtils.isEmpty(refIdList)) {
            return;
        }
        sourceRefPOMapper.deleteSourceByRefIdList(botId, refIdList, refType.getCode());
    }

    @Override
    public List<SourceRefPO> getIntentByRefIdList(Long botId, List<String> refIdList, IntentRefTypeEnum refType) {
        if (CollectionUtils.isEmpty(refIdList)) {
            return new ArrayList<>();
        }
        return sourceRefPOMapper.getIntentByRefIdList(botId, refIdList, refType.getCode());
    }

    @Override
    public List<SourceRefPO> getBySourceIdList(Long botId, List<String> sourceIdList, SourceTypeEnum sourceType) {
        if (CollectionUtils.isEmpty(sourceIdList)) {
            return new ArrayList<>();
        }
        return sourceRefPOMapper.getBySourceIdList(botId, sourceIdList, sourceType.getCode());
    }

    @Override
    public List<SourceRefPO> getListBySourceType(Long botId, SourceTypeEnum sourceType) {
        return sourceRefPOMapper.getListBySourceTypeAndRefType(botId, sourceType.getCode(), null);
    }

    @Override
    public List<SourceRefPO> getListBySourceTypeAndRefType(Long botId, SourceTypeEnum sourceType, IntentRefTypeEnum refType) {
        return sourceRefPOMapper.getListBySourceTypeAndRefType(botId, sourceType.getCode(), refType.getCode());
    }

    @Override
    public void deleteBySourceIdList(Long botId, Collection<String> sourceIdList, IntentRefTypeEnum refType) {
        if (CollectionUtils.isEmpty(sourceIdList)) {
            return;
        }
        sourceRefPOMapper.deleteBySourceIdList(botId, sourceIdList, refType);
    }

    @Override
    public void batchAddSourceRef(List<SourceRefPO> handleList) {
        if (CollectionUtils.isEmpty(handleList)) {
            return;
        }
        sourceRefPOMapper.batchAddSourceRefList(handleList);
    }

    @Override
    public void deleteByRefType(Long botId, IntentRefTypeEnum refType) {
        if (Objects.isNull(refType)) {
            return;
        }
        sourceRefPOMapper.deleteByRefType(botId, refType.getCode());
    }

    @Override
    public void deleteSourceByRefIdAndSourceTypeAndRefType(Long botId, String refId, SourceTypeEnum sourceType, IntentRefTypeEnum refType) {
        if (Objects.isNull(refId) || Objects.isNull(refType) || Objects.isNull(sourceType)) {
            return;
        }
        sourceRefPOMapper.deleteSourceByRefIdAndSourceTypeList(botId, Collections.singletonList(refId), refType.getCode(), sourceType.getCode());
    }

    /**
     * 查询 botIdList  下所有变量依赖数据(可能包含脏数据)
     * @param botIdList
     * @return
     */
    @Override
    public List<SourceRefPO> getVariableRefListByBotIdList(List<Long> botIdList) {
        return getListByBotIdListAndSourceType(botIdList, SourceTypeEnum.VARIABLE);
    }

    @Override
    public void updateRefLabelByRefId(Long botId, String variableId, String newVariableName) {
        sourceRefPOMapper.updateRefLabelByRefId(botId, variableId, newVariableName);
    }

    private List<SourceRefPO> getListByBotIdListAndSourceType(List<Long> botIdList, SourceTypeEnum sourceType) {
        if (CollectionUtils.isEmpty(botIdList) || Objects.isNull(sourceType)) {
            return new ArrayList<>();
        }
        return sourceRefPOMapper.getListByBotIdListAndSourceType(botIdList, sourceType.getCode());
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        //获取源bot下所有资源关联信息
        List<SourceRefPO> sourceRefList = sourceRefPOMapper.getAllByBotId(context.getSrcBotId());
        context.getSnapshot().setSourceRefList(sourceRefList);
    }

    @Override
    public void validateResource(RobotResourceContext context) {
        RobotResourceService.super.validateResource(context);
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        if (!context.isCopy()) {
            return;
        }
        List<SourceRefPO> sourceRefList = context.getSnapshot().getSourceRefList();
        ResourceCopyReferenceMappingBO mapping = context.getResourceCopyReferenceMapping();
        Map<String, String> knowledgeIdMapping = mapping.getKnowledgeIdMapping();
        Map<String, String> stepIdMapping = mapping.getStepIdMapping();
        Map<String, String> nodeIdMapping = mapping.getNodeIdMapping();
        Map<String, String> variableIdMapping = mapping.getVariableIdMapping();
        Map<String, String> intentIdMapping = mapping.getIntentIdMapping();
        Map<String, String> specialIdMapping = mapping.getSpecialAnswerIdMapping();

        if (context.isCopy() && !context.isImport()) {
            for (SourceRefPO sourceRefPO : sourceRefList) {
                String oldSourceId = sourceRefPO.getSourceId();
                String oldRefId = sourceRefPO.getRefId();
                sourceRefPO.setSourceRefId(null);
                sourceRefPO.setBotId(context.getTargetBotId());
                switch (sourceRefPO.getSourceType()) {
                    case INTENT:
                        sourceRefPO.setSourceId(intentIdMapping.get(sourceRefPO.getSourceId()));
                        break;
                    case VARIABLE:
                        sourceRefPO.setSourceId(variableIdMapping.get(sourceRefPO.getSourceId()));
                        break;
                    case ENTITY:
                        sourceRefPO.setSourceId(mapping.getEntityIdMapping().get(sourceRefPO.getSourceId()));
                        break;
                }
                switch (sourceRefPO.getRefType()) {
                    case STEP:
                        sourceRefPO.setRefId(stepIdMapping.get(sourceRefPO.getRefId()));
                        break;
                    case NODE:
                    case JUDGE_NODE_BRANCH:
                    case ORIGIN_INPUT:
                        sourceRefPO.setRefId(nodeIdMapping.get(sourceRefPO.getRefId()));
                        sourceRefPO.setParentRefId(stepIdMapping.get(sourceRefPO.getParentRefId()));
                        break;
                    case KNOWLEDGE:
                        sourceRefPO.setRefId(knowledgeIdMapping.get(sourceRefPO.getRefId()));
                        break;
                    case SPECIAL_ANSWER:
                    case SPECIAL_ANSWER_EXCLUDE:
                        sourceRefPO.setRefId(specialIdMapping.get(sourceRefPO.getRefId()));
                        break;
                    case COMPOSITE_INTENT:
                    case SINGLE_INTENT:
                        sourceRefPO.setRefId(intentIdMapping.get(sourceRefPO.getRefId()));
                        break;
                    case ENTITY:
                        sourceRefPO.setRefId(mapping.getEntityIdMapping().get(sourceRefPO.getRefId()));
                        break;
                    case GLOBAL_ENTITY_COLLECT:
                        sourceRefPO.setRefId(mapping.getEntityConfigIdMapping().get(sourceRefPO.getRefId()));
                        break;
                    case LLM_STEP_CONFIG:
                        sourceRefPO.setRefId(mapping.getLlmStepConfigIdMapping().get(sourceRefPO.getRefId()));
                        sourceRefPO.setParentRefId(mapping.getStepIdMapping().get(sourceRefPO.getParentRefId()));
                        break;
                    case TEMPLATE_VARIABLE:
                        sourceRefPO.setRefId(mapping.getVariableIdMapping().get(sourceRefPO.getRefId()));
                        break;
                    default:
                        log.warn("[LogHub_Warn] 未处理的引用类型:{}", sourceRefPO.getSourceType());
                        break;
                }
                if (StringUtils.isBlank(sourceRefPO.getSourceId())
                        || StringUtils.isBlank(sourceRefPO.getRefId())) {
                   log.warn("sourceRefPO is invalid, sourceRefPO:{}, oldSourceId={}, oldRefId={}", sourceRefPO, oldSourceId, oldRefId);
                } else {
                    try {
                        addSourceRef(sourceRefPO);
                    } catch (Exception e) {
                        log.warn("add sourceRefPO failed, sourceRefPO:{},  oldSourceId={}, oldRefId={}", sourceRefPO, oldSourceId, oldRefId, e);
                    }
                }
            }
        }
    }

    @Override
    public List<Class<? extends RobotResourceService>> dependsOn() {
        return Lists.newArrayList(StepServiceImpl.class, StepNodeServiceImpl.class, IntentServiceImpl.class,
                KnowledgeServiceImpl.class, SpecialAnswerConfigServiceImpl.class, EntityServiceImpl.class,
                VariableServiceImpl.class, EntityCollectConfigServiceImpl.class, LlmStepConfigServiceImpl.class);
    }
}
