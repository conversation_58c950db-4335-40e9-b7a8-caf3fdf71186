package com.yiwise.dialogflow.service.impl;

import com.github.pagehelper.PageHelper;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.dto.response.VarValueTtsReplaceDTO;
import com.yiwise.dialogflow.common.OssKeyCenter;
import com.yiwise.dialogflow.entity.po.VarValueTtsReplacePO;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.entity.query.VarValueTtsReplaceDeleteVO;
import com.yiwise.dialogflow.entity.query.VarValueTtsReplaceExportRequest;
import com.yiwise.dialogflow.entity.query.VarValueTtsReplaceQueryVO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.VarValueTtsReplaceVO;
import com.yiwise.dialogflow.entity.vo.audio.TtsComposeResultVO;
import com.yiwise.dialogflow.mapper.VarValueTtsReplacePOMapper;
import com.yiwise.dialogflow.service.VarValueTtsReplaceService;
import com.yiwise.dialogflow.service.remote.TtsComposeService;
import com.yiwise.dialogflow.service.remote.UserService;
import com.yiwise.middleware.mysql.service.impl.BasicServiceImpl;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import com.yiwise.middleware.objectstorage.serializer.AddOssPrefixSerializer;
import com.yiwise.middleware.tts.enums.TtsProviderEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class VarValueTtsReplaceServiceImpl extends BasicServiceImpl<VarValueTtsReplacePO> implements VarValueTtsReplaceService {

    private static final String 变量值 = "变量值";
    private static final String 变量名 = "变量名";
    private static final String 读音值 = "读音值";
    private static final String TTS供应商 = "TTS供应商";

    @Resource
    private VarValueTtsReplacePOMapper varValueTtsReplacePOMapper;

    @Resource
    private UserService userService;

    @Resource
    private TtsComposeService ttsComposeService;

    @Resource
    private ObjectStorageHelper objectStorageHelper;

    @Override
    public PageResultObject<VarValueTtsReplaceVO> queryByCondition(VarValueTtsReplaceQueryVO query) {
        if (Objects.isNull(query.getTenantId())) {
            return PageResultObject.of(Collections.emptyList());
        }
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<VarValueTtsReplaceVO> list = varValueTtsReplacePOMapper.queryByCondition(query);
        List<Long> userIdList = list.stream()
                .map(VarValueTtsReplaceVO::getUpdateUserId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        List<UserPO> userList = userService.getUserByIdList(userIdList);
        Map<Long, String> userIdNameMap = MyCollectionUtils.listToConvertMap(userList, UserPO::getUserId, UserPO::getName);

        for (VarValueTtsReplaceVO vo : list) {
            vo.setUpdateUserName(userIdNameMap.getOrDefault(vo.getUpdateUserId(), "--"));
            if (Objects.nonNull(vo.getTtsProvider())) {
                vo.setTtsProviderName(vo.getTtsProvider().getDesc());
            }
        }
        return PageResultObject.of(list);
    }

    @Override
    public VarValueTtsReplacePO create(VarValueTtsReplacePO create, Long userId) {
        validate(create);
        VarValueTtsReplacePO exist = queryByVariableNameAndValue(create.getTenantId(), create.getTtsProvider(), create.getVarName(), create.getVarValue(), null);
        if (Objects.nonNull(exist)) {
            throw new ComException(ComErrorCode.REQUEST_PARAM_ERROR, "变量名和变量值已存在");
        }
        create.setCreateUserId(userId);
        create.setUpdateUserId(userId);
        saveNotNull(create);
        return create;
    }

    private void validate(VarValueTtsReplacePO po) {
        if (Objects.isNull(po.getTenantId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "租户id不能为空");
        }
        if (StringUtils.isBlank(po.getReplacement())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "读音值不能为空");
        }
        if (StringUtils.isBlank(po.getVarName())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "变量名不能为空");
        }
        if (StringUtils.isBlank(po.getVarValue())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "变量值不能为空");
        }
        if (Objects.isNull(po.getTtsProvider())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "TTS提供商不能为空");
        }
    }

    @Override
    public VarValueTtsReplacePO update(VarValueTtsReplacePO update, Long userId) {
        validate(update);
        if (Objects.isNull(update.getVarValueTtsReplaceId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "id不能为空");
        }

        VarValueTtsReplacePO exist = queryByVariableNameAndValue(update.getTenantId(), update.getTtsProvider(), update.getVarName(), update.getVarValue(), update.getVarValueTtsReplaceId());
        if (Objects.nonNull(exist)) {
            throw new ComException(ComErrorCode.REQUEST_PARAM_ERROR, "变量名和变量值已存在");
        }

        update.setUpdateUserId(userId);
        updateNotNull(update);

        return update;
    }

    @Override
    public void deleteByIdList(Long tenantId, List<Long> idList, Long userId) {
        if (CollectionUtils.isEmpty(idList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "id列表不能为空");
        }
        if (Objects.isNull(tenantId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "租户id不能为空");
        }
        varValueTtsReplacePOMapper.deleteByIdList(tenantId, idList, userId);
    }

    @Override
    public void deleteByTenantId(Long tenantId, Long userId) {
        if (Objects.isNull(tenantId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "租户id不能为空");
        }
        varValueTtsReplacePOMapper.deleteByTenantId(tenantId, userId);
    }

    @Override
    public void importFromExcel(Long tenantId, Long userId, MultipartFile file) {
        if (Objects.isNull(tenantId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "租户id不能为空");
        }
        if (file == null) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "文件不能为空");
        }

        try {

            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            // 读取第一个sheet
            Sheet sheet = workbook.getSheetAt(0);
            // 表头在第一行
            Row headerRow = sheet.getRow(0);

            // 从 excel 中读取内容

            List<String> headerNameList = new ArrayList<>();
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                headerNameList.add(getCellStringValue(headerRow.getCell(i)));
            }
            log.info("headerNameList:{}", headerNameList);

            // 读取表头
            Map<String, Integer> titleIndexMap = new HashMap<>();
            for (int i = 0; i < headerNameList.size(); i++) {
                titleIndexMap.put(headerNameList.get(i), i);
            }
            // 校验表头
            // 变量名
            // 变量值
            // 读音值
            if (!titleIndexMap.containsKey(变量名)) {
                throw new ComException(ComErrorCode.REQUEST_PARAM_ERROR, "上传文件格式不对，请下载标准模板");
            }
            if (!titleIndexMap.containsKey(TTS供应商)) {
                throw new ComException(ComErrorCode.REQUEST_PARAM_ERROR, "上传文件格式不对，请下载标准模板");
            }
            if (!titleIndexMap.containsKey(变量值)) {
                throw new ComException(ComErrorCode.REQUEST_PARAM_ERROR, "上传文件格式不对，请下载标准模板");
            }
            if (!titleIndexMap.containsKey(读音值)) {
                throw new ComException(ComErrorCode.REQUEST_PARAM_ERROR, "上传文件格式不对，请下载标准模板");
            }

            int lastRowNum = sheet.getLastRowNum();
            if (lastRowNum == 0) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "数据不能为空");
            }
            if (lastRowNum > 10000) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "单次导入不能超过10000条");
            }

            List<List<String>> dataList = new ArrayList<>();
            for (int i = 1; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (Objects.isNull(row)) {
                    continue;
                }

                List<String> colList = new ArrayList<>();
                for (int j = 0; j < headerNameList.size(); j++) {
                    Cell cell = row.getCell(j);
                    String celValue = getCellStringValue(cell);
                    colList.add(celValue);
                }
                dataList.add(colList);
            }

            // 读取数据
            List<VarValueTtsReplacePO> importList = new ArrayList<>();

            Map<String, TtsProviderEnum> providerEnumMap = new HashMap<>();
            providerEnumMap.put(TtsProviderEnum.ALI.getDesc(), TtsProviderEnum.ALI);
            providerEnumMap.put(TtsProviderEnum.MICROSOFT.getDesc(), TtsProviderEnum.MICROSOFT);
            providerEnumMap.put(TtsProviderEnum.YIWISE3.getDesc(), TtsProviderEnum.YIWISE3);
            for (int i = 0; i < dataList.size(); i++) {
                VarValueTtsReplacePO po = new VarValueTtsReplacePO();
                try {
                    List<String> row = dataList.get(i);

                    po.setTenantId(tenantId);
                    po.setUpdateUserId(userId);

                    po.setVarName(getValue(row, 变量名, titleIndexMap));
                    po.setVarValue(getValue(row, 变量值, titleIndexMap));
                    po.setReplacement(getValue(row, 读音值, titleIndexMap));
                    String providerDesc = getValue(row, TTS供应商, titleIndexMap);
                    if (StringUtils.isBlank(providerDesc)) {
                        throw new ComException(ComErrorCode.VALIDATE_ERROR, "TTS供应商不能为空");
                    }
                    if (!providerEnumMap.containsKey(providerDesc)) {
                        throw new ComException(ComErrorCode.VALIDATE_ERROR, "仅支持" + providerEnumMap.keySet() + "供应商");
                    }
                    po.setTtsProvider(providerEnumMap.get(providerDesc));

                    validate(po);
                    importList.add(po);
                } catch (Exception e) {
                    log.warn("校验错误:{}", po, e);
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "第" + (i + 1) + "行数据校验失败: " + e.getMessage());
                }
            }

            if (CollectionUtils.isNotEmpty(importList)) {
                varValueTtsReplacePOMapper.batchUpsert(importList);
            }

        } catch (Exception e) {
            log.warn("导入失败", e);
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, e.getMessage());
        }
    }



    private String getCellStringValue(Cell cell) {
        if (Objects.isNull(cell)) {
            return "";
        }
        DataFormatter dataFormatter = new DataFormatter();
        return dataFormatter.formatCellValue(cell);
    }

    private String getValue(List<String> row, String title, Map<String, Integer> titleIndexMap) {
        int index = titleIndexMap.get(title);
        if (index >= row.size()) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, title + "不能为空");
        }
        String obj = row.get(titleIndexMap.get(title));
        if (Objects.isNull(obj)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, title + "不能为空");
        }
        return obj;
    }

    @Override
    public TtsComposeResultVO audition(Long tenantId, Long id) {
        VarValueTtsReplacePO po = selectByKey(id);
        if (Objects.isNull(po)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "配置不存在");
        }
        return audition(po);
    }

    @Override
    public TtsComposeResultVO audition(VarValueTtsReplacePO audition) {
        validate(audition);
        return ttsComposeService.varAudition(audition.getVarName(), audition.getVarValue(), audition.getReplacement(), audition.getTtsProvider());
    }

    @Override
    public List<VarValueTtsReplaceDTO> queryAllByTenantId(Long tenantId) {
        return varValueTtsReplacePOMapper.queryByTenantId(tenantId).stream()
                .map(item -> {
                    VarValueTtsReplaceDTO dto = new VarValueTtsReplaceDTO();
                    dto.setReplacement(item.getReplacement());
                    dto.setVarName(item.getVarName());
                    dto.setVarValue(item.getVarValue());
                    dto.setTtsProviderCode(item.getTtsProvider().getCode());
                    return dto;
                }).collect(Collectors.toList());
    }

    @Override
    public List<IdNamePair<String, String>> getSupports() {
        List<IdNamePair<String, String>> ttsProviderEnums = new ArrayList<>();
        ttsProviderEnums.add(IdNamePair.of(TtsProviderEnum.ALI.name(), TtsProviderEnum.ALI.getDesc()));
        ttsProviderEnums.add(IdNamePair.of(TtsProviderEnum.MICROSOFT.name(), TtsProviderEnum.MICROSOFT.getDesc()));
        ttsProviderEnums.add(IdNamePair.of(TtsProviderEnum.YIWISE3.name(), TtsProviderEnum.YIWISE3.getDesc()));
        return ttsProviderEnums;
    }

    @Override
    public void delete(VarValueTtsReplaceDeleteVO deleteRequest, Long userId) {
        if (Objects.isNull(deleteRequest.getTenantId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "租户id不能为空");
        }
        if (Objects.isNull(deleteRequest.getIdList()) && Objects.isNull(deleteRequest.getDeleteAll())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "id列表和删除全部不能同时为空");
        }
        if (Objects.nonNull(deleteRequest.getIdList())) {
            deleteByIdList(deleteRequest.getTenantId(), deleteRequest.getIdList(), userId);
        }
        if (Objects.nonNull(deleteRequest.getDeleteAll()) && deleteRequest.getDeleteAll()) {
            deleteByTenantId(deleteRequest.getTenantId(), userId);
        }
    }

    @Override
    public String export(VarValueTtsReplaceExportRequest request) {
        if (Objects.isNull(request.getTenantId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "租户id不能为空");
        }
        List<VarValueTtsReplacePO> list = Collections.emptyList();
        if (BooleanUtils.isTrue(request.getExportAll())) {
            list = varValueTtsReplacePOMapper.queryByTenantId(request.getTenantId());
        } else if (CollectionUtils.isNotEmpty(request.getIdList())) {
            list = varValueTtsReplacePOMapper.queryByTenantIdAndIdList(request.getTenantId(), request.getIdList());
        }

        if (CollectionUtils.isEmpty(list)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "导出数据为空");
        }

        SXSSFWorkbook workbook = new SXSSFWorkbook();
        SXSSFSheet sheet = workbook.createSheet("变量读音值替换");
        writeToExcel(list, sheet);

        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            workbook.write(bos);
            String ossKey = OssKeyCenter.getVarValueReplaceExport();
            String url = objectStorageHelper.upload(ossKey, bos.toByteArray());
            return AddOssPrefixSerializer.getAddOssPrefixUrl(url);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "导出失败");
        } finally {
            // 清除临时文件
            workbook.dispose();
        }
    }

    private static void writeToExcel(List<VarValueTtsReplacePO> dataList, SXSSFSheet sheet) {
        sheet.setDefaultRowHeightInPoints(22);
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 20 * 256);
        sheet.setColumnWidth(3, 100 * 256);
        SXSSFRow title = sheet.createRow(0);
        title.createCell(0).setCellValue("变量名");
        title.createCell(1).setCellValue("变量值");
        title.createCell(2).setCellValue("TTS供应商");
        title.createCell(3).setCellValue("读音值");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (int i = 0; i < dataList.size(); i++) {
            SXSSFRow row = sheet.createRow(i + 1);
            VarValueTtsReplacePO item = dataList.get(i);
            row.createCell(0).setCellValue(item.getVarName());
            row.createCell(1).setCellValue(item.getVarValue());
            row.createCell(2).setCellValue(item.getTtsProvider().getDesc());
            row.createCell(3).setCellValue(item.getReplacement());
        }
    }


    private VarValueTtsReplacePO queryByVariableNameAndValue(Long tenantId,
                                                             TtsProviderEnum provider,
                                                             String variableName,
                                                             String variableValue,
                                                             Long excludeId) {
        return varValueTtsReplacePOMapper.queryByVariableNameAndValue(tenantId, provider.getCode(), variableName, variableValue, excludeId);
    }
}
