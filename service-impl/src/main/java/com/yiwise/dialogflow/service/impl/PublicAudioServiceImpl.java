package com.yiwise.dialogflow.service.impl;

import com.google.common.collect.Lists;
import com.mongodb.client.result.DeleteResult;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.OssKeyCenter;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderElement;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.entity.enums.GroupTypeEnum;
import com.yiwise.dialogflow.entity.enums.SyncModeEnum;
import com.yiwise.dialogflow.entity.po.BotAudioConfigPO;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.GroupPO;
import com.yiwise.dialogflow.entity.po.PublicAudioPO;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.audio.AnswerAudioWrapVO;
import com.yiwise.dialogflow.entity.vo.audio.AnswerPlaceholderElementVO;
import com.yiwise.dialogflow.entity.vo.audio.PublicAudioVO;
import com.yiwise.dialogflow.entity.vo.audio.request.*;
import com.yiwise.dialogflow.helper.AddOssPrefixSerializer;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.impl.audio.AudioUploadServiceImpl;
import com.yiwise.dialogflow.service.remote.UserService;
import com.yiwise.dialogflow.utils.ParseUtil;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/14
 */
@Slf4j
@Service
public class PublicAudioServiceImpl implements PublicAudioService {

    /**
     * 标点符号
     */
    private static final String PUNCTUATION = ",;!?.，；！？。";

    /**
     * 同义标点
     */
    private static final String SYNONYMOUS_PUNCTUATION = ",，.。";

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private ObjectStorageHelper objectStorageHelper;

    @Resource
    private UserService userService;

    @Resource
    private GroupService groupService;

    @Resource
    private BotConfigService botConfigService;

    @Resource
    private AnswerAudioManagerService answerAudioManagerService;

    @Resource
    private AnswerAudioMappingService answerAudioMappingService;

    @Resource
    private BotSyncOperationLogService botSyncOperationLogService;

    public static String text2Regex(String text) {
        if (StringUtils.isBlank(text)) {
            return null;
        }
        String middle = text;
        StringBuffer sb = new StringBuffer();
        sb.append("^");
        sb.append("[" + PUNCTUATION + "]?");
        char firstChar = text.charAt(0);
        if (PUNCTUATION.indexOf(firstChar) >= 0) {
            middle = StringUtils.substring(middle, 1);
        }
        if (middle.length() >= 1) {
            char lastChar = middle.charAt(middle.length() - 1);
            if (PUNCTUATION.indexOf(lastChar) >= 0 && SYNONYMOUS_PUNCTUATION.indexOf(lastChar) < 0) {
                sb.append(ParseUtil.regexEscape(middle));
            } else {
                if (SYNONYMOUS_PUNCTUATION.indexOf(lastChar) >= 0) {
                    middle = StringUtils.substring(middle, 0, middle.length() - 1);
                }
                sb.append(ParseUtil.regexEscape(middle));
                sb.append("[" + SYNONYMOUS_PUNCTUATION + "]?");
            }
        }
        sb.append("$");
        log.info("文案【{}】转正则【{}】", text, sb);
        return sb.toString();
    }

    @Override
    public PublicAudioPO selectOne(String groupId, Long recordUserId, String regex) {
        if (StringUtils.isBlank(groupId) || Objects.isNull(recordUserId) || StringUtils.isBlank(regex)) {
            return null;
        }
        Query query = Query.query(Criteria.where("groupId").is(groupId).and("recordUserId").is(recordUserId).and("regex").is(regex));
        query.limit(1);
        return mongoTemplate.findOne(query, PublicAudioPO.class, PublicAudioPO.COLLECTION_NAME);
    }

    @Override
    public void sync(Long recordUserId, List<AnswerAudioWrapVO> answerAudioWrapVOList, BotPO bot, SyncAudioRequestVO request, Long userId) {
        LocalDateTime now = LocalDateTime.now();
        String groupId = request.getGroupId();

        String userName = Optional.ofNullable(userId).map(userService::getUserById).map(UserPO::getName).orElse(null);
        String recordUserName = Optional.ofNullable(recordUserId).map(userService::getUserById).map(UserPO::getName).orElse(null);

        for (AnswerAudioWrapVO answerAudioWrapVO : answerAudioWrapVOList) {
            List<AnswerPlaceholderElementVO> collect = answerAudioWrapVO.getAnswerElementList().stream()
                    .filter(s -> TextPlaceholderTypeEnum.TEXT.equals(s.getType()) && StringUtils.isNotBlank(s.getUrl()) && AudioTypeEnum.MAN_MADE.equals(s.getAudioType()))
                    .collect(Collectors.toList());
            for (AnswerPlaceholderElementVO mapping : collect) {
                String text = mapping.getValue();
                log.info("开始同步音频【{}】", text);
                String regex = text2Regex(text);
                PublicAudioPO existsAudio = selectOne(groupId, recordUserId, regex);
                if (Objects.nonNull(existsAudio)) {
                    log.info("音频【{}】已存在", existsAudio.getText());
                    if (SyncModeEnum.SKIP.equals(request.getSyncMode())) {
                        continue;
                    } else {
                        // 删除旧的音频
                        mongoTemplate.remove(Query.query(Criteria.where("_id").is(existsAudio.getId())), PublicAudioPO.COLLECTION_NAME);
                    }
                }
                // 保存新的音频
                PublicAudioPO po = new PublicAudioPO();
                po.setName(answerAudioWrapVO.getLocate().getDisplayName());
                po.setRecordUserId(recordUserId);
                po.setRecordUserName(recordUserName);
                po.setBotId(bot.getBotId());
                po.setBotName(bot.getName());
                po.setText(text);
                po.setRegex(regex);
                String newUrl = OssKeyCenter.getBotUploadAudioPath(0L, AudioUploadServiceImpl.convertToStorageName(mapping.getUrl()));
                objectStorageHelper.copyObject(mapping.getUrl(), newUrl);
                po.setUrl(newUrl);
                po.setVolume(mapping.getVolume());
                po.setDuration(mapping.getDuration());
                po.setGroupId(groupId);
                po.setCreateTime(now);
                po.setUpdateTime(now);
                po.setCreateUserId(userId);
                po.setUpdateUserId(userId);
                po.setUpdateUserName(userName);
                mongoTemplate.save(po, PublicAudioPO.COLLECTION_NAME);
                log.info("音频【{}】同步成功", text);
            }
        }
    }

    private Criteria buildCriteria(PublicAudioSearchRequestVO request) {
        List<Criteria> criteriaList = Lists.newArrayList();

        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(request.getSearch())) {
            List<Criteria> orCriteriaList = Lists.newArrayList();
            String regex = ParseUtil.regex(request.getSearch());
            orCriteriaList.add(Criteria.where("name").regex(regex));
            orCriteriaList.add(Criteria.where("text").regex(regex));
            criteriaList.add(criteria.orOperator(orCriteriaList.toArray(new Criteria[]{})));
        }

        if (CollectionUtils.isNotEmpty(request.getRecordUserIdList())) {
            criteriaList.add(Criteria.where("recordUserId").in(request.getRecordUserIdList()));
        }

        if (CollectionUtils.isNotEmpty(request.getSourceBotIdList())) {
            criteriaList.add(Criteria.where("botId").in(request.getSourceBotIdList()));
        }

        if (CollectionUtils.isNotEmpty(request.getUpdateUserIdList())) {
            criteriaList.add(Criteria.where("updateUserId").in(request.getUpdateUserIdList()));
        }

        if (Objects.nonNull(request.getStartTime())) {
            criteriaList.add(Criteria.where("updateTime").gte(request.getStartTime()));
        }
        if (Objects.nonNull(request.getEndTime())) {
            criteriaList.add(Criteria.where("updateTime").lte(request.getEndTime()));
        }

        if (CollectionUtils.isNotEmpty(request.getIdList())) {
            criteriaList.add(Criteria.where("_id").in(request.getIdList()));
        }

        if (StringUtils.isNotBlank(request.getGroupId())) {
            if (BooleanUtils.isNotTrue(request.getShowSubLevel())) {
                criteriaList.add(Criteria.where("groupId").is(request.getGroupId()));
            } else {
                List<String> groupIdList = groupService.listCurrentAndDescendantGroupId(request.getGroupId());
                criteriaList.add(Criteria.where("groupId").in(groupIdList));
            }
        }

        if (CollectionUtils.isNotEmpty(request.getExcludeIdList())) {
            criteriaList.add(Criteria.where("_id").nin(request.getExcludeIdList()));
        }

        if (CollectionUtils.isNotEmpty(criteriaList)) {
            criteria.andOperator(criteriaList.toArray(new Criteria[]{}));
        }
        return criteria;
    }

    private Query toUnPageQuery(PublicAudioSearchRequestVO request) {
        return Query.query(buildCriteria(request));
    }

    @Override
    public PageResultObject<PublicAudioVO> list(PublicAudioSearchRequestVO request) {
        Query query = toUnPageQuery(request);
        long count = mongoTemplate.count(query, PublicAudioPO.COLLECTION_NAME);
        query.with(PageRequest.of(request.getPageNum() - 1, request.getPageSize()));
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        List<PublicAudioVO> list = mongoTemplate.find(query, PublicAudioVO.class, PublicAudioPO.COLLECTION_NAME);
        List<String> groupIdList = MyCollectionUtils.listToConvertList(list, PublicAudioVO::getGroupId);
        Map<String, String> groupMap = MyCollectionUtils.listToMap(groupService.listByIds(groupIdList), GroupPO::getId, GroupPO::getPath);
        list.forEach(vo -> {
            vo.setFullUrl(AddOssPrefixSerializer.getAddOssPrefixUrl(vo.getUrl()));
            vo.setGroupPath(Optional.ofNullable(vo.getGroupId()).map(groupMap::get).orElse(null));
        });
        return PageResultObject.of(list, request.getPageNum(), request.getPageSize(), (int) count);
    }

    private List<IdNamePair<Long, String>> groupList(String key, String value) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where(key).ne(null).and(value).ne(null)),
                Aggregation.group(key, value));
        return mongoTemplate.aggregate(aggregation, PublicAudioPO.COLLECTION_NAME, Map.class).getMappedResults()
                .stream().map(s -> IdNamePair.of(Long.valueOf(String.valueOf(s.get(key))), String.valueOf(s.get(value)))).collect(Collectors.toList());
    }

    @Override
    public List<IdNamePair<Long, String>> recordUserList() {
        return groupList("recordUserId", "recordUserName");
    }

    @Override
    public List<IdNamePair<Long, String>> sourceBotList(String search, Integer pageSize) {
        Criteria criteria = Criteria.where("botId").ne(null).and("botName").ne(null);
        if (StringUtils.isNotBlank(search)) {
            List<Criteria> orCriteriaList = new ArrayList<>();
            try {
                long botId = Long.parseLong(search);
                orCriteriaList.add(Criteria.where("botId").is(botId));
            } catch (NumberFormatException ignored) {
            }
            orCriteriaList.add(Criteria.where("botName").regex(search));
            criteria.andOperator(new Criteria().orOperator(orCriteriaList.toArray(new Criteria[0])));
        }
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("botId", "botName"),
                Aggregation.limit(pageSize)
        );
        return mongoTemplate.aggregate(aggregation, PublicAudioPO.COLLECTION_NAME, Map.class).getMappedResults()
                .stream().map(s -> IdNamePair.of(Long.valueOf(String.valueOf(s.get("botId"))), String.valueOf(s.get("botName")))).collect(Collectors.toList());
    }

    @Override
    public List<IdNamePair<Long, String>> updateUserList() {
        return groupList("updateUserId", "updateUserName");
    }

    @Override
    public void delete(PublicAudioSearchRequestVO request) {
        Query query = toUnPageQuery(request);
        DeleteResult deleteResult = mongoTemplate.remove(query, PublicAudioPO.COLLECTION_NAME);
        log.info("删除成功{}条音频", deleteResult.getDeletedCount());
    }

    @Override
    public void changeGroup(PublicAudioChangeGroupRequestVO request, Long userId) {
        String targetGroupId = request.getTargetGroupId();
        GroupPO targetGroup = groupService.selectOne(targetGroupId, GroupTypeEnum.PUBLIC_AUDIO, 0L);
        Assert.notNull(targetGroup, "目标分组不存在");

        if (BooleanUtils.isTrue(duplicateCheck(request))) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "选中音频中有相同，无法移动到同一分组");
        }

        List<String> waitDeleteIdList = Lists.newArrayList();
        List<String> waitChangeGroupIdList = Lists.newArrayList();

        int pageNum = 0;
        Query query = toUnPageQuery(request);
        while (true) {
            query.with(PageRequest.of(pageNum++, 100));
            List<PublicAudioPO> audioList = mongoTemplate.find(query, PublicAudioPO.class, PublicAudioPO.COLLECTION_NAME);
            if (CollectionUtils.isEmpty(audioList)) {
                break;
            }
            for (PublicAudioPO audio : audioList) {
                PublicAudioPO existsAudio = selectOne(targetGroupId, audio.getRecordUserId(), text2Regex(audio.getText()));
                if (Objects.nonNull(existsAudio)) {
                    if (StringUtils.equals(audio.getId(), existsAudio.getId()) || SyncModeEnum.SKIP.equals(request.getSyncMode())) {
                        continue;
                    } else {
                        waitDeleteIdList.add(existsAudio.getId());
                    }
                }
                waitChangeGroupIdList.add(audio.getId());
            }
        }

        if (CollectionUtils.isNotEmpty(waitDeleteIdList)) {
            mongoTemplate.remove(Query.query(Criteria.where("_id").in(waitDeleteIdList)), PublicAudioPO.COLLECTION_NAME);
        }
        if (CollectionUtils.isNotEmpty(waitChangeGroupIdList)) {
            String userName = Optional.ofNullable(userId).map(userService::getUserById).map(UserPO::getName).orElse(null);
            Update update = Update.update("groupId", targetGroupId).set("updateTime", LocalDateTime.now())
                    .set("updateUserId", userId).set("updateUserName", userName);
            mongoTemplate.updateMulti(Query.query(Criteria.where("_id").in(waitChangeGroupIdList)), update, PublicAudioPO.COLLECTION_NAME);
        }
    }

    @Override
    public Boolean duplicateCheck(PublicAudioSearchRequestVO request) {

        Criteria criteria = buildCriteria(request);

        Query query = Query.query(criteria);
        long count = mongoTemplate.count(query, PublicAudioPO.COLLECTION_NAME);

        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(Aggregation.match(criteria));
        aggregationOperationList.add(Aggregation.group("recordUserId", "regex"));
        aggregationOperationList.add(Aggregation.count().as("count"));
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
        long aggregationCount = Optional.ofNullable(mongoTemplate.aggregate(aggregation, PublicAudioPO.COLLECTION_NAME, Map.class).getUniqueMappedResult())
                .map(map -> map.get("count")).map(String::valueOf).map(Long::parseLong).orElse(0L);

        log.info("音频【{}】条,分组后【{}】条", count, aggregationCount);
        return count != aggregationCount;
    }

    @Override
    public void update(PublicAudioUpdateRequestVO request, Long userId) {
        String userName = Optional.ofNullable(userId).map(userService::getUserById).map(UserPO::getName).orElse(null);
        Update update = Update.update("url", request.getUrl()).set("volume", request.getVolume()).set("duration", request.getDuration())
                .set("updateTime", LocalDateTime.now()).set("updateUserId", userId).set("updateUserName", userName);
        mongoTemplate.updateFirst(Query.query(Criteria.where("_id").is(request.getId())), update, PublicAudioPO.COLLECTION_NAME);
    }

    @Override
    public void sync(PublicAudioSyncRequestVO request) {
        Long botId = request.getTargetBotId();
        Long recordUserId = Optional.ofNullable(botConfigService.getAudioConfig(botId))
                .filter(config -> !AudioTypeEnum.COMPOSE.equals(config.getAudioType()))
                .map(BotAudioConfigPO::getRecordUserId)
                .orElseThrow(() -> new ComException(ComErrorCode.VALIDATE_ERROR, "录音师不存在"));

        Criteria criteria = buildCriteria(request);

        List<String> successAnswerList = new ArrayList<>();

        answerAudioManagerService.getAllByBotId(botId).parallelStream().forEach(answerAudioWrapVO -> {
            for (AnswerPlaceholderElement answerPlaceholderElement : answerAudioWrapVO.getAnswerElementList()) {
                if (!TextPlaceholderTypeEnum.TEXT.equals(answerPlaceholderElement.getType())) {
                    continue;
                }
                if (SyncModeEnum.SKIP.equals(request.getSyncMode()) && StringUtils.isNotBlank(answerPlaceholderElement.getUrl())) {
                    continue;
                }
                String text = answerPlaceholderElement.getValue();
                String regex = text2Regex(text);
                Query query = Query.query(new Criteria().andOperator(criteria, Criteria.where("recordUserId").is(recordUserId).and("regex").is(regex))).limit(1);
                PublicAudioPO existsAudio = mongoTemplate.findOne(query, PublicAudioPO.class, PublicAudioPO.COLLECTION_NAME);
                if (Objects.isNull(existsAudio)) {
                    continue;
                }
                String newUrl = OssKeyCenter.getBotUploadAudioPath(botId, AudioUploadServiceImpl.convertToStorageName(existsAudio.getUrl()));
                objectStorageHelper.copyObject(existsAudio.getUrl(), newUrl);
                answerAudioMappingService.upsertAudioMapping(botId, recordUserId, AudioTypeEnum.MAN_MADE, text,
                        newUrl, existsAudio.getVolume(), existsAudio.getDuration());
                successAnswerList.add(text);
            }
        });

        botSyncOperationLogService.audioSync(request, successAnswerList);
    }

    @Override
    public void download(String id, HttpServletResponse response) {
        PublicAudioPO audio = mongoTemplate.findById(id, PublicAudioPO.class);
        Assert.notNull(audio, "音频不存在");
        InputStream is = objectStorageHelper.downloadToInputStream(audio.getUrl());
        try {
            response.reset();
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.getMimeType());
            response.setHeader("Content-Disposition", String.format("attachment;filename=%s", System.currentTimeMillis() + ".wav"));
            IOUtils.copy(is, response.getOutputStream());
        } catch (Exception ex) {
            log.error("音频下载失败", ex);
        } finally {
            IOUtils.closeQuietly(is);
        }
    }
}