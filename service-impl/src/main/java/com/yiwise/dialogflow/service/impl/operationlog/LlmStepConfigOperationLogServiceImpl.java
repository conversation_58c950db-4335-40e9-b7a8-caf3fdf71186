package com.yiwise.dialogflow.service.impl.operationlog;

import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import com.yiwise.dialogflow.entity.po.LlmStepCollectTaskConfigPO;
import com.yiwise.dialogflow.entity.po.LlmStepConfigPO;
import com.yiwise.dialogflow.entity.po.LlmStepVariableAssign;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.operationlog.LlmStepConfigOperationLogService;
import com.yiwise.dialogflow.service.operationlog.MismatchOperationLogService;
import com.yiwise.dialogflow.service.operationlog.UninterruptedOperationLogService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class LlmStepConfigOperationLogServiceImpl implements LlmStepConfigOperationLogService {

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private MismatchOperationLogService mismatchOperationLogService;

    @Resource
    private UninterruptedOperationLogService uninterruptedOperationLogService;

    @Override
    public void create(Long botId, String stepName, Long userId) {
        operationLogService.save(botId, OperationLogTypeEnum.STEP, OperationLogResourceTypeEnum.LLM_STEP,
                String.format("新建大模型流程【%s】配置", stepName), userId);
    }

    @Override
    public void update(String stepName, LlmStepConfigPO oldConfig, LlmStepConfigPO newConfig, Long userId, DependentResourceBO resource) {
        Long botId = oldConfig.getBotId();
        List<String> logList = new ArrayList<>();
        if (!StringUtils.equals(oldConfig.getRoleDesc(), newConfig.getRoleDesc())) {
            logList.add(String.format("%s角色描述【%s】,修改为【%s】", prefix(stepName), oldConfig.getRoleDesc(), newConfig.getRoleDesc()));
        }
        if (!StringUtils.equals(oldConfig.getPrompt(), newConfig.getPrompt())) {
            logList.add(String.format("%s提示词【%s】,修改为【%s】", prefix(stepName), oldConfig.getPrompt(), newConfig.getPrompt()));
        }
        if (!StringUtils.equals(oldConfig.getBackground(), newConfig.getBackground())) {
            logList.add(String.format("%s背景信息【%s】,修改为【%s】", prefix(stepName), StringUtils.trimToEmpty(oldConfig.getBackground()), StringUtils.trimToEmpty(newConfig.getBackground())));
        }
        String oldConfigInfo = generateCollectTaskInfo(oldConfig.getCollectTaskConfigList(), resource.getVariableIdNameMap());
        String newConfigInfo = generateCollectTaskInfo(newConfig.getCollectTaskConfigList(), resource.getVariableIdNameMap());
        if (!StringUtils.equals(oldConfigInfo, newConfigInfo)) {
            logList.add(String.format("%s任务配置【%s】,修改为【%s】", prefix(stepName), oldConfigInfo, newConfigInfo));
        }
        logList.addAll(mismatchOperationLogService.compare(prefix(stepName), oldConfig, newConfig, resource));
        logList.addAll(uninterruptedOperationLogService.compare(prefix(stepName), oldConfig, newConfig, resource));
        String oldVariableAssignInfo = generateVariableAssignInfo(oldConfig, resource.getVariableIdNameMap());
        String newVariableAssignInfo = generateVariableAssignInfo(newConfig, resource.getVariableIdNameMap());
        if (!StringUtils.equals(oldVariableAssignInfo, newVariableAssignInfo)) {
            logList.add(String.format("%s变量赋值配置【%s】,修改为【%s】", prefix(stepName), oldVariableAssignInfo, newVariableAssignInfo));
        }
        String oldSilence = generateSilenceLog(oldConfig);
        String newSilence = generateSilenceLog(newConfig);
        if (!StringUtils.equals(oldSilence, newSilence)) {
            logList.add(String.format("%s用户无应答时长设置【%s】,修改为【%s】", prefix(stepName), oldSilence, newSilence));
        }
        for (String log : logList) {
            operationLogService.save(botId, OperationLogTypeEnum.STEP, OperationLogResourceTypeEnum.LLM_STEP, log, userId);
        }
    }

    private String generateSilenceLog(LlmStepConfigPO config) {
        if (BooleanUtils.isNotTrue(config.getEnableUserSilence())) {
            return "未开启";
        }
        if (Objects.isNull(config.getCustomUserSilenceSecond())) {
            return "开启, 但未设置时长";
        }
        return String.format("%s秒", config.getCustomUserSilenceSecond());

    }

    private String prefix(String stepName) {
        return String.format("编辑大模型流程配置：原流程【%s】", stepName);
    }

    private String generateCollectTaskInfo(List<LlmStepCollectTaskConfigPO> collectTaskConfigList, Map<String, String> variableIdNameMap) {
        if (CollectionUtils.isEmpty(collectTaskConfigList)) {
            return "";
        }
        return collectTaskConfigList.stream().map(config -> {
                    StringJoiner sj = new StringJoiner("，");
                    sj.add("任务描述：" + config.getDesc());
                    sj.add("推荐引导话术：" + (Objects.isNull(config.getGuideAnswer()) ? "" : config.getGuideAnswer()));
                    sj.add("变量赋值：" + generateVariableAssignInfo(config, variableIdNameMap));
                    return sj.toString();
                }).map(s -> "【" + s + "】")
                .collect(Collectors.joining(","));
    }

    private String generateVariableAssignInfo(LlmStepVariableAssign assign, Map<String, String> variableIdNameMap) {
        if (BooleanUtils.isNotTrue(assign.getEnableAssign())) {
            return "未启用";
        } else {
            return assign.getVariableAssignConfigList().stream().map(config -> "<" + variableIdNameMap.getOrDefault(config.getVariableId(), "") + "，" + config.getDesc() + ">")
                .collect(Collectors.joining(","));
        }
    }
}