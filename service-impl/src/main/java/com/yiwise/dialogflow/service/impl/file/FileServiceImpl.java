package com.yiwise.dialogflow.service.impl.file;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.OssKeyCenter;
import com.yiwise.dialogflow.common.TempFilePathKeyCenter;
import com.yiwise.dialogflow.service.file.FileService;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import com.yiwise.middleware.objectstorage.serializer.AddOssPrefixSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/19 15:32:51
 */
@Service
public class FileServiceImpl implements FileService {

    private static final Logger logger = LoggerFactory.getLogger(FileServiceImpl.class);

    @Resource
    private ObjectStorageHelper objectStorageHelper;

    @Override
    public Map<String, String> upload(Long tenantId, Long userId, MultipartFile multipartFile) {
        String fileName = multipartFile.getOriginalFilename();
        String localPath = TempFilePathKeyCenter.getUserUploadTempFilePath(fileName);
        File localPathFile = new File(localPath);
        if (!localPathFile.exists()) {
            boolean newFolderSuccess = localPathFile.mkdirs();
            if (!newFolderSuccess) {
                logger.error("新建储存用户上传文件的文件夹失败，此次用户上传的文件名为{}", fileName);
                throw new ComException(ComErrorCode.UNKNOWN_ERROR, "储存失败", "用户上传的文件储存到服务器失败");
            }
        }
        File file = new File(localPath);
        try {
            multipartFile.transferTo(file);
        } catch (IOException e) {
            logger.error("用户上传的文件储存到服务器失败，文件名为{}", fileName, e);
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "储存失败", "用户上传的文件储存到服务器失败");
        }

        Map<String, String> urls = new HashMap<>(2);
        String ossKey = OssKeyCenter.getUploadIssFileKey(tenantId, userId, fileName);
        String url = objectStorageHelper.upload(ossKey, file);
        urls.put("relativeURL", objectStorageHelper.getKeyFromUrl(url));
        urls.put("fullURL", url);
        urls.put("fullURLWithSign", AddOssPrefixSerializer.getAddOssPrefixUrl(url));
        return urls;
    }
}