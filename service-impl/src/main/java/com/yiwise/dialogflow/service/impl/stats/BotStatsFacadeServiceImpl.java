package com.yiwise.dialogflow.service.impl.stats;

import com.yiwise.base.common.helper.EnvironmentConstants;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.calloutjob.api.dto.CallOutJobRequestDTO;
import com.yiwise.calloutjob.api.vo.CallOutJobSimpleInfoVO;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.share.CallDataInfo;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.entity.bo.ActionNameResourceBO;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.bo.algorithm.ResourceId2NameBO;
import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.enums.DialStatusEnum;
import com.yiwise.dialogflow.entity.enums.NodeJumpTargetTypeEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleActionPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRulePO;
import com.yiwise.dialogflow.entity.po.stats.*;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;
import com.yiwise.dialogflow.entity.query.SpecialAnswerConfigQuery;
import com.yiwise.dialogflow.entity.query.StepNodeQuery;
import com.yiwise.dialogflow.entity.query.StepQueryVO;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.entity.vo.node.DialogBaseNodeVO;
import com.yiwise.dialogflow.entity.vo.stats.*;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.intent.IntentRuleActionService;
import com.yiwise.dialogflow.service.intent.IntentRuleService;
import com.yiwise.dialogflow.service.remote.IntentLevelTagDetailService;
import com.yiwise.dialogflow.service.remote.MaFlowService;
import com.yiwise.dialogflow.service.remote.RobotCallJobService;
import com.yiwise.dialogflow.service.remote.calloutjob.CallOutJobClient;
import com.yiwise.dialogflow.service.stats.*;
import com.yiwise.dialogflow.utils.BotStatsUtil;
import com.yiwise.dialogflow.utils.IntentRuleContentRenderUtils;
import javaslang.Tuple;
import javaslang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BotStatsFacadeServiceImpl implements BotStatsFacadeService {

    @Resource
    private IntentTriggerStatsService intentTriggerStatsService;

    @Resource
    private KnowledgeService knowledgeService;

    @Resource
    private StepService stepService;

    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    @Resource
    private AnswerStatsService answerStatsService;

    @Resource
    private BotReceptionStatsService botReceptionStatsService;

    @Lazy
    @Resource
    private StepNodeService stepNodeService;

    @Resource
    private StepNodeStatsService stepNodeStatsService;

    @Resource
    private RuleStatsService ruleStatsService;

    @Resource
    private StepStatsService stepStatsService;

    @Resource
    private RobotCallJobService robotCallJobService;

    @Resource
    private IntentRuleService intentRuleService;
    @Resource
    private IntentRuleActionService intentRuleActionService;

    @Resource
    private IntentLevelTagDetailService intentLevelTagDetailService;

    @Resource
    private BotService botService;

    @Resource
    private DependResourceService dependResourceService;

    @Resource
    private BotRefService botRefService;

    @Resource
    private MaFlowService maFlowService;

    @Resource
    private CallOutJobClient callOutJobClient;

    @Override
    public void saveAnalysisResult(BotStatsAnalysisResult analysisResult, CallDataInfo callDataInfo) {
        if (Objects.isNull(analysisResult)) {
            return;
        }
        if (Objects.isNull(analysisResult.getCallJobId())) {
            analysisResult.setCallJobId(0L);
        }
        if (Objects.isNull(analysisResult.getMaFlowId())) {
            analysisResult.setMaFlowId(0L);
        }
        if (Objects.isNull(analysisResult.getNewCallJobId())) {
            analysisResult.setNewCallJobId(0L);
        }
        if (Objects.isNull(analysisResult.getCallInJobId())) {
            analysisResult.setCallInJobId(0L);
        }
        log.info("统计分析结果: {}", JsonUtils.object2String(analysisResult));

        if (analysisResult.getCallJobId() < 1
                && analysisResult.getMaFlowId() < 1
                && analysisResult.getNewCallJobId() < 1
                && analysisResult.getCallInJobId() < 1) {
            if (EnvironmentConstants.CURR_ENV.isProd()) {
                log.info("当前环境为生产环境, 非外呼数据/ma外呼/新版外呼任务, 不进行统计分析, MA_BOT_STATS_IDS={}", ApplicationConstant.MA_BOT_STATS_IDS);
                return;
            }
        }

        // 分发到各个地方进行保存
        analysisResult.setCallEndTime(LocalDateTime.now());
        if (DialStatusEnum.ANSWERED.getCode().equals(callDataInfo.getDialStatus())) {
            // 后续优化策略是先把可以合并的结果合并了再刷到mongo里, 减少mongo写请求

            // 意图触发统计
            intentTriggerStatsService.saveIntentTriggerStats(analysisResult);

            // 节点跳转
            stepNodeStatsService.saveNodeJumpStats(analysisResult);

            // 流程命中统计
            stepStatsService.saveStepStats(analysisResult);

            // 答案统计
            answerStatsService.saveAnswerStats(analysisResult);
        }

        // 机器人接待, 如果未接通, 则递增totalCallCount总数, 如果接通了再递增count总数
        botReceptionStatsService.saveBotReceptionStats(analysisResult, callDataInfo.getDialStatus());

        // 规则数据
        ruleStatsService.saveRuleStats(analysisResult);
    }

    @Override
    public List<IntentTriggerStatsVO> queryIntentTriggerStats(Long botId, BaseStatsQuery condition) {
        List<IntentTriggerStatsPO> statsList = intentTriggerStatsService.queryIntentTriggerStats(botId, condition);
        if (CollectionUtils.isEmpty(statsList)) {
            return Collections.emptyList();
        }

        List<KnowledgePO> knowledgeList = knowledgeService.getAllListByBotId(botId);
        List<SpecialAnswerConfigPO> specialAnswerConfigList = specialAnswerConfigService.getByBotId(botId);
        List<StepPO> stepList = stepService.getAllListByBotId(botId);
        // 过滤掉已经删除掉的数据
        statsList = cleanDeletedData(statsList, knowledgeList, specialAnswerConfigList, stepList);

        List<AnswerStatsPO> knowledgeAnswerStatsList = answerStatsService.queryAllKnowledgeAllAnswerTotalHangupAndDeclineStats(botId, condition);
        List<AnswerStatsPO> specialAnswerConfigAnswerStatsList = answerStatsService.queryAllSpecialAnswerAllAnswerTotalHangupAndDeclineStats(botId, condition);
        Map<String, List<AnswerStatsPO>> knowledgeAnswerStatsListMap = MyCollectionUtils.listToMapList(knowledgeAnswerStatsList, AnswerStatsPO::getKnowledgeId);
        Map<String, List<AnswerStatsPO>> specialAnswerStatsListMap = MyCollectionUtils.listToMapList(specialAnswerConfigAnswerStatsList, AnswerStatsPO::getSpecialAnswerConfigId);

        Map<String, KnowledgePO> knowledgeMap = MyCollectionUtils.listToMap(knowledgeList, KnowledgePO::getId);
        Map<String, SpecialAnswerConfigPO> specialAnswerConfigMap = MyCollectionUtils.listToMap(specialAnswerConfigList, SpecialAnswerConfigPO::getId);
        Map<String, StepPO> stepMap = MyCollectionUtils.listToMap(stepList, StepPO::getId);

        // 查询通话数
        int botReceptionCount = botReceptionStatsService.queryBotReceptionCount(botId, condition);
        int totalCallCount = statsList.stream().map(IntentTriggerStatsPO::getCallCount).reduce(0, Integer::sum);
        return statsList.stream()
                .map(item -> {
                    IntentTriggerStatsVO vo = new IntentTriggerStatsVO();
                    if (NodeJumpTargetTypeEnum.STEP.name().equals(item.getTargetType())) {
                        StepPO target = stepMap.get(item.getTargetId());
                        if (Objects.isNull(target)) {
                            return null;
                        }
                        vo.setName(target.getName());
                        vo.setLabel(target.getLabel());
                        vo.setType(target.getType().getDesc());
                    } else if (NodeJumpTargetTypeEnum.KNOWLEDGE.name().equals(item.getTargetType())) {
                        KnowledgePO target = knowledgeMap.get(item.getTargetId());
                        if (Objects.isNull(target)) {
                            return null;
                        }

                        vo.setName(target.getName());
                        vo.setLabel(target.getLabel());
                        vo.setType(target.getCategory().getDesc());
                        calculateKnowledgeAnswerStats(item, vo, knowledgeAnswerStatsListMap.getOrDefault(target.getId(), Collections.emptyList()));
                    } else if (NodeJumpTargetTypeEnum.SPECIAL_ANSWER.name().equals(item.getTargetType())) {
                        SpecialAnswerConfigPO target = specialAnswerConfigMap.get(item.getTargetId());
                        if (Objects.isNull(target)) {
                            return null;
                        }
                        vo.setName(target.getName());
                        vo.setLabel(target.getLabel());
                        vo.setType("特殊语境");
                        calculateSpecialAnswerStats(item, vo, specialAnswerStatsListMap.getOrDefault(target.getId(), Collections.emptyList()));
                    } else {
                        return null;
                    }

                    vo.setReach(BotStatsUtil.calculatePercent(item.getCount(), botReceptionCount));
                    vo.setReachCall(BotStatsUtil.calculatePercent(item.getCallCount(), botReceptionCount));
                    vo.setHitRatio(BotStatsUtil.calculatePercent(item.getCallCount(), totalCallCount));
                    return vo;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<AnswerPlayProgressHangupStatsVO> queryAnswerPlayProgressHangupStats(Long botId,
                                                                                    AnswerSourceEnum answerSource,
                                                                                    String stepId, String nodeId,
                                                                                    String knowledgeId,
                                                                                    String specialAnswerConfigId,
                                                                                    BaseStatsQuery condition) {
        List<AnswerPlayProgressHangupStatsPO> statsList = Collections.emptyList();
        switch (answerSource) {
            case KNOWLEDGE: statsList = answerStatsService.queryKnowledgeAllAnswerProgressHangupStats(botId, knowledgeId, condition); break;
            case STEP: statsList = answerStatsService.queryNodeAllAnswerProgressHangupStats(botId, stepId, nodeId, condition); break;
            case SPECIAL_ANSWER: statsList = answerStatsService.querySpecialAnswerAllAnswerProgressHangupStats(botId, specialAnswerConfigId, condition); break;
            default:
                break;
        }
        return statsList.stream()
                .map(AnswerPlayProgressHangupStatsVO::from)
                .collect(Collectors.toList());
    }

    @Override
    public void wrapNodeStatsInfo(List<DialogBaseNodeVO> nodeList, StepNodeQuery condition) {
        if (CollectionUtils.isEmpty(nodeList) || BooleanUtils.isNotTrue(condition.getEnableStatsInfo())) {
            return;
        }

        String stepId = condition.getStepId();
        Long botId = condition.getBotId();
        List<String> nodeIdList = nodeList.stream().map(DialogBaseNodeVO::getId).collect(Collectors.toList());
        // 查询所有

        // 节点的数据从两个数据中获取,
        // 1. 节点跳转信息
        // 2. 节点挂机信息

        // 查询所有的跳转数据

        Set<String> knowledgeIdSet = knowledgeService.getAllListByBotId(botId).stream().map(KnowledgePO::getId).collect(Collectors.toSet());
        Set<String> specialAnswerConfigIdSet = specialAnswerConfigService.getByBotId(botId).stream().map(SpecialAnswerConfigPO::getId).collect(Collectors.toSet());
        Set<String> stepIdSet = stepService.getAllListByBotId(botId).stream().map(StepPO::getId).collect(Collectors.toSet());
        Set<String> intentIdSet = getIntentIdSetByBotId(botId);
        // 过滤掉已经删除了的特殊语境, 问答知识, 流程, 意图
        List<StepNodeJumpStatsPO> nodeJumpStatsList = stepNodeStatsService.queryNodesAllJumpStats(botId, stepId, nodeIdList, condition);
        nodeJumpStatsList = cleanDeletedData(nodeJumpStatsList, knowledgeIdSet,stepIdSet, specialAnswerConfigIdSet, intentIdSet);
        Map<String, List<StepNodeJumpStatsPO>> listMap = MyCollectionUtils.listToMapList(nodeJumpStatsList, StepNodeJumpStatsPO::getNodeId);

        // 查询答案挂机信息
        List<AnswerStatsPO> answerStatsList = answerStatsService.queryStepAllAnswerTotalHangupStats(botId, stepId, nodeIdList, condition);
        Map<String, List<AnswerStatsPO>> nodeAnswerStatsMap = MyCollectionUtils.listToMapList(answerStatsList, AnswerStatsPO::getNodeId);
        // 包装并计算

        for (DialogBaseNodeVO node : nodeList) {
            String nodeId = node.getId();
            NodeStatsVO stats = new NodeStatsVO();
            Set<String> availableIntentSet = getNodeAvailableIntentSet(node);

            List<StepNodeJumpStatsPO> availableJumpStatsList = listMap.getOrDefault(nodeId, Collections.emptyList())
                    .stream()
                    .filter(item -> checkJumpTargetIsAvailable(item, knowledgeIdSet, stepIdSet, specialAnswerConfigIdSet, availableIntentSet))
                    .collect(Collectors.toList());

            List<AnswerStatsPO> nodeAnswerStatsList = nodeAnswerStatsMap.getOrDefault(nodeId, Collections.emptyList());
            int totalHangupCount = nodeAnswerStatsList.stream().mapToInt(AnswerStatsPO::getTotalHangupCount).sum();
            int total = availableJumpStatsList.stream().mapToInt(StepNodeJumpStatsPO::getCallCount).sum()
                    + totalHangupCount;

            List<StepNodeJumpStatsPO> toNextNodeList = availableJumpStatsList.stream()
                    .filter(item -> checkJumpTargetTypeIs(NodeJumpTargetTypeEnum.NODE, item))
                    .collect(Collectors.toList());

            Map<String, List<StepNodeJumpStatsPO>> toNextNodeMap = MyCollectionUtils.listToMapList(toNextNodeList, StepNodeJumpStatsPO::getIntentId);
            int jumpOutCount = availableJumpStatsList.stream()
                    .filter(item -> !checkJumpTargetTypeIs(NodeJumpTargetTypeEnum.NODE, item))
                    .mapToInt(StepNodeJumpStatsPO::getCallCount).sum();
            // 计算跳出数
            stats.setJumpOut(BotStatsUtil.calculatePercent(jumpOutCount, total));
            // 计算挂机数
            stats.setCustomerHangup(BotStatsUtil.calculatePercent(totalHangupCount, total));
            node.setStatsInfo(stats);
            // 设置分支数据
            resetNodeIntentBranchStats(node, toNextNodeMap, total);
        }
    }

    private Set<String> getIntentIdSetByBotId(Long botId) {
        return getIntentId2NameMapByBotId(botId).keySet();
    }

    @Override
    public List<NodeJumpStatsVO> queryNodeJumpOutStats(Long botId, String stepId, String nodeId, BaseStatsQuery condition) {
        StepPO step = stepService.getPOById(botId, stepId);
        if (Objects.isNull(step)) {
            return Collections.emptyList();
        }
        DialogBaseNodePO node = stepNodeService.getById(botId, stepId, nodeId);
        if (Objects.isNull(node)) {
            return Collections.emptyList();
        }
        List<StepNodeJumpStatsPO> originJumpStatsList = stepNodeStatsService.queryNodeJumpStats(botId, stepId, nodeId, condition);

        // 过滤现在存在的问答知识, 特殊语境, 流程
        List<KnowledgePO> knowledgeList = knowledgeService.getAllListByBotId(botId);
        List<SpecialAnswerConfigPO> specialAnswerConfigList = specialAnswerConfigService.getByBotId(botId);
        List<StepPO> stepList = stepService.getAllListByBotId(botId);

        Map<String, KnowledgePO> knowledgeMap = MyCollectionUtils.listToMap(knowledgeList, KnowledgePO::getId);
        Map<String, SpecialAnswerConfigPO> specialAnswerConfigMap = MyCollectionUtils.listToMap(specialAnswerConfigList, SpecialAnswerConfigPO::getId);
        Map<String, StepPO> stepMap = MyCollectionUtils.listToMap(stepList, StepPO::getId);

        Map<String, String> intentId2NameMap = getIntentId2NameMapByBotId(botId);
        // 过滤已删除的数据

        // 计算百分比
        List<StepNodeJumpStatsPO> jumpStatsList = cleanDeletedData(originJumpStatsList, knowledgeMap.keySet(), stepMap.keySet(), specialAnswerConfigMap.keySet(), intentId2NameMap.keySet())
                .stream().filter(item -> !NodeJumpTargetTypeEnum.NODE.name().equals(item.getTargetType()))
                .collect(Collectors.toList());

        int totalCount = jumpStatsList.stream().mapToInt(StepNodeJumpStatsPO::getCount).sum();
        int totalCallCount = jumpStatsList.stream().mapToInt(StepNodeJumpStatsPO::getCallCount).sum();
        return jumpStatsList.stream().map(item -> {
            NodeJumpStatsVO vo = new NodeJumpStatsVO();
            if (NodeJumpTargetTypeEnum.STEP.name().equals(item.getTargetType())) {
                StepPO target = stepMap.get(item.getTargetId());
                if (Objects.isNull(target)) {
                    return null;
                }
                vo.setName(target.getName());
                vo.setLabel(target.getLabel());
                vo.setType(target.getType().getDesc());
            } else if (NodeJumpTargetTypeEnum.KNOWLEDGE.name().equals(item.getTargetType())) {
                KnowledgePO target = knowledgeMap.get(item.getTargetId());
                if (Objects.isNull(target)) {
                    return null;
                }
                vo.setName(target.getName());
                vo.setLabel(target.getLabel());
                vo.setType(target.getCategory().getDesc());
            } else if (NodeJumpTargetTypeEnum.SPECIAL_ANSWER.name().equals(item.getTargetType())) {
                SpecialAnswerConfigPO target = specialAnswerConfigMap.get(item.getTargetId());
                if (Objects.isNull(target)) {
                    return null;
                }
                vo.setName(target.getName());
                vo.setLabel(target.getLabel());
                vo.setType("特殊语境");
            } else {
                return null;
            }
            vo.setIntentId(item.getIntentId());
            vo.setIntentName(intentId2NameMap.get(item.getIntentId()));
            vo.setReach(BotStatsUtil.calculatePercent(item.getCount(), totalCount));
            vo.setReachCall(BotStatsUtil.calculatePercent(item.getCallCount(), totalCallCount));
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private Map<String, String> getIntentId2NameMapByBotId(Long botId) {
        DependentResourceBO dependentResourceBO = dependResourceService.generateByCondition(new DependentResourceBO.Condition(botId).intent());
        return dependentResourceBO.getIntentIdNameMap();
    }

    private List<StepNodeJumpStatsPO> cleanDeletedData(List<StepNodeJumpStatsPO> allDataList,
                                                       Set<String> knowledgeIdSet,
                                                       Set<String> stepIdSet,
                                                       Set<String> specialAnswerConfigIdSet,
                                                       Set<String> intentIdSet) {
        return allDataList.stream()
                .filter(item -> intentIdSet.contains(item.getIntentId()))
                .filter(item -> checkJumpTargetIsAvailable(item, knowledgeIdSet, stepIdSet, specialAnswerConfigIdSet, intentIdSet))
                .collect(Collectors.toList());
    }

    private Set<String> getNodeAvailableIntentSet(DialogBaseNodeVO node) {
        if (CollectionUtils.isEmpty(node.getIntentInfoList())) {
            return Collections.emptySet();
        }
        return node.getIntentInfoList().stream().map(NodeIntentBranchStatsVO::getId).collect(Collectors.toSet());
    }

    @Override
    public void wrapKnowledgeStatsInfo(List<KnowledgeVO> knowledgeList, KnowledgeQueryVO condition) {
        if (CollectionUtils.isEmpty(knowledgeList) || BooleanUtils.isNotTrue(condition.getEnableStatsInfo())) {
            return;
        }

        Long botId = condition.getBotId();
        // 问答知识需要的统计数据有: 1. 命中次数, 2. 命中通数, 3. 客户主动挂机, 4. 客户拒绝

        List<String> knowledgeIdList = knowledgeList.stream().map(KnowledgeVO::getId).collect(Collectors.toList());
        List<IntentTriggerStatsPO> statsList = intentTriggerStatsService.queryIntentTriggerKnowledgeStats(botId, knowledgeIdList, condition);
        if (CollectionUtils.isEmpty(statsList)) {
            return;
        }

        // 过滤掉已经删除掉的数据
        statsList = cleanDeletedData(statsList, new HashSet<>(knowledgeIdList), Collections.emptySet(), Collections.emptySet());
        Map<String, IntentTriggerStatsPO> knowledgeStatsMap = MyCollectionUtils.listToMap(statsList, IntentTriggerStatsPO::getTargetId);

        List<AnswerStatsPO> knowledgeAnswerStatsList = answerStatsService.queryAllKnowledgeAllAnswerTotalHangupAndDeclineStats(botId, condition);
        Map<String, List<AnswerStatsPO>> knowledgeAnswerStatsListMap = MyCollectionUtils.listToMapList(knowledgeAnswerStatsList, AnswerStatsPO::getKnowledgeId);

        // 查询通话数
        int botReceptionCount = botReceptionStatsService.queryBotReceptionCount(botId, condition);
        for (KnowledgeVO knowledge : knowledgeList) {
            IntentTriggerStatsPO statsItem = knowledgeStatsMap.get(knowledge.getId());
            if (Objects.isNull(statsItem)) {
                continue;
            }
            wrapKnowledgeAnswerStats(statsItem, knowledge.getStatsInfo(), knowledgeAnswerStatsListMap.getOrDefault(knowledge.getId(), Collections.emptyList()));
            knowledge.getStatsInfo().setReach(BotStatsUtil.calculatePercent(statsItem.getCount(), botReceptionCount));
            knowledge.getStatsInfo().setReachCall(BotStatsUtil.calculatePercent(statsItem.getCallCount(), botReceptionCount));
        }
    }

    @Override
    public void wrapSpecialAnswerStatsInfo(List<SpecialAnswerConfigVO> specialAnswerConfigList, SpecialAnswerConfigQuery condition) {
        if (CollectionUtils.isEmpty(specialAnswerConfigList) || BooleanUtils.isNotTrue(condition.getEnableStatsInfo())) {
            return;
        }

        Long botId = condition.getBotId();
        // 问答知识需要的统计数据有: 1. 命中次数, 2. 命中通数, 3. 客户主动挂机, 4. 客户拒绝

        List<IntentTriggerStatsPO> statsList = intentTriggerStatsService.queryIntentTriggerSpecialAnswerStats(botId, condition);
        if (CollectionUtils.isEmpty(statsList)) {
            return;
        }

        Set<String> specialAnswerConfigIdSet = specialAnswerConfigList.stream().map(SpecialAnswerConfigVO::getId).collect(Collectors.toSet());

        // 过滤掉已经删除掉的数据
        statsList = cleanDeletedData(statsList, Collections.emptySet(), specialAnswerConfigIdSet, Collections.emptySet());
        Map<String, IntentTriggerStatsPO> statsMap = MyCollectionUtils.listToMap(statsList, IntentTriggerStatsPO::getTargetId);

        List<AnswerStatsPO> specialAnswerStatsList = answerStatsService.queryAllSpecialAnswerAllAnswerTotalHangupAndDeclineStats(botId, condition);
        Map<String, List<AnswerStatsPO>> specialAnswerStatsListMap = MyCollectionUtils.listToMapList(specialAnswerStatsList, AnswerStatsPO::getSpecialAnswerConfigId);

        // 查询通话数
        int botReceptionCount = botReceptionStatsService.queryBotReceptionCount(botId, condition);
        for (SpecialAnswerConfigVO specialAnswerConfig : specialAnswerConfigList) {
            IntentTriggerStatsPO statsItem = statsMap.get(specialAnswerConfig.getId());
            if (Objects.isNull(statsItem)) {
                continue;
            }
            wrapKnowledgeAnswerStats(statsItem, specialAnswerConfig.getStatsInfo(), specialAnswerStatsListMap.getOrDefault(specialAnswerConfig.getId(), Collections.emptyList()));
            specialAnswerConfig.getStatsInfo().setReach(BotStatsUtil.calculatePercent(statsItem.getCount(), botReceptionCount));
            specialAnswerConfig.getStatsInfo().setReachCall(BotStatsUtil.calculatePercent(statsItem.getCallCount(), botReceptionCount));
        }
    }

    @Override
    public void wrapIntentLevelRuleStatsInfo(List<IntentRuleVO> ruleList, BaseStatsQuery condition) {
        if (CollectionUtils.isEmpty(ruleList) || BooleanUtils.isNotTrue(condition.getEnableStatsInfo())) {
            return;
        }
        // 查询规则触发
        List<IntentRuleStatsPO> statsList = ruleStatsService.queryBotAllIntentLevelRuleStatsList(condition.getBotId(), condition);
        if (CollectionUtils.isEmpty(statsList)) {
            return;
        }
        Map<String, IntentRuleStatsPO> statsMap = MyCollectionUtils.listToMap(statsList, IntentRuleStatsPO::getRuleId);

        int botReceptionCount = botReceptionStatsService.queryBotReceptionCount(condition.getBotId(), condition);
        for (IntentRuleVO rule : ruleList) {
            IntentRuleStatsPO stats = statsMap.get(rule.getId());
            if (Objects.nonNull(stats)) {
                rule.getStatsInfo().setReach(BotStatsUtil.calculatePercent(stats.getCount(), botReceptionCount));
            }
        }
    }

    @Override
    public void wrapIntentActionRuleStatsInfo(List<IntentRuleActionVO> ruleList, BaseStatsQuery condition) {
        if (CollectionUtils.isEmpty(ruleList) || BooleanUtils.isNotTrue(condition.getEnableStatsInfo())) {
            return;
        }
        // 查询规则触发
        List<IntentRuleStatsPO> statsList = ruleStatsService.queryBotAllIntentActionRuleStatsList(condition.getBotId(), condition);
        if (CollectionUtils.isEmpty(statsList)) {
            return;
        }
        Map<String, IntentRuleStatsPO> statsMap = MyCollectionUtils.listToMap(statsList, IntentRuleStatsPO::getRuleId);
        int botReceptionCount = botReceptionStatsService.queryBotReceptionCount(condition.getBotId(), condition);
        for (IntentRuleActionVO rule : ruleList) {
            IntentRuleStatsPO stats = statsMap.get(rule.getId());
            if (Objects.nonNull(stats)) {
                rule.getStatsInfo().setReach(BotStatsUtil.calculatePercent(stats.getCount(), botReceptionCount));
            }
        }
    }

    @Override
    public void wrapStepStatsInfo(List<StepVO> stepList, StepQueryVO condition) {
        if (CollectionUtils.isEmpty(stepList) || BooleanUtils.isNotTrue(condition.getEnableStatsInfo())) {
            return;
        }
        List<String> stepIdList = stepList.stream().map(StepVO::getId).collect(Collectors.toList());
        List<StepStatsPO> statsList = stepStatsService.queryAllStepStatsList(condition.getBotId(), stepIdList, condition);
        Map<String, StepStatsPO> statsMap = MyCollectionUtils.listToMap(statsList, StepStatsPO::getStepId);
        int botReceptionCount = botReceptionStatsService.queryBotReceptionCount(condition.getBotId(), condition);
        Map<String, AtomicInteger> stepAnswerCustomerHangupCountMap = new HashMap<>();
        if (BooleanUtils.isTrue(condition.getWrapCustomerHangupStats())) {
            // 查询流程所有答案的挂机率
            List<AnswerStatsPO> answerStatsList = answerStatsService.queryAllStepAllAnswerTotalHangupStats(condition.getBotId(), condition);
            // 查询所有的节点
            List<DialogBaseNodePO> nodeList = stepNodeService.getListByStepIdList(condition.getBotId(), stepIdList);
            calculateStepTotalAnswerCustomerHangupCount(stepAnswerCustomerHangupCountMap, answerStatsList, nodeList);
        }

        for (StepVO step : stepList) {
            StepStatsPO statsInfo = statsMap.get(step.getId());
            if (Objects.nonNull(statsInfo)) {
                step.getStatsInfo().setReach(BotStatsUtil.calculatePercent(statsInfo.getCount(), botReceptionCount));
                step.getStatsInfo().setReachCall(BotStatsUtil.calculatePercent(statsInfo.getCallCount(), botReceptionCount));
            }
            AtomicInteger hangupCount = stepAnswerCustomerHangupCountMap.get(step.getId());
            if (Objects.nonNull(hangupCount)) {
                step.getStatsInfo().setCustomerHangup(BotStatsUtil.calculatePercent(hangupCount.get(), botReceptionCount));
            }
        }
    }

    private void calculateStepTotalAnswerCustomerHangupCount(Map<String, AtomicInteger> stepAnswerCustomerHangupCountMap,
                                                             List<AnswerStatsPO> answerStatsList,
                                                             List<DialogBaseNodePO> stepNodeList) {
        Set<Tuple2<String, String>> availableAnswerSet = new HashSet<>();
        for (DialogBaseNodePO node : stepNodeList) {
            availableAnswerSet.add(Tuple.of(node.getStepId(), node.getId()));
        }

        for (AnswerStatsPO answerStats : answerStatsList) {
            Tuple2<String, String> stepAnswerTuple = Tuple.of(answerStats.getStepId(), answerStats.getNodeId());
            if (availableAnswerSet.contains(stepAnswerTuple)) {
                stepAnswerCustomerHangupCountMap.computeIfAbsent(answerStats.getStepId(), k -> new AtomicInteger(0))
                        .addAndGet(answerStats.getTotalHangupCount());
            }
        }
    }

    @Override
    public List<SimpleCallJobInfoVO> queryCallJobList(BaseStatsQuery condition) {
        if (BooleanUtils.isNotTrue(condition.getEnableStatsInfo())) {
            return Collections.emptyList();
        }

        List<JobCallDateRangePO> list = botReceptionStatsService.queryJobReceptionInfo(condition.getBotId(), condition);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<Long> jobIdList = list.stream()
                .filter(item -> Objects.nonNull(item.getCallJobId()) && item.getCallJobId() > 0)
                .map(JobCallDateRangePO::getCallJobId)
                .collect(Collectors.toList());

        List<IdNamePair<Long, String>> jobNameList = robotCallJobService.selectJobIdNamePairByIdList(jobIdList);
        Map<Long, String> jobIdNameMap = MyCollectionUtils.listToMap(jobNameList, IdNamePair::getId, IdNamePair::getName);

        return list.stream().distinct()
                .map(item -> {
                    SimpleCallJobInfoVO jobInfo = new SimpleCallJobInfoVO();
                    jobInfo.setCallJobId(item.getCallJobId());
                    jobInfo.setCallJobName(jobIdNameMap.getOrDefault(item.getCallJobId(), String.valueOf(item.getCallJobId())));
                    jobInfo.setFirstCallDate(LocalDate.ofEpochDay(item.getFirstEpochDay()));
                    jobInfo.setLastCallDate(LocalDate.ofEpochDay(item.getLastEpochDay()));
                    return jobInfo;
                }).collect(Collectors.toList());
    }

    @Override
    public List<SimpleMaFlowInfoVO> queryMaFlowList(BaseStatsQuery condition) {
        if (BooleanUtils.isNotTrue(condition.getEnableStatsInfo())) {
            return Collections.emptyList();
        }

        condition.setQueryMaFlow(true);
        List<JobCallDateRangePO> list = botReceptionStatsService.queryJobReceptionInfo(condition.getBotId(), condition);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<Long> maFlowIdList = list.stream()
                .filter(item -> Objects.nonNull(item.getMaFlowId()) && item.getMaFlowId() > 0)
                .map(JobCallDateRangePO::getMaFlowId)
                .collect(Collectors.toList());

        List<IdNamePair<Long, String>> nameList = getMaFlowIdNamePair(maFlowIdList);
        Map<Long, String> jobIdNameMap = MyCollectionUtils.listToMap(nameList, IdNamePair::getId, IdNamePair::getName);

        return list.stream().distinct()
                .map(item -> {
                    SimpleMaFlowInfoVO jobInfo = new SimpleMaFlowInfoVO();
                    jobInfo.setMaFlowId(item.getMaFlowId());
                    jobInfo.setMaFlowName(jobIdNameMap.getOrDefault(item.getMaFlowId(), String.valueOf(item.getMaFlowId())));
                    jobInfo.setFirstCallDate(LocalDate.ofEpochDay(item.getFirstEpochDay()));
                    jobInfo.setLastCallDate(LocalDate.ofEpochDay(item.getLastEpochDay()));
                    return jobInfo;
                }).collect(Collectors.toList());
    }

    @Override
    public List<SimpleNewCallJobInfoVO> queryNewCallJobList(BaseStatsQuery condition) {
        if (BooleanUtils.isNotTrue(condition.getEnableStatsInfo())) {
            return Collections.emptyList();
        }

        condition.setQueryNewCallJob(true);
        List<JobCallDateRangePO> list = botReceptionStatsService.queryJobReceptionInfo(condition.getBotId(), condition);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<Long> newCallJobIdList = list.stream()
                .filter(item -> Objects.nonNull(item.getNewCallJobId()) && item.getNewCallJobId() > 0)
                .map(JobCallDateRangePO::getNewCallJobId)
                .collect(Collectors.toList());

        List<IdNamePair<Long, String>> nameList = getNewCallJobIdNamePair(newCallJobIdList);
        Map<Long, String> jobIdNameMap = MyCollectionUtils.listToMap(nameList, IdNamePair::getId, IdNamePair::getName);

        return list.stream().distinct()
                .map(item -> {
                    SimpleNewCallJobInfoVO jobInfo = new SimpleNewCallJobInfoVO();
                    jobInfo.setNewCallJobId(item.getNewCallJobId());
                    jobInfo.setCallJobName(jobIdNameMap.getOrDefault(item.getNewCallJobId(), String.valueOf(item.getNewCallJobId())));
                    jobInfo.setFirstCallDate(LocalDate.ofEpochDay(item.getFirstEpochDay()));
                    jobInfo.setLastCallDate(LocalDate.ofEpochDay(item.getLastEpochDay()));
                    return jobInfo;
                }).collect(Collectors.toList());
    }

    private List<IdNamePair<Long, String>> getMaFlowIdNamePair(List<Long> maFlowIdList) {
        if (CollectionUtils.isEmpty(maFlowIdList)) {
            return Collections.emptyList();
        }
        return maFlowService.getMaFlowNameList(maFlowIdList);
    }

    private List<IdNamePair<Long, String>> getNewCallJobIdNamePair(List<Long> newCallJobIdList) {
        if (CollectionUtils.isEmpty(newCallJobIdList)) {
            return Collections.emptyList();
        }

        CallOutJobRequestDTO request = new CallOutJobRequestDTO();
        request.setCallOutJobIds(newCallJobIdList);
        log.debug("查询新版外呼任务名称, request={}", request);
        try {
            List<CallOutJobSimpleInfoVO> list = callOutJobClient.getByIds(request);
            log.debug("查询新版外呼任务名称成功, response={}", list);
            return list.stream()
                    .map(item -> new IdNamePair<>(item.getCallOutJobId(), item.getCallOutJobName()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询新版外呼任务名称失败, request={}", request, e);
            return Collections.emptyList();
        }
    }

    @Override
    public JobCallDateVO queryCallDateInfo(BaseStatsQuery condition) {
        JobCallDateVO result = new JobCallDateVO();
        List<BotReceptionStatsPO> statsList = botReceptionStatsService.queryBotDailyReceptionInfo(condition.getBotId(), condition);
        List<String> dateList = statsList.stream()
                .map(item -> LocalDate.of(item.getYear(), item.getMonth(), item.getDay()))
                .sorted(Comparator.comparing(LocalDate::toEpochDay))
                .map(date -> date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                .collect(Collectors.toList());
        result.setDateList(dateList);
        return result;
    }

    @Override
    public List<SimpleNodeStatsInfoVO> queryAllNodeStats(BaseStatsQuery condition) {
        if (BooleanUtils.isNotTrue(condition.getEnableStatsInfo())) {
            return Collections.emptyList();
        }
        // 查询所有流程下所有节点的统计数据
        // 直接循环处理各个流程吧, 后面加了缓存后性能应该还好吧

        List<StepPO> stepList = stepService.getAllListByBotId(condition.getBotId());
        if (CollectionUtils.isEmpty(stepList)) {
            return Collections.emptyList();
        }
        List<String> stepIdList = stepList.stream().map(StepPO::getId).collect(Collectors.toList());
        List<DialogBaseNodeVO> nodeStatsList = stepNodeService.getVOListByStepIdList(MyBeanUtils.copy(condition, StepNodeQuery.class), stepIdList);
        Map<String, List<DialogBaseNodeVO>> stepNodeStatsMap = MyCollectionUtils.listToMapList(nodeStatsList, DialogBaseNodeVO::getStepId);
        List<SimpleNodeStatsInfoVO> result = new ArrayList<>();
        for (StepPO step : stepList) {
            StepNodeQuery nodeQuery = MyBeanUtils.copy(condition, StepNodeQuery.class);
            nodeQuery.setStepId(step.getId());
            List<DialogBaseNodeVO> nodeList = stepNodeStatsMap.getOrDefault(step.getId(), Collections.emptyList());
            for (DialogBaseNodeVO node : nodeList) {
                SimpleNodeStatsInfoVO nodeStatsInfo = new SimpleNodeStatsInfoVO();
                nodeStatsInfo.setStepId(step.getId());
                nodeStatsInfo.setStepName(step.getName());
                nodeStatsInfo.setStepLabel(step.getLabel());
                nodeStatsInfo.setId(node.getId());
                nodeStatsInfo.setName(node.getName());
                nodeStatsInfo.setLabel(node.getLabel());
                nodeStatsInfo.setIntentBranchStatsList(node.getIntentInfoList());
                nodeStatsInfo.setType(node.getType());
                nodeStatsInfo.setStatsInfo(node.getStatsInfo());
                nodeStatsInfo.setAnswerList(node.getAnswerList());
                result.add(nodeStatsInfo);
            }
        }
        return result;
    }

    @Override
    public List<SimpleIntentLevelStatsVO> queryIntentLevelStatsList(BaseStatsQuery condition) {
        if (BooleanUtils.isNotTrue(condition.getEnableStatsInfo())) {
            return Collections.emptyList();
        }
        // 查询所有流程下所有节点的统计数据
        // 直接循环处理各个流程吧, 后面加了缓存后性能应该还好吧
        BotPO bot = botService.selectByKey(condition.getBotId());
        if (Objects.isNull(bot)) {
            return Collections.emptyList();
        }

        List<IntentRuleStatsPO> statsList = ruleStatsService.queryBotAllIntentLevelStatsList(condition.getBotId(), condition);
        if (CollectionUtils.isEmpty(statsList)) {
            return Collections.emptyList();
        }

        // 查询所有的意图, 流程, 节点, 特殊语境
        List<IntentRulePO> intentLevelRuleList = intentRuleService.getIntentRuleList(condition.getBotId());
        Map<String, IntentRulePO> intentLevelRuleMap = MyCollectionUtils.listToMap(intentLevelRuleList, IntentRulePO::getId);

        List<String> nodeIdList = statsList.stream().map(IntentRuleStatsPO::getNodeId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> knowledgeIdList = statsList.stream().map(IntentRuleStatsPO::getKnowledgeId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<DialogBaseNodePO> nodeList = stepNodeService.getByIdList(condition.getBotId(), nodeIdList);
        Map<String, DialogBaseNodePO> nodeMap = MyCollectionUtils.listToMap(nodeList, DialogBaseNodePO::getId);
        List<KnowledgePO> knowledgeList = knowledgeService.getByIdList(condition.getBotId(), knowledgeIdList);
        Map<String, KnowledgePO> knowledgeMap = MyCollectionUtils.listToMap(knowledgeList, KnowledgePO::getId);
        List<SpecialAnswerConfigPO> specialAnswerConfigList = specialAnswerConfigService.getByBotId(condition.getBotId());
        Map<String, SpecialAnswerConfigPO> specialAnswerConfigMap = MyCollectionUtils.listToMap(specialAnswerConfigList, SpecialAnswerConfigPO::getId);
        List<SimpleIntentLevelStatsVO> result = new ArrayList<>();
        int botReceptionCount = botReceptionStatsService.queryBotReceptionCount(condition.getBotId(), condition);
        Map<Integer, String> intentLevelMap = intentLevelTagDetailService.getIntentLevelTagDetailCode2NameMap(bot.getIntentLevelTagId());
        ResourceId2NameBO resourceId2NameBO = intentRuleService.getResourceNameMap(condition.getBotId());

        statsList.forEach(stats -> {
            if (Objects.isNull(stats.getSource())) {
                return;
            }
            switch (stats.getSource()) {
                case STEP:
                    DialogBaseNodePO node = nodeMap.get(stats.getNodeId());
                    if (Objects.nonNull(node)
                            && BooleanUtils.isTrue(node.getEnableIntentLevel())
                            && Objects.nonNull(node.getIntentLevelDetailCode())) {
                        SimpleIntentLevelStatsVO statsVO = new SimpleIntentLevelStatsVO();
                        statsVO.setId(stats.getNodeId());
                        statsVO.setContent(String.format("%s:%s", node.getLabel(), node.getName()));
                        statsVO.setOrder("--");
                        statsVO.setIntentLevelDetailCode(node.getIntentLevelDetailCode());
                        statsVO.setIntentLevelName(intentLevelMap.getOrDefault(node.getIntentLevelDetailCode(), String.valueOf(node.getIntentLevelDetailCode())));
                        statsVO.setReach(BotStatsUtil.calculatePercent(stats.getCount(), botReceptionCount));
                        result.add(statsVO);
                    }
                    break;
                case KNOWLEDGE:
                    KnowledgePO knowledge = knowledgeMap.get(stats.getKnowledgeId());
                    if (Objects.nonNull(knowledge)
                            && BooleanUtils.isTrue(knowledge.getEnableIntentLevel())
                            && Objects.nonNull(knowledge.getIntentLevelDetailCode())) {
                        SimpleIntentLevelStatsVO statsVO = new SimpleIntentLevelStatsVO();
                        statsVO.setId(stats.getKnowledgeId());
                        statsVO.setContent(String.format("%s:%s", knowledge.getLabel(), knowledge.getName()));
                        statsVO.setOrder("--");
                        statsVO.setIntentLevelDetailCode(knowledge.getIntentLevelDetailCode());
                        statsVO.setIntentLevelName(intentLevelMap.getOrDefault(knowledge.getIntentLevelDetailCode(), String.valueOf(knowledge.getIntentLevelDetailCode())));
                        statsVO.setReach(BotStatsUtil.calculatePercent(stats.getCount(), botReceptionCount));
                        result.add(statsVO);
                    }
                    break;
                case SPECIAL_ANSWER:
                    SpecialAnswerConfigPO specialAnswerConfig = specialAnswerConfigMap.get(stats.getSpecialAnswerConfigId());
                    if (Objects.nonNull(specialAnswerConfig)
                            && BooleanUtils.isTrue(specialAnswerConfig.getEnableIntentLevel())
                            && Objects.nonNull(specialAnswerConfig.getIntentLevelDetailCode())) {
                        SimpleIntentLevelStatsVO statsVO = new SimpleIntentLevelStatsVO();
                        statsVO.setId(stats.getSpecialAnswerConfigId());
                        statsVO.setContent(String.format("%s:%s", specialAnswerConfig.getLabel(), specialAnswerConfig.getName()));
                        statsVO.setOrder("--");
                        statsVO.setIntentLevelDetailCode(specialAnswerConfig.getIntentLevelDetailCode());
                        statsVO.setIntentLevelName(intentLevelMap.getOrDefault(specialAnswerConfig.getIntentLevelDetailCode(), String.valueOf(specialAnswerConfig.getIntentLevelDetailCode())));
                        statsVO.setReach(BotStatsUtil.calculatePercent(stats.getCount(), botReceptionCount));
                        result.add(statsVO);
                    }
                    break;
                case RULE:
                    IntentRulePO rule = intentLevelRuleMap.get(stats.getRuleId());
                    if (Objects.nonNull(rule)) {
                        SimpleIntentLevelStatsVO statsVO = new SimpleIntentLevelStatsVO();
                        statsVO.setId(stats.getRuleId());
                        statsVO.setContent(IntentRuleContentRenderUtils.renderIntentRuleConditionContent(rule.getConditionList(), resourceId2NameBO));
                        statsVO.setOrder(String.valueOf(rule.getMatchOrder()));
                        statsVO.setIntentLevelDetailCode(rule.getIntentLevelTagDetailCode());
                        statsVO.setIntentLevelName(intentLevelMap.getOrDefault(rule.getIntentLevelTagDetailCode(), String.valueOf(rule.getIntentLevelTagDetailCode())));
                        statsVO.setReach(BotStatsUtil.calculatePercent(stats.getCount(), botReceptionCount));
                        result.add(statsVO);
                    }
                    break;
                default:
                    break;
            }
        });

        result.sort(Comparator.comparingLong(item -> getStatsOrderValue(item, condition)));
        return result;
    }

    private long getStatsOrderValue(SimpleIntentLevelStatsVO item, BaseStatsQuery condition) {
        long orderValue = geOrderValueByOrderBy(item, condition);
        if (ApplicationConstant.SORT_DIRECTION_DESC.equals(condition.getDirection())) {
            return -orderValue;
        } else {
            return orderValue;
        }
    }

    private long geOrderValueByOrderBy(SimpleIntentLevelStatsVO item, BaseStatsQuery condition) {
        if (StringUtils.isBlank(condition.getOrderBy()) || StringUtils.isBlank(condition.getDirection())) {
            return item.getIntentLevelDetailCode();
        }
        if (condition.getOrderBy().contains("reachCount")) {
            return item.getReachCount();
        } else if (condition.getOrderBy().contains("reachCallCount")) {
            return item.getReachCallCount();
        }
        return item.getIntentLevelDetailCode();
    }

    @Override
    public List<SimpleIntentActionStatsVO> queryIntentActionStatsList(BaseStatsQuery condition) {
        if (BooleanUtils.isNotTrue(condition.getEnableStatsInfo())) {
            return Collections.emptyList();
        }
        // 查询所有流程下所有节点的统计数据
        // 直接循环处理各个流程吧, 后面加了缓存后性能应该还好吧
        BotPO bot = botService.selectByKey(condition.getBotId());
        if (Objects.isNull(bot)) {
            return Collections.emptyList();
        }

        List<IntentRuleStatsPO> statsList = ruleStatsService.queryBotAllIntentActionStatsList(condition.getBotId(), condition);
        if (CollectionUtils.isEmpty(statsList)) {
            return Collections.emptyList();
        }

        // 查询所有的意图, 流程, 节点, 特殊语境
        List<IntentRuleActionPO> intentActionRuleList = intentRuleActionService.getIntentRuleActionList(condition.getBotId(), Collections.emptyList());
        Map<String, IntentRuleActionPO> intentActionRuleMap = MyCollectionUtils.listToMap(intentActionRuleList, IntentRuleActionPO::getId);

        List<String> nodeIdList = statsList.stream().map(IntentRuleStatsPO::getNodeId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> knowledgeIdList = statsList.stream().map(IntentRuleStatsPO::getKnowledgeId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<DialogBaseNodePO> nodeList = stepNodeService.getByIdList(condition.getBotId(), nodeIdList);
        Map<String, DialogBaseNodePO> nodeMap = MyCollectionUtils.listToMap(nodeList, DialogBaseNodePO::getId);
        List<KnowledgePO> knowledgeList = knowledgeService.getByIdList(condition.getBotId(), knowledgeIdList);
        Map<String, KnowledgePO> knowledgeMap = MyCollectionUtils.listToMap(knowledgeList, KnowledgePO::getId);
        List<SpecialAnswerConfigPO> specialAnswerConfigList = specialAnswerConfigService.getByBotId(condition.getBotId());
        Map<String, SpecialAnswerConfigPO> specialAnswerConfigMap = MyCollectionUtils.listToMap(specialAnswerConfigList, SpecialAnswerConfigPO::getId);
        List<SimpleIntentActionStatsVO> result = new ArrayList<>();
        int botReceptionCount = botReceptionStatsService.queryBotReceptionCount(condition.getBotId(), condition);
        ActionNameResourceBO actionNameResource = intentRuleActionService.getSourceId2NameMap(botRefService.getTenantIdByBotId(bot.getBotId()));
        ResourceId2NameBO resourceId2NameBO = intentRuleService.getResourceNameMap(condition.getBotId());

        statsList.forEach(stats -> {
            if (Objects.isNull(stats.getSource())) {
                return;
            }
            switch (stats.getSource()) {
                case STEP:
                    DialogBaseNodePO node = nodeMap.get(stats.getNodeId());
                    if (Objects.nonNull(node)
                            && BooleanUtils.isTrue(node.getIsEnableAction())
                            && CollectionUtils.isNotEmpty(node.getActionList())) {
                        SimpleIntentActionStatsVO statsVO = new SimpleIntentActionStatsVO();
                        statsVO.setId(stats.getNodeId());
                        statsVO.setContent(String.format("%s:%s", node.getLabel(), node.getName()));
                        statsVO.setReach(BotStatsUtil.calculatePercent(stats.getCount(), botReceptionCount));
                        statsVO.setActionList(node.getActionList());
                        result.add(statsVO);
                    }
                    break;
                case KNOWLEDGE:
                    KnowledgePO knowledge = knowledgeMap.get(stats.getKnowledgeId());
                    if (Objects.nonNull(knowledge)
                            && BooleanUtils.isTrue(knowledge.getIsEnableAction())
                            && CollectionUtils.isNotEmpty(knowledge.getActionList())) {
                        SimpleIntentActionStatsVO statsVO = new SimpleIntentActionStatsVO();
                        statsVO.setId(stats.getKnowledgeId());
                        statsVO.setContent(String.format("%s:%s", knowledge.getLabel(), knowledge.getName()));
                        statsVO.setReach(BotStatsUtil.calculatePercent(stats.getCount(), botReceptionCount));
                        statsVO.setActionList(knowledge.getActionList());
                        result.add(statsVO);
                    }
                    break;
                case SPECIAL_ANSWER:
                    SpecialAnswerConfigPO specialAnswerConfig = specialAnswerConfigMap.get(stats.getSpecialAnswerConfigId());
                    if (Objects.nonNull(specialAnswerConfig)
                            && BooleanUtils.isTrue(specialAnswerConfig.getIsEnableAction())
                            && CollectionUtils.isNotEmpty(specialAnswerConfig.getActionList())) {
                        SimpleIntentActionStatsVO statsVO = new SimpleIntentActionStatsVO();
                        statsVO.setId(stats.getSpecialAnswerConfigId());
                        statsVO.setContent(String.format("%s:%s", specialAnswerConfig.getLabel(), specialAnswerConfig.getName()));
                        statsVO.setReach(BotStatsUtil.calculatePercent(stats.getCount(), botReceptionCount));
                        statsVO.setActionList(specialAnswerConfig.getActionList());
                        result.add(statsVO);
                    }
                    break;
                case RULE:
                    IntentRuleActionPO rule = intentActionRuleMap.get(stats.getRuleId());
                    if (Objects.nonNull(rule)) {
                        SimpleIntentActionStatsVO statsVO = new SimpleIntentActionStatsVO();
                        statsVO.setId(stats.getRuleId());
                        statsVO.setContent(IntentRuleContentRenderUtils.renderIntentRuleConditionContent(rule.getConditionList(), resourceId2NameBO));
                        statsVO.setReach(BotStatsUtil.calculatePercent(stats.getCount(), botReceptionCount));
                        statsVO.setActionList(rule.getActionList());
                        result.add(statsVO);
                    }
                    break;
                default:
                    break;
            }
        });

        for (SimpleIntentActionStatsVO item : result) {
            intentRuleActionService.addSourceName(item.getActionList(), actionNameResource);
        }
        return result.stream()
                .sorted(Comparator.comparingLong(item -> getStatsOrderValue(item, condition)))
                .collect(Collectors.toList());
    }

    private long getStatsOrderValue(SimpleIntentActionStatsVO item, BaseStatsQuery condition) {
        long orderValue = geOrderValueByOrderBy(item, condition);
        if (ApplicationConstant.SORT_DIRECTION_DESC.equals(condition.getDirection())) {
            return -orderValue;
        } else {
            return orderValue;
        }
    }

    private long geOrderValueByOrderBy(SimpleIntentActionStatsVO item, BaseStatsQuery condition) {
        if (StringUtils.isBlank(condition.getOrderBy()) || StringUtils.isBlank(condition.getDirection())) {
            return 0L;
        }
        if (condition.getOrderBy().contains("reachCount")) {
            return item.getReachCount();
        } else if (condition.getOrderBy().contains("reachCallCount")) {
            return item.getReachCallCount();
        }
        return 0L;
    }
    private void resetNodeIntentBranchStats(DialogBaseNodeVO node, Map<String, List<StepNodeJumpStatsPO>> toNextNodeMap, int total) {
        if (CollectionUtils.isEmpty(node.getIntentInfoList())) {
            return;
        }
        node.getIntentInfoList().forEach(simpleIntent -> {
            List<StepNodeJumpStatsPO> statsList = toNextNodeMap.getOrDefault(simpleIntent.getId(), Collections.emptyList());
            int callCount = statsList.stream().mapToInt(StepNodeJumpStatsPO::getCallCount).sum();
            simpleIntent.setReach(BotStatsUtil.calculatePercent(callCount, total));
        });
    }

    private boolean checkJumpTargetTypeIs(NodeJumpTargetTypeEnum typeEnum, StepNodeJumpStatsPO item) {
        return typeEnum.name().equals(item.getTargetType());
    }

    private boolean checkJumpTargetIsAvailable(StepNodeJumpStatsPO statsItem,
                                               Set<String> knowledgeIdSet,
                                               Set<String> stepIdSet,
                                               Set<String> specialAnswerConfigIdSet,
                                               Set<String> intentIdSet) {

        String jumpTargetType = statsItem.getTargetType();
        String jumpTargetId = statsItem.getTargetId();
        String intentId = statsItem.getIntentId();

        if (StringUtils.equals(jumpTargetType, NodeJumpTargetTypeEnum.KNOWLEDGE.name())) {
            return knowledgeIdSet.contains(jumpTargetId);
        } else if (StringUtils.equals(jumpTargetType, NodeJumpTargetTypeEnum.STEP.name())) {
            return stepIdSet.contains(jumpTargetId);
        } else if (StringUtils.equals(jumpTargetType, NodeJumpTargetTypeEnum.SPECIAL_ANSWER.name())) {
            return specialAnswerConfigIdSet.contains(jumpTargetId);
        } else if (StringUtils.equals(jumpTargetType, NodeJumpTargetTypeEnum.NODE.name())) {
            return intentIdSet.contains(intentId);
        }
        return false;
    }

    private void calculateKnowledgeAnswerStats(IntentTriggerStatsPO statsPO,
                                               IntentTriggerStatsVO statsVO,
                                               List<AnswerStatsPO> answerStatsList) {
        calculateAnswerStats(statsPO, statsVO, answerStatsList);
    }

    private void calculateSpecialAnswerStats(IntentTriggerStatsPO statsPO,
                                               IntentTriggerStatsVO statsVO,
                                               List<AnswerStatsPO> answerStatsList) {
        calculateAnswerStats(statsPO, statsVO, answerStatsList);
    }

    private void calculateAnswerStats(IntentTriggerStatsPO statsPO,
                                               IntentTriggerStatsVO statsVO,
                                               List<AnswerStatsPO> answerStatsList) {
        int customerHangupCount = answerStatsList.stream().mapToInt(AnswerStatsPO::getTotalHangupCount).sum();
        int declineCount = answerStatsList.stream().mapToInt(AnswerStatsPO::getDeclineCallCount).sum();
        statsVO.setCustomerHangup(BotStatsUtil.calculatePercent(customerHangupCount, statsPO.getCallCount()));
        statsVO.setDecline(BotStatsUtil.calculatePercent(declineCount, statsPO.getCallCount()));
    }

    private void wrapKnowledgeAnswerStats(IntentTriggerStatsPO statsPO,
                                      KnowledgeStatsVO statsVO,
                                      List<AnswerStatsPO> answerStatsList) {
        int customerHangupCount = answerStatsList.stream().mapToInt(AnswerStatsPO::getTotalHangupCount).sum();
        int declineCount = answerStatsList.stream().mapToInt(AnswerStatsPO::getDeclineCallCount).sum();
        statsVO.setCustomerHangup(BotStatsUtil.calculatePercent(customerHangupCount, statsPO.getCallCount()));
        statsVO.setDecline(BotStatsUtil.calculatePercent(declineCount, statsPO.getCallCount()));
    }

    private List<IntentTriggerStatsPO> cleanDeletedData(List<IntentTriggerStatsPO> statsList,
                                                        List<KnowledgePO> knowledgeList,
                                                        List<SpecialAnswerConfigPO> specialAnswerConfigList,
                                                        List<StepPO> stepList) {
        if (CollectionUtils.isEmpty(statsList)) {
            return Collections.emptyList();
        }

        return cleanDeletedData(statsList,
                knowledgeList.stream().map(KnowledgePO::getId).collect(Collectors.toSet()),
                specialAnswerConfigList.stream().map(SpecialAnswerConfigPO::getId).collect(Collectors.toSet()),
                stepList.stream().map(StepPO::getId).collect(Collectors.toSet()));
    }

    private List<IntentTriggerStatsPO> cleanDeletedData(List<IntentTriggerStatsPO> statsList,
                                                        Set<String> knowledgeIdSet,
                                                        Set<String> specialAnswerConfigIdSet,
                                                        Set<String> stepIdSet) {
        if (CollectionUtils.isEmpty(statsList)) {
            return Collections.emptyList();
        }
        return statsList.stream().filter(item -> {
            if (AnswerSourceEnum.SPECIAL_ANSWER.name().equals(item.getTargetType())) {
                return CollectionUtils.isNotEmpty(specialAnswerConfigIdSet) && specialAnswerConfigIdSet.contains(item.getTargetId());
            }
            if (AnswerSourceEnum.KNOWLEDGE.name().equals(item.getTargetType())) {
                return CollectionUtils.isNotEmpty(knowledgeIdSet) && knowledgeIdSet.contains(item.getTargetId());
            }
            if (AnswerSourceEnum.STEP.name().equals(item.getTargetType())) {
                return CollectionUtils.isNotEmpty(stepIdSet) && stepIdSet.contains(item.getTargetId());
            }
            return false;
        }).collect(Collectors.toList());
    }


}
