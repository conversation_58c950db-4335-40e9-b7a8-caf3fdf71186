package com.yiwise.dialogflow.service.impl;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.entitycollect.EntityService;
import com.yiwise.dialogflow.service.intent.IntentService;
import com.yiwise.dialogflow.service.remote.IntentLevelTagDetailService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class DependResourceServiceImpl implements DependResourceService {

    @Lazy
    @Resource
    private IntentService intentService;

    @Lazy
    @Resource
    private StepService stepService;

    @Lazy
    @Resource
    private StepNodeService stepNodeService;

    @Lazy
    @Resource
    private VariableService variableService;

    @Lazy
    @Resource
    private KnowledgeService knowledgeService;

    @Resource
    private GroupService groupService;

    @Resource
    private IntentLevelTagDetailService intentLevelTagDetailService;

    @Lazy
    @Resource
    private BotService botService;

    @Lazy
    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    @Lazy
    @Resource
    private EntityService entityService;

    @Override
    public DependentResourceBO generateByBotId(Long botId) {
        DependentResourceBO.Condition condition = new DependentResourceBO.Condition(botId);
        condition.node().intent().variable().knowledge().step().entity();
        return generateByCondition(condition);
    }

    @Override
    public DependentResourceBO generateBySnapshot(RobotSnapshotPO snapshot) {
        DependentResourceBO result = new DependentResourceBO();
        Long botId = snapshot.getBotId();
        prepareIntentResource(botId, snapshot.getIntentList(), result);
        prepareKnowledgeResource(botId, snapshot.getKnowledgeList(), result);
        prepareStepResource(botId, snapshot.getStepList(), result);
        prepareNodeResource(botId, snapshot.getNodeList(), result);
        prepareVariableResource(botId, snapshot.getVariableList(), result);
        prepareGroupResource(botId, snapshot.getGroupList(), result);
        prepareIntentLevelResource(snapshot.getIntentLevelDetailCode2NameMap(), result);
        prepareSpecialAnswerConfigResource(botId, snapshot.getSpecialAnswerConfigList(), result);
        prepareEntity(botId, snapshot.getEntityList(), result);
        prepareLlmLabel(botId, snapshot.getLlmLabelList(), result);
        return result;
    }

    private void prepareLlmLabel(Long botId, List<LlmLabelPO> llmLabelList, DependentResourceBO resource) {
        if (CollectionUtils.isEmpty(llmLabelList)) {
            resource.setLlmLabelId2NameMap(Collections.emptyMap());
        }
        resource.setLlmLabelId2NameMap(MyCollectionUtils.listToMap(llmLabelList, LlmLabelPO::getId, LlmLabelPO::getName));
    }

    private void prepareEntity(Long botId, List<BaseEntityPO> entityList, DependentResourceBO result) {
        for (BaseEntityPO entity : entityList) {
            result.getEntityId2NameMap().put(entity.getId(), entity.getName());
        }
    }

    private void prepareIntentLevelResource(Map<Integer, String> intentLevelDetailCode2NameMap, DependentResourceBO resource) {
        if (intentLevelDetailCode2NameMap.isEmpty()) {
            return;
        }
        resource.setIntentLevelIdNameMap(intentLevelDetailCode2NameMap);
    }

    @Override
    public DependentResourceBO generateByCondition(DependentResourceBO.Condition condition) {
        DependentResourceBO resource = new DependentResourceBO();
        Long botId = condition.getBotId();
        if (BooleanUtils.isTrue(condition.getIntent())) {
            List<IntentPO> intentList = intentService.getAllByBotId(botId);
            prepareIntentResource(botId, intentList, resource);
        }
        if (BooleanUtils.isTrue(condition.getKnowledge())) {
            List<KnowledgePO> knowledgeList = knowledgeService.getAllListByBotId(botId);
            prepareKnowledgeResource(botId, knowledgeList, resource);
        }
        List<StepPO> stepList = null;
        if (BooleanUtils.isTrue(condition.getStep())) {
            stepList = stepService.getAllListByBotId(botId);
            prepareStepResource(botId, stepList, resource);
        }
        if (BooleanUtils.isTrue(condition.getNode())) {
            if (CollectionUtils.isEmpty(stepList)) {
                stepList = stepService.getAllListByBotId(botId);
            }
            List<DialogBaseNodePO> nodeList = stepNodeService.getListByStepIdList(botId, stepList.stream().map(StepPO::getId).collect(Collectors.toList()));
            prepareNodeResource(botId, nodeList, resource);
        }
        if (BooleanUtils.isTrue(condition.getVariable())) {
            List<VariablePO> variableList = variableService.getListByBotId(botId);
            prepareVariableResource(botId, variableList, resource);
        }
        if (BooleanUtils.isTrue(condition.getGroup())) {
            List<GroupPO> groupList = groupService.getListByBotId(botId);
            prepareGroupResource(botId, groupList, resource);
        }
        if (BooleanUtils.isTrue(condition.getSpecialAnswerConfig())) {
            List<SpecialAnswerConfigPO> specialAnswerConfigList = specialAnswerConfigService.getByBotId(botId);
            prepareSpecialAnswerConfigResource(botId, specialAnswerConfigList, resource);
        }
        if (BooleanUtils.isTrue(condition.getEntity())) {
            resource.setEntityId2NameMap(entityService.getAllEntityIdNameMapByBotId(botId));
        }
        if (BooleanUtils.isTrue(condition.getIntentLevel())) {
            BotPO botPO = botService.selectByKey(botId);
            if (Objects.nonNull(botPO)) {
                Long intentLevelTagId = botPO.getIntentLevelTagId();
                Map<Integer, String> intentLevelId2NameMap = intentLevelTagDetailService.getIntentLevelTagDetailCode2NameMap(intentLevelTagId);
                resource.setIntentLevelIdNameMap(intentLevelId2NameMap);
            }
        }
        return resource;
    }

    private void prepareSpecialAnswerConfigResource(Long botId, List<SpecialAnswerConfigPO> specialAnswerConfigList, DependentResourceBO resource) {
        if (CollectionUtils.isEmpty(specialAnswerConfigList)) {
            return;
        }
        Map<String, String> specialAnswerConfigMap = MyCollectionUtils.listToConvertMap(specialAnswerConfigList, SpecialAnswerConfigPO::getId, SpecialAnswerConfigPO::getName);
        resource.setSpecialAnswerIdNameMap(specialAnswerConfigMap);
        resource.setSpecialAnswerNameToIdMap(MyCollectionUtils.listToConvertMap(specialAnswerConfigList, SpecialAnswerConfigPO::getName, SpecialAnswerConfigPO::getId));
    }

    private void prepareGroupResource(Long botId, List<GroupPO> groupList, DependentResourceBO resource){
        if (CollectionUtils.isEmpty(groupList)){
            return;
        }
        resource.setGroupIdTypeMap(MyCollectionUtils.listToMap(groupList, GroupPO::getId,GroupPO::getType));
    }

    private void prepareIntentResource(Long botId, List<IntentPO> intentList, DependentResourceBO resource) {
        Map<String, String> intentIdNameMap = new HashMap<>();
        intentIdNameMap.put(ApplicationConstant.DEFAULT_INTENT_ID, ApplicationConstant.DEFAULT_INTENT_NAME);
        intentIdNameMap.put(ApplicationConstant.USER_SILENCE_INTENT_ID, ApplicationConstant.USER_SILENCE_INTENT_NAME);
        intentIdNameMap.put(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID, ApplicationConstant.COLLECT_SUCCESS_INTENT_NAME);
        intentIdNameMap.put(ApplicationConstant.COLLECT_FAILED_INTENT_ID, ApplicationConstant.COLLECT_FAILED_INTENT_NAME);
        if (CollectionUtils.isNotEmpty(intentList)) {
            intentIdNameMap.putAll(MyCollectionUtils.listToConvertMap(intentList, IntentPO::getId, IntentPO::getName));
        }
        resource.setIntentIdNameMap(intentIdNameMap);
    }

    private void prepareVariableResource(Long botId, List<VariablePO> variableList, DependentResourceBO resource) {
        if (CollectionUtils.isEmpty(variableList)) {
            return;
        }
        resource.setVariableNameIdMap(MyCollectionUtils.listToConvertMap(variableList, VariablePO::getName, VariablePO::getId));
        resource.setVariableIdNameMap(MyCollectionUtils.listToConvertMap(variableList, VariablePO::getId, VariablePO::getName));
        resource.setVarIdTypeMap(MyCollectionUtils.listToConvertMap(variableList, VariablePO::getId, VariablePO::getType));
    }

    private void prepareKnowledgeResource(Long botId, List<KnowledgePO> knowledgeList, DependentResourceBO resource) {
        if (CollectionUtils.isEmpty(knowledgeList)) {
            return;
        }
        resource.setKnowledgeIdNameMap(MyCollectionUtils.listToConvertMap(knowledgeList, KnowledgePO::getId, KnowledgePO::getName));
        resource.setKnowledgeNameToIdMap(MyCollectionUtils.listToConvertMap(knowledgeList, KnowledgePO::getName, KnowledgePO::getId));
    }

    private void prepareStepResource(Long botId, List<StepPO> stepList, DependentResourceBO resource) {
        if (CollectionUtils.isEmpty(stepList)) {
            return;
        }
        resource.setStepIdNameMap(MyCollectionUtils.listToConvertMap(stepList, StepPO::getId, StepPO::getName));
        resource.setStepNameToIdMap(MyCollectionUtils.listToConvertMap(stepList, StepPO::getName, StepPO::getId));
        resource.setStepMap(MyCollectionUtils.listToConvertMap(stepList, StepPO::getId, item -> item));
    }

    private void prepareNodeResource(Long botId, List<DialogBaseNodePO> nodeList, DependentResourceBO resource) {
        if (CollectionUtils.isEmpty(nodeList)) {
            return;
        }
        resource.setNodeIdNameMap(MyCollectionUtils.listToConvertMap(nodeList, DialogBaseNodePO::getId, DialogBaseNodePO::getName));
        resource.setNodeIdNameMap(MyCollectionUtils.listToConvertMap(nodeList, DialogBaseNodePO::getId, item -> String.format("%s:%s", item.getLabel(), item.getName())));
    }




}
