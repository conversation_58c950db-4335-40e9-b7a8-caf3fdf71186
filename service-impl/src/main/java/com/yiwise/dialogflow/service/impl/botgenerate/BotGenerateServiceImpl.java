package com.yiwise.dialogflow.service.impl.botgenerate;

import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.common.utils.file.MyFileUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.TempFilePathKeyCenter;
import com.yiwise.dialogflow.entity.enums.BotCreateSourceEnum;
import com.yiwise.dialogflow.entity.enums.RewriteTaskSourceEnum;
import com.yiwise.dialogflow.entity.po.BotAnswerRewriteTaskPO;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.query.AnswerQuery;
import com.yiwise.dialogflow.entity.vo.BotCreateRequestVO;
import com.yiwise.dialogflow.entity.vo.RewriteAnswerDetailVO;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotCopyAndRewriteRequestVO;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotRewritePromptInfoVO;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotRewriteRequestVO;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.botgenerate.BotAnswerRewriteTaskService;
import com.yiwise.dialogflow.service.botgenerate.BotRewriteService;
import com.yiwise.dialogflow.utils.TextDiffUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BotGenerateServiceImpl implements BotRewriteService {

    static final List<String> headerSet = Arrays.asList("开场白", "主利益点", "挽回利益点", "链路", "肯定挂机", "拒绝挂机", "默认挂机");
    static final Set<String> onlyOneSentenceHeaderSet = new HashSet<>(Arrays.asList("主利益点", "挽回利益点", "链路"));

    @Resource
    private BotAnswerRewriteTaskService botAnswerRewriteService;

    @Resource
    private BotService botService;

    @Override
    public List<BotAnswerRewriteTaskPO> copyAndRewrite(BotCopyAndRewriteRequestVO request, Long userId) {
        if (!botService.checkCanRewrite(request.getSrcBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "生成/改写失败, 节点内容为空");
        }
        request.setUserId(userId);
        BotPO templateBot = botService.getById(request.getSrcBotId());
        if (Objects.isNull(templateBot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "模板bot不存在");
        }

        if (CollectionUtils.isEmpty(request.getOfferInfo())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "offer信息不能为空");
        }

        List<BotRewritePromptInfoVO> promptInfoList = request.getOfferInfo();

        validate(promptInfoList, false);

        for (BotRewritePromptInfoVO botRewritePromptInfo : promptInfoList) {
            if (CollectionUtils.isNotEmpty(botRewritePromptInfo.getParseFailMsgList())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("%s %s", botRewritePromptInfo.getBotName(), StringUtils.join(botRewritePromptInfo.getParseFailMsgList(), "; ")));
            }
        }

        List<BotAnswerRewriteTaskPO> result = new ArrayList<>();

        Map<Long, BotRewritePromptInfoVO> tmpBotInfoMap = new HashMap<>();
        // excel中一行对应一个PromptInfo对象, 也对应一个bot
        try {
            for (BotRewritePromptInfoVO botRewritePromptInfoVO : request.getOfferInfo()) {
                BotCreateRequestVO botCreateRequest = MyBeanUtils.copy(request, BotCreateRequestVO.class);
                botCreateRequest.setName(botRewritePromptInfoVO.getBotName());
                botCreateRequest.setCreateSource(BotCreateSourceEnum.GENERATE_REWRITE);
                BotPO bot;
                try {
                    log.info("create bot:{}", botCreateRequest);
                    bot = botService.create(botCreateRequest);
                    log.info("create bot success:{}", bot);
                } catch (Exception e) {
                    if (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().contains("话术名已被占用")) {
                        botCreateRequest.setName(String.format("%s_%s", botCreateRequest.getName(), System.currentTimeMillis()));
                        bot = botService.create(botCreateRequest);
                        log.info("create bot success:{}", bot);
                    } else {
                        throw e;
                    }
                }
                tmpBotInfoMap.put(bot.getBotId(), botRewritePromptInfoVO);
            }
        } catch (Exception e) {
            log.warn("Failed to create bot", e);
            // 删除已经创建的bot
            if (MapUtils.isNotEmpty(tmpBotInfoMap)) {
                tmpBotInfoMap.forEach((botId, botRewritePromptInfoVO) -> {
                    botService.delete(botId, userId);
                });
            }
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "Failed to create bot", e);
        }

        tmpBotInfoMap.forEach((botId, botRewritePromptInfoVO) -> {
            BotRewriteRequestVO rewriteRequest = new BotRewriteRequestVO();
            rewriteRequest.setBotId(botId);
            rewriteRequest.setTemplateBotId(request.getSrcBotId());
            rewriteRequest.setOfferMap(botRewritePromptInfoVO.getOfferMap());
            rewriteRequest.setSource(RewriteTaskSourceEnum.AI_GENERATE);
            result.add(botAnswerRewriteService.submitRewriteTask(rewriteRequest, request.getUserId()));
        });

        return result;
    }

    @Override
    public BotAnswerRewriteTaskPO rewrite(BotRewriteRequestVO request, Long userId) {
        if (!botService.checkCanRewrite(request.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "生成/改写失败, 节点内容为空");
        }
        BotRewritePromptInfoVO promptInfo = new BotRewritePromptInfoVO();
        promptInfo.setOfferMap(request.getOfferMap());
        promptInfo.setParseFailMsgList(new ArrayList<>());
        validate(Collections.singletonList(promptInfo), true);
        if (CollectionUtils.isNotEmpty(promptInfo.getParseFailMsgList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, StringUtils.join(promptInfo.getParseFailMsgList(), "; "));
        }
        if (StringUtils.isNotBlank(request.getDescription())) {
            botService.updateDescription(request.getBotId(), request.getDescription());
        }
        request.setSource(RewriteTaskSourceEnum.AI_REWRITE);
        return botAnswerRewriteService.submitRewriteTask(request, userId);
    }

    @Override
    public List<BotRewritePromptInfoVO> uploadAndParsePromptInfo(MultipartFile excelFile) {
        List<BotRewritePromptInfoVO> parseResultList = new ArrayList<>();

        String localTempFilePath = TempFilePathKeyCenter.getBotGenerateExcelTemplateFilePath();
        try {
            File file = new File(localTempFilePath);
            MyFileUtils.makeFileExist(localTempFilePath);
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(excelFile.getBytes());
            fos.close();

            FileInputStream fis = new FileInputStream(file);
            Workbook workbook = new XSSFWorkbook(fis);
            Sheet sheet = workbook.getSheetAt(0);

            List<String> titleNameList = new ArrayList<>();
            Row headerRow = sheet.getRow(1);
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                titleNameList.add(getCellStringValue(headerRow.getCell(i)));
            }
            log.info("titleNameList:{}", titleNameList);
            if (CollectionUtils.size(titleNameList) != headerSet.size() + 1) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "话术offer模板格式错误, 需要" + (headerSet.size() + 1) + "列, 实际为" + CollectionUtils.size(titleNameList) + "列");
            }
            for (int i = 0; i < titleNameList.size(); i++) {
                String title = titleNameList.get(i);
                if (i == 0) {
                    if (!"话术名称".equals(title)) {
                        throw new ComException(ComErrorCode.VALIDATE_ERROR, "话术offer模板格式错误, 第1列必须为:话术名称");
                    }
                } else {
                    if (!headerSet.get(i - 1).equals(title)) {
                        throw new ComException(ComErrorCode.VALIDATE_ERROR, "话术offer模板格式错误, 第" + (i + 1) + "列必须为:" + headerSet.get(i - 1));
                    }
                }
            }

            BotRewritePromptInfoVO botRewritePromptInfo = null;
            for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                Cell botNameCell = row.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                if (StringUtils.isNotBlank(getCellStringValue(botNameCell)) || botRewritePromptInfo == null) {
                    botRewritePromptInfo = new BotRewritePromptInfoVO();
                    botRewritePromptInfo.setBotName(getCellStringValue(botNameCell));
                    botRewritePromptInfo.setOfferMap(new HashMap<>());
                    parseResultList.add(botRewritePromptInfo);
                }
                for (int j = 1; j < row.getLastCellNum(); j++) {
                    String title = titleNameList.get(j);
                    String value = getCellStringValue(row.getCell(j, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
                    if (StringUtils.isNotBlank(value)) {
                        botRewritePromptInfo.getOfferMap().computeIfAbsent(title, k -> new ArrayList<>()).add(value);
                    }
                }
            }
            fis.close();
        } catch (IOException e) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "Failed to parse excel file", e);
        } finally {
            MyFileUtils.deleteFileByPath(localTempFilePath);
        }
        validate(parseResultList, false);
        return parseResultList;
    }

    @Override
    public Map<String, List<String>> getOrGenPromptInfo(Long botId) {
        Optional<BotAnswerRewriteTaskPO> lastTask = botAnswerRewriteService.getLastTaskByBotId(botId);
        Map<String, List<String>> result = new HashMap<>();
        if (lastTask.isPresent()) {
            lastTask.ifPresent(task -> {
                if (MapUtils.isNotEmpty(task.getRewritePromptMap())) {
                    result.putAll(task.getRewritePromptMap());
                }
            });
        } else {
            // 请求关键句解析
            Map<String, List<String>> analyzePromptResult = botAnswerRewriteService.analyzePrompt(botId);
            result.putAll(analyzePromptResult);
        }
        return result;
    }

    @Override
    public PageResultObject<RewriteAnswerDetailVO> queryLastRewriteDetailByCondition(AnswerQuery condition) {
        // 查询最后一次改写成功的任务
        Optional<BotAnswerRewriteTaskPO> lastTaskOpt = botAnswerRewriteService.getLastApplySuccessTaskByBotId(condition.getBotId());
        if (!lastTaskOpt.isPresent()) {
            return PageResultObject.of(Collections.emptyList());
        }

        // 查询当前所有的答案文本信息
        List<BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> realtimeGroupList = botAnswerRewriteService.getRealtimeAnswerGroup(condition.getBotId());

        // 所有的改写记录
        List<BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> rewriteGroupList = lastTaskOpt.get().getSuccessAnswerContentList();

        // 对答案文本进行映射
        List<RewriteAnswerDetailVO> result = new ArrayList<>();

        Map<String, BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> realtimeGroupMap = MyCollectionUtils
                .listToMap(realtimeGroupList, BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup::getGroupKey);

        rewriteGroupList.forEach(rewriteGroup -> {
            BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup realGroup = realtimeGroupMap.get(rewriteGroup.getGroupKey());
            if (realGroup == null) {
                log.warn("realGroup is null, rewriteGroup:{}", rewriteGroup);
                return;
            }
            result.addAll(processOneGroup(rewriteGroup, realGroup));
        });

        // 进行查询过滤
        List<RewriteAnswerDetailVO> list = result.stream()
                .filter(item -> {
                    if (CollectionUtils.isEmpty(condition.getAnswerSourceSet())) {
                        return true;
                    }
                    return condition.getAnswerSourceSet().contains(item.getLocate().getAnswerSource());
                })
                .filter(item -> !StringUtils.equals(item.getOriginalAnswer(), item.getRewriteAnswer()))
                .filter(item -> matchSearch(item, condition.getSearch()))
                // 进行文本 diff
                .peek(item -> {
                    String originAnswer = item.getRealtimeAnswer();
                    if (StringUtils.equals(item.getRewriteAnswer(), item.getRealtimeAnswer())) {
                        originAnswer = item.getOriginalAnswer();
                    }
                    item.setDiffInfo(TextDiffUtil.diffInfo(originAnswer, item.getRealtimeAnswer()));
                })
                // 按照 realTimeAnswer 进行排序
                .sorted(Comparator.comparing(RewriteAnswerDetailVO::getRealtimeAnswer))
                .collect(Collectors.toList());

        return PageResultObject.of(list.stream()
                .skip((long) condition.getPageSize() * (condition.getPageNum() - 1))
                .limit(condition.getPageSize())
                .collect(Collectors.toList()),
                condition.getPageNum(),
                condition.getPageSize(),
                list.size()
        );
    }

    private static List<RewriteAnswerDetailVO> processOneGroup(BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup rewriteGroup,
                                                               BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup realGroup) {
        List<RewriteAnswerDetailVO> result = new ArrayList<>();
        Map<String, BotAnswerRewriteTaskPO.AnswerRewriteDetail> realDetailIdMap = new HashMap<>();
        realGroup.getDetailList().forEach(item -> realDetailIdMap.put(item.getLocate().getAnswerId(), item));

        Map<Integer, BotAnswerRewriteTaskPO.AnswerRewriteDetail> realDetailIndexMap = new HashMap<>();
        realGroup.getDetailList().forEach(item -> realDetailIndexMap.put(item.getLocate().getIndex(), item));

        // 处理非新增的答案
        rewriteGroup.getDetailList().stream()
                .filter(item -> BooleanUtils.isNotTrue(item.isDeleteAnswer()))
                .filter(item -> BooleanUtils.isNotTrue(item.isNewAnswer()))
                .forEach(item -> {
                    BotAnswerRewriteTaskPO.AnswerRewriteDetail realDetail = realDetailIdMap.get(item.getLocate().getAnswerId());
                    if (Objects.nonNull(realDetail)) {
                        RewriteAnswerDetailVO answerDetail = new RewriteAnswerDetailVO();
                        answerDetail.setOriginalAnswer(item.getAnswerContent().getText());
                        answerDetail.setRewriteAnswer(item.getRewriteResult());
                        answerDetail.setRealtimeAnswer(realDetail.getAnswerContent().getText());
                        answerDetail.setLocate(realDetail.getLocate());
                        result.add(answerDetail);
                    } else {
                        // 改写的答案已经被删除了
                        log.info("改写的答案已经被删除了:{}", item.getAnswerContent().getText());
                    }
                });

        // 处理新增的答案
        for (int i = 0; i < rewriteGroup.getDetailList().size(); i++) {
            BotAnswerRewriteTaskPO.AnswerRewriteDetail rewriteDetail = rewriteGroup.getDetailList().get(i);
            if (BooleanUtils.isTrue(rewriteDetail.isNewAnswer())) {
                BotAnswerRewriteTaskPO.AnswerRewriteDetail realDetail = realDetailIndexMap.get(i);
                // todo 目前对于新增的答案是按照顺序进行匹配的
                if (Objects.nonNull(realDetail)) {
                    RewriteAnswerDetailVO answerDetail = new RewriteAnswerDetailVO();
                    answerDetail.setOriginalAnswer("");
                    answerDetail.setRewriteAnswer(rewriteDetail.getRewriteResult());
                    answerDetail.setRealtimeAnswer(realDetail.getAnswerContent().getText());
                    answerDetail.setLocate(realDetail.getLocate());
                    result.add(answerDetail);
                }
            }
        }
        return result;
    }

    private boolean matchSearch(RewriteAnswerDetailVO detail, String search) {
        if (StringUtils.isBlank(search)) {
            return true;
        }
        BiFunction<String, String, Boolean> stringContains = (source, target) -> {
            if (StringUtils.isBlank(source) || StringUtils.isBlank(target)) {
                return false;
            }
            return source.contains(target);
        };
        return stringContains.apply(detail.getOriginalAnswer(), search)
                || stringContains.apply(detail.getRewriteAnswer(), search)
                || stringContains.apply(detail.getRealtimeAnswer(), search)
                || stringContains.apply(detail.getLocate().getDisplayName(), search)
                || stringContains.apply(detail.getLocate().getDisplayLabelName(), search);
    }

    private String getCellStringValue(Cell cell) {
        if (Objects.isNull(cell)) {
            return "";
        }
        DataFormatter dataFormatter = new DataFormatter();
        return dataFormatter.formatCellValue(cell);
    }

    private void validate(List<BotRewritePromptInfoVO> botRewritePromptInfoList,
                          boolean ignoreNameValidate) {
        if (CollectionUtils.isEmpty(botRewritePromptInfoList)) {
            return;
        }
        for (BotRewritePromptInfoVO promptInfoVO : botRewritePromptInfoList) {
            if (MapUtils.isNotEmpty(promptInfoVO.getOfferMap())) {
                promptInfoVO.getOfferMap().forEach((k, v) -> v.removeIf(StringUtils::isBlank));
            }
        }

        for (int i = 0; i < botRewritePromptInfoList.size(); i++) {
            BotRewritePromptInfoVO botRewritePromptInfo = botRewritePromptInfoList.get(i);
            botRewritePromptInfo.setParseFailMsgList(new ArrayList<>());
            String botName = botRewritePromptInfo.getBotName();
            if (!ignoreNameValidate) {
                if (StringUtils.isBlank(botName)) {
                    botRewritePromptInfo.getParseFailMsgList().add("bot名称不能为空");
                } else if (botName.length() > 50) {
                    botRewritePromptInfo.getParseFailMsgList().add("bot名称长度不能超过50");
                }
            }
            Set<String> key = new HashSet<>();
            for (String k : botRewritePromptInfo.getOfferMap().keySet()) {
                List<String> v = botRewritePromptInfo.getOfferMap().get(k);
                if (CollectionUtils.isNotEmpty(v)) {
                    key.add(k);
                }
            }
            if (key.stream().noneMatch(headerSet::contains)) {
                botRewritePromptInfo.getParseFailMsgList().add(String.format("%s 无文案内容", botName));
            }
            botRewritePromptInfo.getOfferMap().forEach((k, list) -> {
                if (CollectionUtils.isNotEmpty(list)) {
                    for (String answer : list) {
                        if (StringUtils.length(answer) > 500) {
                            botRewritePromptInfo.getParseFailMsgList().add( k + "内容超过500字");
                        }
                    }
                }
                if (CollectionUtils.size(list) > 1 && onlyOneSentenceHeaderSet.contains(k)) {
                    botRewritePromptInfo.getParseFailMsgList().add(k + "不支持切换话术");
                }
            });
            if (i >= 100) {
                botRewritePromptInfo.getParseFailMsgList().add("一次最多支持生成100个bot");
            }
        }
    }


}