package com.yiwise.dialogflow.batch.listener;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/6/13
 */
@Slf4j
@Component
public class MD<PERSON>eplaceListener implements JobExecutionListener {

    @Override
    public void beforeJob(JobExecution jobExecution) {
        String oldLogId = MDC.get("MDC_LOG_ID");
        String newLogId = jobExecution.getJobParameters().getString("MDC_LOG_ID");
        MDC.put("MDC_LOG_ID", newLogId);
        log.info("{}==>{}", oldLogId, newLogId);
    }

    @Override
    public void afterJob(JobExecution jobExecution) {
        MDC.remove("MDC_LOG_ID");
    }
}
