package com.yiwise.dialogflow.mapper;

import com.yiwise.dialogflow.entity.po.asrmodel.AsrProviderPO;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrSelfLearningDetailPO;
import com.yiwise.dialogflow.entity.query.AsrSelfLearningDetailQuery;
import com.yiwise.dialogflow.entity.query.AsrVocabDetailQuery;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrSelfLearningDetailVO;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrVocabDetailVO;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/25
 */
public interface AsrSelfLearningPOMapper extends Mapper<AsrSelfLearningDetailPO> {
    List<AsrSelfLearningDetailVO> listByCondition(@Param("query") AsrSelfLearningDetailQuery query);

    List<AsrSelfLearningDetailPO> getByName(@Param("name") String name);
}
