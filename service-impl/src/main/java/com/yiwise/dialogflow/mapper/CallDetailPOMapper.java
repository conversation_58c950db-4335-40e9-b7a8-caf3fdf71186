package com.yiwise.dialogflow.mapper;

import com.yiwise.dialogflow.entity.po.callout.CallDetailPO;
import com.yiwise.dialogflow.entity.po.semantic.analysis.SemanticAnalysisConditionPO;
import com.yiwise.middleware.mysql.aop.annotation.TargetDataSource;
import com.yiwise.middleware.mysql.common.DataSourceEnum;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/29
 */
@TargetDataSource(DataSourceEnum.BASE_ADB)
public interface CallDetailPOMapper extends Mapper<CallDetailPO> {

    List<CallDetailPO> selectRecords(@Param("conditionPO") SemanticAnalysisConditionPO conditionPO);

    int countRecords(@Param("conditionPO") SemanticAnalysisConditionPO conditionPO);
}
