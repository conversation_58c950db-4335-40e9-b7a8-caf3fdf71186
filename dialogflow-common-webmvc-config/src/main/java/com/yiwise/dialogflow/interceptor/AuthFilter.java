package com.yiwise.dialogflow.interceptor;

import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.NetUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.dialogflow.service.remote.IPWhiteListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

import static com.yiwise.base.common.helper.ServerInfoConstants.API_SUCCESS;

@Slf4j
public class AuthFilter extends OncePerRequestFilter {

    private static final String DEFAULT_CONTENT_TYPE = "application/json;charset=UTF-8";

    private final IPWhiteListService ipWhiteListService = AppContextUtils.getBean(IPWhiteListService.class);

    private final List<String> forbiddenUrlPattern;

    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    public AuthFilter() {
        forbiddenUrlPattern = Collections.emptyList();
    }

    public AuthFilter(List<String> forbiddenUrlPattern) {
        this.forbiddenUrlPattern = forbiddenUrlPattern;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {
        String remoteIp = NetUtils.getRemoteIpAddress(request);
        log.info("remoteIp:{}", remoteIp);
        if (isForbiddenUrl(request.getRequestURI())) {
            writeErrorMsg(response, ResultObject.fail(ComErrorCode.FORBIDDEN, "对话服务不支持此接口调用"));
            return;
        }
        if (ipWhiteListService.checkIsInWhiteList(remoteIp)) {
            filterChain.doFilter(request, response);
        } else {
            writeErrorMsg(response, ResultObject.fail(ComErrorCode.USER_NOT_LOGIN, "仅支持在内网环境调用"));
        }
    }

    private boolean isForbiddenUrl(String url) {
        return forbiddenUrlPattern.stream().anyMatch(item -> antPathMatcher.match(item, url));
    }

    public static void writeErrorMsg(HttpServletResponse response, ResultObject ResultObject) throws IOException {
        response.setContentType(DEFAULT_CONTENT_TYPE);
        response.setHeader(API_SUCCESS, "false");
        try (ServletOutputStream writer = response.getOutputStream()) {
            writer.write(JsonUtils.object2String(ResultObject).getBytes(StandardCharsets.UTF_8));
        }
    }
}
