package com.yiwise.dialogflow.aop;

import com.yiwise.base.monitoring.helper.CommonWebLogAspect;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * Created on 2017/03/16
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Order(1)
public class ControllerLogAspect extends CommonWebLogAspect {

    @Pointcut("@within(org.springframework.stereotype.Controller) && execution(* com.yiwise.dialogflow.controller..*.*(..)) ")
    public void controller() {
    }

    @Pointcut("@within(org.springframework.web.bind.annotation.RestController) && execution(* com.yiwise.dialogflow.controller..*.*(..)) ")
    public void restController() {
    }

    @Override
    @Around("(controller() || restController()) && !noLogPointcut()")
    public Object doRestControllerAroundMethod(ProceedingJoinPoint pig) throws Throwable {
        return super.doRestControllerAroundMethod(pig);
    }

    @Pointcut("@within(org.springframework.stereotype.Controller) && execution(* com.yiwise.dialogflow.websocket.controller..*.*(..)) ")
    public void webSocketController() {
    }

    @Pointcut("@within(org.springframework.web.bind.annotation.RestController) && execution(* com.yiwise.dialogflow.websocket.controller..*.*(..)) ")
    public void webSocketRestController() {
    }

    @Override
    @Around("(webSocketController() || webSocketRestController())")
    public Object doWebSocketControllerAroundMethod(ProceedingJoinPoint pig) throws Throwable {
        return super.doWebSocketControllerAroundMethod(pig);
    }

}
