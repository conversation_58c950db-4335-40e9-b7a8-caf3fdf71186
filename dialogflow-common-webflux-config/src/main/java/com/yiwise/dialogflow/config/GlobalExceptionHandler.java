package com.yiwise.dialogflow.config;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import reactor.core.publisher.Mono;

@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(Exception.class)
    public ResponseEntity<String> handleException(Exception e) {
        ComException ComException;
        if (e instanceof ComException) {
            ComException = (ComException) e;
        } else {
            ComException = new ComException(ComErrorCode.UNKNOWN_ERROR, e.getMessage(), e);
        }
        log.error("[LogHub_Warn]GlobalExceptionHandler.handleException", e);
        ResultObject<Object> resultObject = ResultObject.fail(ComException);
        String result = JsonUtils.object2String(resultObject);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .header("Content-Type", "application/json;charset=UTF-8")
                .body(result);
    }
}
