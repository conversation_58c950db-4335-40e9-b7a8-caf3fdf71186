package com.yiwise.dialogflow.config;

import com.yiwise.base.common.helper.ServerInfoConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.context.annotation.*;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.support.ExecutorSubscribableChannel;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.server.WebHandler;

import javax.annotation.PreDestroy;

@Slf4j
@Configuration
@Order(value = Ordered.HIGHEST_PRECEDENCE)
@ComponentScan(basePackages = {
        "com.yiwise.dialogflow", "com.yiwise.base", "com.yiwise.batch"
})
@EnableTransactionManagement
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class ContextConfig extends CommonContextConfig {

    @Bean
    public MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter() {
        return new MappingJackson2HttpMessageConverter();
    }

    @Bean
    public SimpMessagingTemplate simpMessagingTemplate() {
        return new SimpMessagingTemplate(new ExecutorSubscribableChannel());
    }

    @Bean
    public MDCHttpWebHandlerAdapter MDCHttpWebHandlerAdapter(WebHandler webHandler) {
        return new MDCHttpWebHandlerAdapter(webHandler);
    }

    @Bean
    public HttpMessageConverters httpMessageConverters() {
        return new HttpMessageConverters(mappingJackson2HttpMessageConverter());
    }

    @PreDestroy
    public void preDestroy() {
        log.info("=============================spring boot exit successful ! {}=============================", ServerInfoConstants.SERVER_HOSTNAME);
    }
}
