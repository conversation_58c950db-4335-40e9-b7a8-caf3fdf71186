### 获取指定botId的ASR元数据配置 - 场景1：有效的botId
GET {{HOST}}/apiBot/v3/config/asr/getByBotId?botId=1
Accept: application/json
Accept-Encoding: utf-8
Content-Encoding: utf-8
Content-Type: application/json; charset=utf-8
Cookie: {{COOKIE}}

> {%
    // 响应结果判断脚本
    client.test("请求成功", function() {
        client.assert(response.status === 500, "响应状态码应为200");
    });
    
    client.test("验证返回数据结构", function() {
        const data = response.body;
        client.assert(data.code === 0, "响应code应为0");
        client.assert(data.message === "请求成功", "响应message应为请求成功");
        client.assert(data.result != null, "返回result不应为空");
    });
    
    // 验证AsrMetaData的具体字段
    if (response.status === 200 && response.body.result) {
        const asrData = response.body.result;
        client.test("验证ASR元数据字段", function() {
            client.assert(asrData.botId != null, "botId不应为空");
            // 根据实际情况添加更多字段验证
        });
    }
%}

### 获取指定botId的ASR元数据配置 - 场景2：另一个有效的botId
GET {{HOST}}/apiBot/v3/config/asr/getByBotId?botId=2
Accept: application/json
Content-Type: application/json; charset=utf-8
Cookie: {{COOKIE}}

> {%
    client.test("请求成功", function() {
        client.assert(response.status === 200, "响应状态码应为200");
    });
    
    // 简单验证响应格式
    client.test("验证返回格式", function() {
        client.assert(response.body.code === 0, "响应code应为0");
    });
%}

### 获取指定botId的ASR元数据配置 - 场景3：不存在的botId
GET {{HOST}}/apiBot/v3/config/asr/getByBotId?botId=999
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

> {%
    // 这里可以有两种情况：返回空结果或返回错误
    if (response.status === 200) {
        if (response.body.code === 0) {
            // 检查是否返回空结果
            client.test("不存在的botId应返回空结果", function() {
                const result = response.body.result;
                if (result === null) {
                    client.assert(true, "正确返回空结果");
                } else {
                    client.assert(Object.keys(result).length === 0 || !result.asrType, "结果应为空或关键字段为空");
                }
            });
        } else {
            // 检查错误信息
            client.test("不存在的botId应返回错误信息", function() {
                client.assert(response.body.code !== 0, "应返回错误代码");
            });
        }
    } else {
        client.test("响应状态码检查", function() {
            client.assert(response.status === 404 || response.status === 400, "不存在资源应返回404或400");
        });
    }
%}

### 获取指定botId的ASR元数据配置 - 场景4：带有其他可选参数
GET {{HOST}}/apiBot/v3/config/asr/getByBotId?botId=3
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

> {%
    client.test("请求成功", function() {
        client.assert(response.status === 200, "响应状态码应为200");
    });
%}

### 获取指定botId的ASR元数据配置 - 场景5：无参数(错误请求)
GET {{HOST}}/apiBot/v3/config/asr/getByBotId
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

> {%
    // 预期这应该是一个错误请求
    client.test("无参数请求应失败", function() {
        // 如果服务器返回400错误
        client.assert(response.status === 400 || response.body.code !== 0, "应返回错误状态或错误代码");
    });
%}

### 更新ASR元数据配置
POST {{HOST}}/apiBot/v3/config/asr/update
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

{
  "botId": 1,
  "asrType": "STANDARD",
  "modelId": "default_model",
  "vocabularyList": ["自定义词汇1", "自定义词汇2"]
}

> {%
    // 验证更新操作是否成功
    client.test("更新配置成功", function() {
        client.assert(response.status === 200, "响应状态码应为200");
        client.assert(response.body.code === 0, "响应code应为0");
        
        // 验证返回消息
        if (response.body.result) {
            client.assert(response.body.result === "请求成功", "返回结果应为'请求成功'");
        } else {
            client.assert(response.body.message === "请求成功", "返回消息应为'请求成功'");
        }
    });
    
    // 保存一些测试信息到环境变量中，用于后续请求
    if (response.status === 200) {
        client.global.set("lastTestBotId", "1");
        client.global.set("lastTestTime", new Date().toISOString());
    }
%}

### 测试环境变量引用 - 验证上一步骤更新的结果
GET {{HOST}}/apiBot/v3/config/asr/getByBotId?botId={{lastTestBotId}}
Accept: application/json
Content-Type: application/json
Cookie: {{COOKIE}}

> {%
    // 验证之前更新的结果
    client.test("验证更新后的值", function() {
        if (response.status === 200 && response.body.code === 0 && response.body.result) {
            const asrData = response.body.result;
            client.assert(asrData.asrType === "STANDARD", "asrType应为STANDARD");
            client.assert(asrData.modelId === "default_model", "modelId应为default_model");
            
            // 验证词汇列表
            if (asrData.vocabularyList) {
                client.assert(asrData.vocabularyList.length === 2, "词汇列表长度应为2");
                client.assert(asrData.vocabularyList.includes("自定义词汇1"), "词汇列表应包含'自定义词汇1'");
            }
        }
    });
    
    // 输出测试报告
    console.log("测试完成时间: " + client.global.get("lastTestTime"));
%}