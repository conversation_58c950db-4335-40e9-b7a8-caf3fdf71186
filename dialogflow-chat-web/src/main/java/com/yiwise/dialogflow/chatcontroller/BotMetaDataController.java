package com.yiwise.dialogflow.chatcontroller;

import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.engine.share.BotAudioConfig;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import com.yiwise.dialogflow.engine.share.MagicActivityConfig;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.model.BotAsrConfig;
import com.yiwise.dialogflow.service.RobotSnapshotService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

@RestController
@RequestMapping("apiBot/v3/botMetaData")
public class BotMetaDataController {

    @Resource
    private RobotSnapshotService robotSnapshotService;

    @NoLogin
    @GetMapping("getLastVersionByDialogFlowId")
    public Mono<ResultObject<Integer>> getLastVersionByDialogFlowId(@NotNull Long dialogFlowId,
                                                                   @NotNull RobotSnapshotUsageTargetEnum usageTarget) {
        return robotSnapshotService.asyncGetLastVersionByDialogFlowId(dialogFlowId, usageTarget)
                .map(ResultObject::success);
    }

    @NoLogin
    @GetMapping("getBotMetaData")
    public Mono<ResultObject<BotMetaData>> getBotMetaData(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        return robotSnapshotService.asyncGetBotMetaDataByDialogFlowId(dialogFlowId, usageTarget, version)
                .map(ResultObject::success);
    }

    @NoLogin
    @RequestMapping("getRealtimeAsrConfig")
    public Mono<ResultObject<BotAsrConfig>> getRealAsrConfig(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        return robotSnapshotService.asyncGetRealtimeAsrConfig(botId, usageTarget, version)
                .map(ResultObject::success);
    }

    @NoLogin
    @InnerOnly
    @GetMapping("getMagicActivityAudioConfig")
    public ResultObject<MagicActivityConfig> getMagicActivityAudioConfig(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version, Long callJobId) {
        return ResultObject.success(robotSnapshotService.getMagicActivityConfig(botId, usageTarget, version, callJobId));
    }


}
