package com.yiwise.dialogflow.api;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.api.dto.request.*;
import com.yiwise.dialogflow.api.dto.response.*;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RequestMapping({"/apiBot/v3/bot"})
public interface BotApi {

    /**
     * 根据话术id列表查询话术信息
     * @param request 请求对象
     * @return 话术信息列表
     */
    @PostMapping({"queryBotList"})
    List<SimpleBotInfo> getListByIdList(@RequestBody BotListRequest request);

    @PostMapping("publishAndApproval")
    SimpleBotApprovalResult approval(@RequestBody BotApprovalRequest request);

    @PostMapping("copyBot")
    Long copyBot(@RequestBody CopyBotRequest request);

    @PostMapping("checkHasPermissionByDialogFlowId")
    Boolean checkHasPermissionByDialogFlowId(@RequestParam("dialogFlowId") @NotNull(message = "dialogFlowId不能为空") Long dialogFlowId,
                                          @RequestParam("tenantId") @NotNull(message = "tenantId不能为空") Long tenantId);

    @PostMapping("bind")
    void bindBot(@RequestBody BotBindRequest bindRequest);

    @PostMapping("unbind")
    void unbind(@RequestBody BotBindRequest bindRequest);

    @PostMapping("queryCallOutSmsTemplateIdSetByDialogFlowIdList")
    Map<Long, Set<Long>> queryCallOutSmsTemplateIdSetByDialogFlowIdList(@RequestBody QueryCallOutUsedSmsTemplateIdRequest request);

    /**
     * 检查话术和租户是否绑定
     * @param dialogFlowId 话术id
     * @param tenantId 租户id
     * @return 是否处于绑定状态, true:绑定, false:未绑定
     */
    @GetMapping("checkBindStatusByDialogFlowId")
    boolean checkBindStatusByDialogFlowId(@RequestParam("tenantId") @NotNull(message = "tenantId不能为空") Long tenantId,
                                          @RequestParam("dialogFlowId") @NotNull(message = "dialogFlowId不能为空") Long dialogFlowId);

    /**
     * 查询租户下所有已发布的话术(bot)列表
     * @param tenantId 租户id
     * @param searchWord 搜索内容(按话术名称模糊搜索)
     * @return 话术列表
     */
    @GetMapping("queryPublishedBotListByTenantId")
    List<SimpleBotInfo> queryPublishedBotListByTenantId(@RequestParam("tenantId") @NotNull(message = "tenantId不能为空") Long tenantId,
                                                        @RequestParam(value = "searchWord", required = false) String searchWord);

    /**
     * 根据租户id和话术id查询话术信息
     * @param tenantId 租户id
     * @param dialogFlowId 话术id, 注意是dialogFlowId, 不是botId
     * @return 话术信息
     */
    @GetMapping("getBotInfoByDialogFlowId")
    BotInfo getBotInfoByDialogFlowId(@RequestParam("tenantId") @NotNull(message = "tenantId不能为空") Long tenantId,
                                     @RequestParam("dialogFlowId") @NotNull(message = "dialogFlowId不能为空") Long dialogFlowId);

    /**
     * 根据租户id和话术id导出话术信息
     *
     * @param tenantId 租户id
     * @param dialogFlowId 话术id
     * @return 话术信息
     */
    @GetMapping("exportBotInfo")
    BotExportInfo exportBotInfo(@RequestParam("tenantId") @NotNull(message = "tenantId不能为空") Long tenantId,
                                @RequestParam("dialogFlowId") @NotNull(message = "dialogFlowId不能为空") Long dialogFlowId);

    @GetMapping("getAllListByTenantId")
    List<BotInfo> getAllListByTenantId(@RequestParam("tenantId") @NotNull(message = "tenantId不能为空") Long tenantId);

    @GetMapping("getAllCommonBotListByTenantId")
    List<BotInfo> getAllCommonBotListByTenantId(@RequestParam("tenantId") @NotNull(message = "tenantId不能为空") Long tenantId);

    @PostMapping("getDetailListByIdList")
    List<BotInfo> getDetailListByIdList(@RequestBody BotListRequest request);

    /**
     * 查询轻量 bot 模板列表
     */
    @PostMapping("getMagicBotTemplateByIdList")
    List<MagicBotTemplateInfo> getMagicBotTemplateByIdList(@RequestBody BotListRequest request);

    /**
     * 创建轻量版Bot(由轻量版 bot 模板复制而来)
     */
    @PostMapping("createMagicBot")
    MagicBotInfo createMagicBot(@RequestBody MagicBotCreateRequest request);

    /**
     * 创建轻量版Bot(由轻量版 bot 模板复制而来)前的检查
     * return true: 可以创建, false: 不可以创建
     */
    @PostMapping("preCreateMagicBot")
    Boolean preCreateMagicBot(@RequestBody MagicBotCreateRequest request);

    @GetMapping("getDialogFlowIdByBotId")
    Long getDialogFlowIdByBotId(Long botId);

    /**
     * 根据dialogFlowId获取bot属性, 如果话术不存在, 则返回默认值
     * @param dialogFlowId 话术id
     * @return bot属性
     */
    @GetMapping("getAttributeByDialogFlowId")
    BotAttributeDTO getBotAttributeByDialogFlowId(@RequestParam("dialogFlowId") @NotNull(message = "dialogFlowId不能为空") Long dialogFlowId);

    /**
     * 搜索bot列表
     */
    @PostMapping("searchBot")
    PageResultObject<SimpleBotInfo> searchBot(@RequestBody BotSearchRequest request);

    /**
     * 预创建轻量版活动配置
     */
    @PostMapping("preCreateMagicActivityConfig")
    Boolean preCreateMagicActivityConfig(@RequestBody MagicActivityConfigCreateRequest request);

    /**
     * 创建轻量版活动配置
     */
    @PostMapping("createMagicActivityConfig")
    String createMagicActivityConfig(@RequestBody MagicActivityConfigCreateRequest request);

}
