package com.yiwise.dialogflow.api;

import com.yiwise.dialogflow.api.dto.request.IdListDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;


@RequestMapping({"/apiBot/v3/snapshot"})
public interface SnapshotApi {

    /**
     * 根据 botId 列表查询最后一次发布的快照信息
     */
    @PostMapping("getLastPublishedSnapshotByBotIdList")
    Map<Long, Object> getLastPublishedSnapshotByBotIdList(@RequestBody IdListDTO idList);

}
