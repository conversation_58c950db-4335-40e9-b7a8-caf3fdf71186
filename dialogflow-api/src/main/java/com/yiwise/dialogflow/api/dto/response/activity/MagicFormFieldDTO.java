package com.yiwise.dialogflow.api.dto.response.activity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class MagicFormFieldDTO {

    String id;

    /**
     * 字段名称
     */
    String key;

    /**
     * 字段描述
     */
    String description;

    /**
     * 是否必填
     */
    Boolean required;

    /**
     * 数据类型
     */
    String type;

    /**
     * 枚举值列表
     */
    List<String> choices;

    /**
     * 表格栏目默认值
     */
    String defaultValue;

    /**
     * 【"type" 是枚举值时】是否允许用户自定义值。
     */
    Boolean allowCustom;
}
