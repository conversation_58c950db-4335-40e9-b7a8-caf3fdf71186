package com.yiwise.dialogflow.api;

import com.yiwise.dialogflow.api.dto.response.step.SimpleNode;
import com.yiwise.dialogflow.api.dto.response.step.SimpleStep;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

@RequestMapping({"/apiBot/v3/step"})
public interface StepApi {

    @GetMapping("getStepListByDialogFlowId")
    List<SimpleStep> getStepListByDialogFlowId(@RequestParam("dialogFlowId")
                                               @NotNull(message = "dialogFlowId不能为空") Long dialogFlowId);


    /**
     * 更新节点答案
     * @param node 节点
     */
    @PostMapping("updateNode")
    Boolean update(@RequestParam("tenantId") Long tenantId,
                @RequestParam("userId") Long userId,
                @RequestBody SimpleNode node);
}
