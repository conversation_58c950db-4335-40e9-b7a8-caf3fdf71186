package com.yiwise.dialogflow.api;

import com.yiwise.dialogflow.api.dto.request.StepAggregationStatsRequest;
import com.yiwise.dialogflow.api.dto.response.stats.DataReportStepDataPO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@RequestMapping("apiBot/v3/botStats")
public interface BotStatsApi {

    @PostMapping("generateDialogFlowDataReportData")
    List<DataReportStepDataPO> generateDialogFlowDataReportData(@RequestBody StepAggregationStatsRequest request);
}
