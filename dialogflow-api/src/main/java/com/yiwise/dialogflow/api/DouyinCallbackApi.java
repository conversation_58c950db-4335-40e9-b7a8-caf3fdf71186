package com.yiwise.dialogflow.api;

import com.yiwise.dialogflow.api.dto.request.douyin.DouyinBotInfoQueryDTO;
import com.yiwise.dialogflow.api.dto.response.douyin.DouyinBotInfoDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@RequestMapping({"/apiBot/v3/douyin"})
public interface DouyinCallbackApi {

    @PostMapping("queryByCondition")
    List<DouyinBotInfoDTO> queryByCondition(@RequestBody DouyinBotInfoQueryDTO query);

}
