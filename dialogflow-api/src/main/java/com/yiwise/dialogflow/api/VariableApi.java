package com.yiwise.dialogflow.api;

import com.yiwise.dialogflow.api.dto.response.variable.VariableInfoDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

@RequestMapping({"/apiBot/v3/variable"})
public interface VariableApi {

    @Deprecated
    @GetMapping("getUsedVariableNameSetByDialogFlowId")
    Set<String> getUsedVariableNameSetByDialogFlowId(@NotNull(message = "dialogFlowId不能为空") @RequestParam("dialogFlowId") Long dialogFlowId);


    @GetMapping("getUsedVariableNameSetByDialogFlowIdAndCallJobId")
    Set<String> getUsedVariableNameSetByDialogFlowIdAndCallJobId(@NotNull(message = "dialogFlowId不能为空") @RequestParam("dialogFlowId") Long dialogFlowId,
                                                                 @RequestParam(value = "callJobId", required = false) Long callJobId);

    @GetMapping("getUsingVariableListByDialogFlowId")
    List<VariableInfoDTO> getUsingVariableListByDialogFlowId(@NotNull(message = "dialogFlowId不能为空") @RequestParam("dialogFlowId") Long dialogFlowId);

    @GetMapping("getUsingTemplateVariableListByDialogFlowId")
    List<VariableInfoDTO> getUsingTemplateVariableListByDialogFlowId(@NotNull(message = "dialogFlowId不能为空") @RequestParam("dialogFlowId") Long dialogFlowId);

    @GetMapping("getAllDynamicVariableNameByDialogFlowId")
    List<String> getAllDynamicVariableNameByDialogFlowId(@NotNull(message = "dialogFlowId不能为空") @RequestParam("dialogFlowId") Long dialogFlowId);
}
