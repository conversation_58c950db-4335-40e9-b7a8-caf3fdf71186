package com.yiwise.dialogflow.api;

import com.yiwise.dialogflow.api.dto.request.IdListDTO;
import com.yiwise.dialogflow.api.dto.response.activity.MagicActivityConfigDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@RequestMapping({"/apiBot/v3/magicActivity/config"})
public interface MagicActivityConfigApi {

    @PostMapping("queryByActivityIdList")
    List<MagicActivityConfigDTO> queryByActivityIdList(@RequestBody IdListDTO idList);
}
