package com.yiwise.dialogflow.config;

import com.mongodb.MongoClientSettings;
import com.mongodb.ReadPreference;
import com.mongodb.reactivestreams.client.MongoClient;
import com.yiwise.middleware.mongodb.common.CustomMongoProperties;
import com.yiwise.middleware.mongodb.config.MongoConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;

import java.lang.reflect.Method;

import static com.yiwise.middleware.mongodb.common.MongoConfigYml.mongoProperties;

@Slf4j
@Configuration
public class ReactiveMongoConfig extends MongoConfig {

    private  Method configureClientSettingsMethod;

    // todo
    private void initConfigureClientSettingsMethod() {
        if (configureClientSettingsMethod == null) {
            try {
                configureClientSettingsMethod = MongoConfig.class.getDeclaredMethod("configureClientSettings", CustomMongoProperties.class);
            } catch (NoSuchMethodException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Bean
    @Primary
    public ReactiveMongoTemplate reactiveMongoTemplate() {
        MongoClient mongoClient = reactiveMongoClient();
        ReactiveMongoTemplate reactiveMongoTemplate = new ReactiveMongoTemplate(mongoClient, getDatabaseName());
        reactiveMongoTemplate.setReadPreference(ReadPreference.primaryPreferred());
        log.info("==== 初始化MongoDB库" + reactiveMongoTemplate.getMongoDatabase().getName() + " ReactiveMongoTemplate====");
        return reactiveMongoTemplate;
    }

    @Bean
    public ReactiveMongoTemplate primaryReactiveMongoTemplate() {
        MongoClient mongoClient = reactiveMongoClient();
        ReactiveMongoTemplate reactiveMongoTemplate = new ReactiveMongoTemplate(mongoClient, getDatabaseName());
        reactiveMongoTemplate.setReadPreference(ReadPreference.primary());
        log.info("==== 初始化MongoDB库" + reactiveMongoTemplate.getMongoDatabase().getName() + " ReactiveMongoTemplate====");
        return reactiveMongoTemplate;
    }

    private MongoClient reactiveMongoClient() {
        MongoClientSettings mongoClientSettings = configureClientSettings(mongoProperties());
        return com.mongodb.reactivestreams.client.MongoClients.create(mongoClientSettings);
    }

    private MongoClientSettings configureClientSettings(CustomMongoProperties properties)  {
        initConfigureClientSettingsMethod();
        boolean access = configureClientSettingsMethod.isAccessible();
        try {
            configureClientSettingsMethod.setAccessible(true);
            return (MongoClientSettings) configureClientSettingsMethod.invoke(this, properties);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            configureClientSettingsMethod.setAccessible(access);
        }
    }

}
