CREATE TABLE `bot`
(
    `bot_id`                  bigint(20)                                              NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `name`                    varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT 'BOT名称',
    `description`             varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'BOT描述',
    `intent_level_tag_id`     bigint(20)                                              NOT NULL COMMENT '意向标签组ID',
    `industry`                varchar(50)                                             NULL COMMENT '行业',
    `sub_industry`            varchar(50)                                             NULL COMMENT '子行业',
    `customer_track_type`     int                                                     NULL COMMENT '赛道ID',
    `customer_track_sub_type` int                                                     NULL COMMENT '二级赛道ID',
    `recorder_id`             bigint COMMENT '录音师ID',
    `tts_voice`               varchar(50) COMMENT 'TTS语音',
    voice_type                varchar(20) comment '语音类型',
    audit_status              varchar(20) comment '审核状态',
    enable_status             tinyint         default 1                               not null comment '删除状态',
    create_user_id            bigint unsigned default 0                               not null,
    update_user_id            bigint unsigned default 0                               not null,
    create_time               timestamp       default CURRENT_TIMESTAMP               not null,
    update_time               timestamp       default CURRENT_TIMESTAMP               not null on update CURRENT_TIMESTAMP,
    PRIMARY KEY (`bot_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;



CREATE TABLE `bot_ref`
(
    `bot_id`         bigint(20)                                NOT NULL COMMENT 'BotID',
    `dialog_flow_id` bigint(20)                                NOT NULL COMMENT 'AICC侧话术ID',
    `tenant_id`      bigint(20)                                NOT NULL COMMENT '租户ID',
    enable_status    tinyint         default 1                 not null comment '删除状态',
    create_user_id   bigint unsigned default 0                 not null,
    update_user_id   bigint unsigned default 0                 not null,
    create_time      timestamp       default CURRENT_TIMESTAMP not null,
    update_time      timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    PRIMARY KEY (`bot_id`, dialog_flow_id) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;



CREATE TABLE `intent_ref`
(
    `intent_ref_id` bigint(20)                                NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `bot_id`        bigint(20)                                NOT NULL COMMENT 'BotID',
    `intent_id`     varchar(64)                               NOT NULL COMMENT '意图ID',
    `ref_id`        varchar(64)                               NOT NULL COMMENT '关联ID',
    `ref_type`      varchar(50)                               NOT NULL COMMENT '关联类型',
    create_user_id  bigint unsigned default 0                 not null,
    update_user_id  bigint unsigned default 0                 not null,
    create_time     timestamp       default CURRENT_TIMESTAMP not null,
    update_time     timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    PRIMARY KEY (intent_ref_id) USING BTREE,
    UNIQUE INDEX `uid` (`intent_id`, ref_id, ref_type) USING BTREE,
    INDEX `bid` (bot_id) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;