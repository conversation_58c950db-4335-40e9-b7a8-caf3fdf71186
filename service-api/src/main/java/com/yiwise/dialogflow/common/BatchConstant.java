package com.yiwise.dialogflow.common;

import com.google.common.collect.Lists;
import com.yiwise.batch.model.dto.SheetInfoDTO;

import java.util.List;

import static com.yiwise.dialogflow.common.BatchConstant.Header.*;

/**
 * <AUTHOR>
 * @date 2022/11/29
 */
public class BatchConstant {

    public static class Header {
        public static final String 意图名称 = "意图名称";
        public static final String 命中次数 = "命中次数";

        public static final String 通话ID = "通话ID";
        public static final String 客户名称 = "客户名称";
        public static final String 话术名称 = "话术名称";
        public static final String 任务名称 = "任务名称";
        public static final String 语料内容 = "语料内容";
        public static final String 历史命中意图 = "历史命中意图";
        public static final String 当前命中的意图 = "当前命中的意图";
        public static final String 时间 = "时间";
    }

    public static class SheetName {
        public static final String SEMANTIC_ANALYSIS_EMPTY_STAT_SHEET_NAME = "新增意图统计";
        public static final String SEMANTIC_ANALYSIS_HIST_STAT_SHEET_NAME = "历史意图统计";
        public static final String SEMANTIC_ANALYSIS_DETAIL_SHEET_NAME = "语料明细统计";
    }

    public static class HeaderList {
        public static final List<String> SEMANTIC_ANALYSIS_EMPTY_STAT_HEADER_LIST = Lists.newArrayList(意图名称, 命中次数);
        public static final List<String> SEMANTIC_ANALYSIS_HIST_STAT_HEADER_LIST = Lists.newArrayList(历史命中意图, 命中次数);
        public static final List<String> SEMANTIC_ANALYSIS_DETAIL_HEADER_LIST = Lists.newArrayList(通话ID, 客户名称, 话术名称, 任务名称, 语料内容, 历史命中意图, 当前命中的意图, 时间);
    }

    public static class SheetInfoList {
        public static final List<SheetInfoDTO> SEMANTIC_ANALYSIS_STAT_TUPLE_LIST = Lists.newArrayList(
                SheetInfoDTO.of(SheetName.SEMANTIC_ANALYSIS_EMPTY_STAT_SHEET_NAME, HeaderList.SEMANTIC_ANALYSIS_EMPTY_STAT_HEADER_LIST),
                SheetInfoDTO.of(SheetName.SEMANTIC_ANALYSIS_HIST_STAT_SHEET_NAME, HeaderList.SEMANTIC_ANALYSIS_HIST_STAT_HEADER_LIST)
        );

        public static final List<SheetInfoDTO> SEMANTIC_ANALYSIS_DETAIL_TUPLE_LIST = Lists.newArrayList(
                SheetInfoDTO.of(SheetName.SEMANTIC_ANALYSIS_EMPTY_STAT_SHEET_NAME, HeaderList.SEMANTIC_ANALYSIS_EMPTY_STAT_HEADER_LIST),
                SheetInfoDTO.of(SheetName.SEMANTIC_ANALYSIS_HIST_STAT_SHEET_NAME, HeaderList.SEMANTIC_ANALYSIS_HIST_STAT_HEADER_LIST),
                SheetInfoDTO.of(SheetName.SEMANTIC_ANALYSIS_DETAIL_SHEET_NAME, HeaderList.SEMANTIC_ANALYSIS_DETAIL_HEADER_LIST)
        );
    }
}
