package com.yiwise.dialogflow.service.asrmodel;

import com.yiwise.dialogflow.entity.enums.AsrSourceConcurrencyRecordTypeEnum;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrSourceConcurrencyRecordPO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/27 13:58:04
 */
public interface AsrSourceConcurrencyRecordService {
    List<AsrSourceConcurrencyRecordPO> countByTime(LocalDate startTime, LocalDate endTime, List<Long> sourceIdList, AsrSourceConcurrencyRecordTypeEnum asrSourceConcurrencyRecordType);

    void save(AsrSourceConcurrencyRecordPO asrSourceConcurrencyRecordPO);
}
