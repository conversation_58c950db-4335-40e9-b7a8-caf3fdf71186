package com.yiwise.dialogflow.service.remote;

import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.entity.dto.BotNameCountDTO;
import com.yiwise.dialogflow.entity.dto.DialogFlowPostDTO;
import com.yiwise.dialogflow.entity.enums.AuditStatusEnum;
import com.yiwise.dialogflow.entity.po.DialogFlowInfoPO;
import com.yiwise.dialogflow.entity.vo.CreateBotQrCodeVO;
import com.yiwise.dialogflow.service.openfeign.RemoteServiceAdapter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.core.type.TypeReference;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/11
 */
@Service
public class DialogFlowService {

    @Resource
    RemoteServiceAdapter remoteServiceAdapter;

    public Long createNewDialogFlow(DialogFlowPostDTO dialogFlowPostDTO) {
        return remoteServiceAdapter.invoke("dialogFlowServiceImpl", "createNewDialogFlow", Long.class, dialogFlowPostDTO);
    }

    public void updateNewDialogFlow(DialogFlowPostDTO dialogFlowPostDTO) {
        remoteServiceAdapter.invoke("dialogFlowServiceImpl", "updateNewDialogFlow", Void.class, dialogFlowPostDTO);
    }

    public void deleteDialogFlow(Long dialogFlowId, Boolean forceDelete) {
        remoteServiceAdapter.invoke("dialogFlowServiceImpl", "remoteDeleteDialogFlow", Void.class, dialogFlowId, forceDelete);
    }

    public void updateDialogFlow(Long dialogFlowId, Long userId, AuditStatusEnum auditStatus) {
        remoteServiceAdapter.invoke("dialogFlowServiceImpl", "updateDialogFlowPublishStatus", Void.class, dialogFlowId, userId, auditStatus);
    }

    public void updateVisibleStatus(Long dialogFlowId, EnabledStatusEnum visibleStatus) {
        remoteServiceAdapter.invoke("dialogFlowServiceImpl", "updateVisibleStatus", Void.class, dialogFlowId, visibleStatus);
    }

    public DialogFlowInfoPO selectByBotId(Long botId) {
        return remoteServiceAdapter.invoke("dialogFlowServiceImpl", "selectByDialogFlowId", DialogFlowInfoPO.class, botId);
    }

    public void changeDialogFlowStatus(Long botId, String draft) {
        remoteServiceAdapter.invoke("dialogFlowServiceImpl", "remoteChangeDialogFlowStatus", Void.class, botId, draft);
    }

    public String createDialogFlowQcCode(CreateBotQrCodeVO createBotQrCodeVO) {
        return remoteServiceAdapter.invoke(SystemEnum.OPE, "dialogFlowServiceImpl", "createQrCode", String.class, createBotQrCodeVO);
    }

    public List<BotNameCountDTO> countDialogFlowByNameList(List<String> botNameList) {
        if (CollectionUtils.isEmpty(botNameList)) {
            return Collections.emptyList();
        }
        TypeReference<List<BotNameCountDTO>>  typeReference = new TypeReference<List<BotNameCountDTO>>(){};
        return remoteServiceAdapter.invoke("dialogFlowServiceImpl", "countDialogFlowByNameList", typeReference, botNameList);
    }
}
