package com.yiwise.dialogflow.service.intent;

import com.yiwise.dialogflow.entity.bo.algorithm.ResourceId2NameBO;
import com.yiwise.dialogflow.entity.po.intent.IntentRulePO;

import java.util.Map;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/9/22
 * @class <code>IntentRuleOperationLogService</code>
 * @see
 * @since JDK1.8
 */
public interface IntentRuleOperationLogService {
    void addOperationLog(IntentRulePO oldIntentRule, IntentRulePO newIntentRule,
                         ResourceId2NameBO resourceMap, Long botId, Long userId);

    void moveMatchOrderLog(Map<Integer, IntentRulePO> ruleOrderMap, int[] moveInfo, ResourceId2NameBO resourceMap, Long botId, Long userId);
}
