package com.yiwise.dialogflow.service.remote;

import com.yiwise.dialogflow.service.openfeign.RemoteServiceAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class IntentLevelTagTenantRelationService {

    @Resource
    RemoteServiceAdapter remoteServiceAdapter;

    public void bindIntentLevelTagToTenant(Long distributorId, Long tenantId, Long intentLevelTagId) {
        remoteServiceAdapter.invoke("intentLevelTagTenantRelationServiceImpl", "innerAddIntentLevelTenantRelation", Void.class, distributorId, tenantId, intentLevelTagId);
    }


}
