package com.yiwise.dialogflow.service.entitycollect;

import com.yiwise.dialogflow.entity.bo.entity.RuntimeEntityBO;
import com.yiwise.dialogflow.entity.po.BaseEntityPO;

import java.util.List;

/**
 * 实体预处理和校验service
 */
public interface EntityPreprocessService {

    List<RuntimeEntityBO> preprocess(List<BaseEntityPO> entityList);

    void validate(List<BaseEntityPO> entityList);

    void validate(BaseEntityPO entity);
}
