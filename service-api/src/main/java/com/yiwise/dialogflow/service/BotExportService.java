package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.entity.po.DialogFlowExtraRuleConditionNodePO;
import com.yiwise.dialogflow.entity.vo.BotExportPartExcelVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/1
 */
public interface BotExportService {

    /**
     * 导出话术文本
     *
     * @param botId  botId
     * @param userId 操作人id
     * @return txt文件链接
     */
    String exportText(Long botId, Long userId);

    /**
     * 批量导出话术文本
     *
     * @param botIdList botId列表
     * @param userId 操作人id
     * @return 压缩文件链接
     */
    String batchExportText(List<Long> botIdList, Long userId);

    /**
     * 导出word格式话术
     *
     * @param botId             botId
     * @param userId            操作人id
     * @param generateWatermark
     * @return word文档链接
     */
    String exportWord(Long botId, Long userId, Boolean generateWatermark);

    /**
     * 导出轻量化活动模板配置
     */
    String exportMagicTemplateWord(Long botId, Long activityId);

    /**
     * 批量导出word格式话术
     *
     * @param botIdList botId列表
     * @param userId 操作人id
     * @return 压缩文件链接
     */
    String batchExportWord(List<Long> botIdList, Long userId);

    /**
     * 导出XMind格式话术
     *
     * @param botId  botId
     * @param userId 操作人id
     * @return XMind文件链接
     */
    String exportXMind(Long botId, Long userId);

    /**
     * 批量导出XMind格式话术
     *
     * @param botIdList botId列表
     * @param userId 操作人id
     * @return 压缩文件链接
     */
    String batchExportXMind(List<Long> botIdList, Long userId);

    /**
     * 导出Excel格式话术
     *
     * @param botId             botId
     * @param userId            操作人id
     * @param generateWatermark
     * @return Excel文件链接
     */
    String exportExcel(Long botId, Long userId, Boolean generateWatermark);

    /**
     * 批量导出Excel格式话术
     *
     * @param botIdList botId列表
     * @param userId 操作人id
     * @return 压缩文件链接
     */
    String batchExportExcel(List<Long> botIdList, Long userId);

    /**
     * 导出部分话术Excel
     *
     * @param excelVO 选中的节点信息
     * @param userId  操作人id
     * @return Excel文件链接
     */
    String exportPartExcel(BotExportPartExcelVO excelVO, Long userId);

    /**
     * 导出部分话术默认选中的节点
     *
     * @param botId botId
     * @return 节点列表
     */
    List<DialogFlowExtraRuleConditionNodePO> defaultSelectedNodeList(Long botId);

    String exportFullResource(Long botId, Long userId);
}
