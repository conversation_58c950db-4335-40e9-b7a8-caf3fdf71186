package com.yiwise.dialogflow.service.remote;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.utils.IpMaskUtils;
import com.yiwise.middleware.redis.service.ObjectRedisTemplate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/1/17
 */
@Service
public class IPWhiteListService {

    @Resource
    private ObjectRedisTemplate objectRedisTemplate;

    private final Cache<Integer, Set<String>> ORIGIN_PATTERN_CACHE = CacheBuilder.newBuilder()
            .maximumSize(8)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    private final Cache<String, Boolean> ALLOW_IP_CACHE = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build();

    public boolean checkIsInWhiteList(String ip) {
        if (StringUtils.isBlank(ip)) {
            return false;
        }

        if (ALLOW_IP_CACHE.getIfPresent(ip) != null) {
            return true;
        }

        Set<String> cacheIps = ORIGIN_PATTERN_CACHE.getIfPresent(0);
        if (CollectionUtils.isEmpty(cacheIps)) {
            Set<Object> ipRanges = objectRedisTemplate.opsForSet().members(RedisKeyCenter.getIpWhiteListRedisKey());
            if (CollectionUtils.isNotEmpty(ipRanges)) {
                ORIGIN_PATTERN_CACHE.put(0, ipRanges.stream().map(String::valueOf).collect(java.util.stream.Collectors.toSet()));
                cacheIps = ORIGIN_PATTERN_CACHE.getIfPresent(0);
            }
        }

        if (CollectionUtils.isNotEmpty(cacheIps)) {
            for (String objRange : cacheIps) {
                if(IpMaskUtils.matchIpMask(ip, objRange)) {
                    ALLOW_IP_CACHE.put(ip, true);
                    return true;
                }
            }
        }
        return false;
    }

}
