package com.yiwise.dialogflow.service.intent;

import com.yiwise.dialogflow.entity.bo.ActionNameResourceBO;
import com.yiwise.dialogflow.entity.enums.ActionCategoryEnum;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleActionPO;
import com.yiwise.dialogflow.entity.po.intent.RuleActionParam;
import com.yiwise.dialogflow.entity.query.IntentActionQuery;
import com.yiwise.dialogflow.entity.vo.IntentRuleActionVO;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.RuleActionSyncVO;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/6/21
 * @class <code>IntentRuleActionService</code>
 * @see
 * @since JDK1.8
 */
public interface IntentRuleActionService {
    /**
     * 添加触发动作
     *
     * @param actionPO
     * @return
     */
    IntentRuleActionPO addIntentRuleAction(IntentRuleActionPO actionPO, boolean isCheck, Long tenantId);

    /**
     * 更新触发动作
     *
     * @param actionPO
     * @return
     */
    IntentRuleActionPO updateIntentRuleAction(IntentRuleActionPO actionPO, Long tenantId);

    /**
     * 获取动作列表
     *
     * @param botId
     * @param actionType
     * @return
     */
    List<IntentRuleActionPO> getIntentRuleActionList(Long botId, List<ActionCategoryEnum> actionType);

    /**
     * 批量删除触发动作
     *
     * @param botId
     * @param idList
     * @return
     */
    boolean batchDeleteAction(Long botId, List<String> idList, Long tenantId, Long userId);

    /**
     * 查询单条意向规则触发动作
     *
     * @param id
     * @return
     */
    IntentRuleActionPO getIntentRuleAction(String id);

    ActionNameResourceBO getSourceId2NameMap(Long tenantId);
    ActionNameResourceBO getSourceId2NameMapByBotId(Long botId);

    void addSourceName(List<RuleActionParam> actionList, ActionNameResourceBO nameResourceBO);

    List<IntentRuleActionVO> getIntentRuleActionDetailList(IntentActionQuery condition);

    Optional<IntentRuleActionPO> getIntentRuleActionDetail(String id);

    void updateActionList(IntentRuleActionPO intentAction);

    void clearInvalidNodeId(Set<String> oldNodeIdSet, Long botId);

    BotSyncResultVO sync(RuleActionSyncVO syncVO);

    List<IntentRuleActionPO> findLlmLabelAction(Long botId, String llmLabelId);
}
