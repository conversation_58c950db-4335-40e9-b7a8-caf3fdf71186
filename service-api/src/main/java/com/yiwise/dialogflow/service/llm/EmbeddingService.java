package com.yiwise.dialogflow.service.llm;

import com.yiwise.dialogflow.entity.po.DialogBaseNodePO;
import com.yiwise.dialogflow.entity.po.KnowledgePO;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import com.yiwise.dialogflow.entity.po.llm.RagTextUploadRecordPO;
import com.yiwise.dialogflow.entity.vo.BaseCallbackResultVO;

import java.util.List;
import java.util.Optional;

/**
 * 将答案列表送给算法, 用于相关性搜索
 */
public interface EmbeddingService {

    /**
     * 上传所有答案文本
     * @return 上传记录 id
     */
    String uploadAll(Long botId,
                     Long userId,
                     List<DialogBaseNodePO> allNodeList,
                     List<KnowledgePO> allKnowledgeList,
                     List<SpecialAnswerConfigPO> allSpecialAnswerList);

    /**
     * 检查当前所有文本和上传相比是否有变更, 如果没有变更, 则不用重新上传, 否则, 重新上传
     */
    boolean checkNeedUpload(RagTextUploadRecordPO preRecord,
                            List<DialogBaseNodePO> allNodeList,
                            List<KnowledgePO> allKnowledgeList,
                            List<SpecialAnswerConfigPO> allSpecialAnswerList);

    /**
     * 根据上传记录查询是否已经完成(接收到算法回调)
     */
    boolean checkIsSuccess(Long botId, String id);

    boolean checkIsTimeout(RagTextUploadRecordPO record);

    Optional<RagTextUploadRecordPO> getLastByBotId(Long botId);

    /**
     * 更新结果
     */
    void updateResult(String id, BaseCallbackResultVO result);

    void waitingResult(Long botId, String id);

}
