package com.yiwise.dialogflow.service.asrmodel;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.entity.enums.asr.AsrErrorCorrectionTrainStatusEnum;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrErrorCorrectionDetailPO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrCorrectionRequestVO;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrErrorCorrectionDetailVO;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/10/14 15:44:00
 */
public interface AsrErrorCorrectionService {

    void startTrain(String asrErrorCorrectionDetailId, Long userId, SystemEnum system);

    void trainSuccess(String refId);

    String requestAsrErrorCorrection(Long dialogFlowId, String userInput);
    String requestAsrErrorCorrectionByBotId(Long botId, String userInput);

    Mono<String> requestAsrErrorCorrection(AsrCorrectionRequestVO request);

    AsrErrorCorrectionDetailPO get(String asrErrorCorrectionDetailId);

    void save(AsrErrorCorrectionDetailVO asrErrorCorrectionDetailVO);

    void unbindBot(AsrErrorCorrectionDetailVO asrErrorCorrectionDetailVO);

    void bindBot(AsrErrorCorrectionDetailVO asrErrorCorrectionDetailVO);

    void delete(String asrErrorCorrectionDetailId);

    List<BotPO> getBotList(List<String> asrErrorCorrectionDetailId, String botInfo);

    PageResultObject<BotPO> getBotList(String asrErrorCorrectionDetailId, String botInfo, Integer pageNum, Integer pageSize);

    PageResultObject<AsrErrorCorrectionDetailVO> list(String name, AsrErrorCorrectionTrainStatusEnum status, String botInfo, Integer pageNum, Integer pageSize);

    PageResultObject<IdNamePair<String, String>> idNamePairList(String name, Integer pageNum, Integer pageSize);

    Optional<AsrErrorCorrectionDetailPO> getByBotId(Long botId);

    AsrErrorCorrectionDetailPO getByModelId(String modelId);

    boolean nameExists(String asrErrorCorrectionDetailId, String name);
}
