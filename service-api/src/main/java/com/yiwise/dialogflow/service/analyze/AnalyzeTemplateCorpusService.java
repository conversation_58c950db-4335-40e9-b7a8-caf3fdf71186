package com.yiwise.dialogflow.service.analyze;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.po.analyze.AnalyzeTemplateCorpusPO;
import com.yiwise.dialogflow.entity.vo.analyze.AnalyzeTemplateCorpusAddVO;
import com.yiwise.dialogflow.entity.vo.analyze.AnalyzeTemplateCorpusQueryVO;
import com.yiwise.dialogflow.entity.vo.analyze.AnalyzeTemplateCorpusUpdateVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AnalyzeTemplateCorpusService {

    /**
     * 批量保存语料列表
     *
     * @param list 语料列表
     */
    void batchSave(List<AnalyzeTemplateCorpusPO> list);

    /**
     * 根据模板id删除语料
     *
     * @param templateId 模板id
     */
    void deleteByTemplateId(String templateId);

    /**
     * 根据意图id列表删除语料
     *
     * @param intentIdList 意图id列表
     */
    void deleteByIntentIdList(List<String> intentIdList);

    /**
     * 根据意图id列表查询语料
     *
     * @param intentIdList 意图id列表
     * @return 语料列表
     */
    List<AnalyzeTemplateCorpusPO> listByIntentIdList(List<String> intentIdList);

    /**
     * 查询全部语料
     *
     * @param templateId 模板id
     * @return 语料列表
     */
    List<AnalyzeTemplateCorpusPO> listByTemplateId(String templateId);

    /**
     * 分页查询语料
     *
     * @param request form
     * @return 语料列表
     */
    PageResultObject<AnalyzeTemplateCorpusPO> list(AnalyzeTemplateCorpusQueryVO request);

    /**
     * 添加语料
     *
     * @param request form
     */
    void add(AnalyzeTemplateCorpusAddVO request);

    /**
     * 修改语料
     *
     * @param request form
     */
    void update(AnalyzeTemplateCorpusUpdateVO request);

    /**
     * 删除语料
     *
     * @param request form
     */
    void delete(AnalyzeTemplateCorpusQueryVO request);

    /**
     * 根据语料列表查询已存在的语料
     *
     * @param templateId 模板id
     * @param corpusList 语料列表
     * @return <语料，意图id列表>
     */
    Map<String, List<String>> findExistsCorpusMap(String templateId, List<String> corpusList);

    /**
     * 根据语料模糊查询意图id列表
     *
     * @param templateId 模板id
     * @param corpus 语料
     * @return 意图id列表
     */
    List<String> finsIntentIdListByCorpus(String templateId, String corpus);
}
