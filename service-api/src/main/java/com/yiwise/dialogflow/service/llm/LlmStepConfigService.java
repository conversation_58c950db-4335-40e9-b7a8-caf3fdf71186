package com.yiwise.dialogflow.service.llm;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.po.LlmStepConfigPO;
import com.yiwise.dialogflow.entity.po.StepPO;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface LlmStepConfigService {

    /**
     * 保存大模型流程配置
     *
     * @param config 实体
     * @param userId 操作人id
     * @return 保存后的实体
     */
    LlmStepConfigPO save(LlmStepConfigPO config, Long userId);

    /**
     * 根据话术id和流程id查询大模型流程配置
     *
     * @param botId  话术id
     * @param stepId 流程id
     * @return 大模型流程配置
     */
    LlmStepConfigPO getByStepId(Long botId, String stepId);

    /**
     * 删除大模型流程配置
     *
     * @param botId      话术id
     * @param stepIdList 流程id列表
     */
    void deleteByStepIdList(Long botId, List<String> stepIdList);

    /**
     * 话术内复制流程
     *
     * @param botId      话术id
     * @param srcStepId  源流程id
     * @param targetStep 目标流程
     */
    void copyInBot(Long botId, String srcStepId, StepPO targetStep);

    void sync(LlmStepConfigPO srcLlmStepConfig, StepPO targetStep, Map<String, String> stepIdMapping, Map<String, String> knowledgeIdMapping,
              Map<String, String> specialAnswerIdMapping, Map<String, String> variableIdMapping);

    /**
     * 根据话术id查询大模型流程配置列表
     *
     * @param botId 话术id
     * @return 大模型流程配置列表
     */
    List<LlmStepConfigPO> getByBotId(Long botId);

    /**
     * 提示词优化
     *
     * @param prompt 提示词
     * @return 优化后的提示词
     */
    Flux<ResultObject<String>> promptOptimize(String prompt);

    /**
     * 重置大模型流程配置资源引用信息
     *
     * @param botId 话术id
     */
    void resetResourceReferenceInfo(Long botId);

    void updateStepOnVariableRename(Long botId, String variableId, String oldVariableName, String newVariableName, Long userId);

}