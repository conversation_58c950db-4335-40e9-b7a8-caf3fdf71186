package com.yiwise.dialogflow.service.operationlog;

import com.yiwise.dialogflow.entity.po.llm.RagDocumentPO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface RagDocumentOperationLogService {

    void createDoc(RagDocumentPO po);

    void reSplitDoc(RagDocumentPO po);

    void updateEnabledStatus(RagDocumentPO po);

    void updateDocName(Long botId, String oldName, String newName, Long userId);

    void deleteDoc(Long botId, List<RagDocumentPO> docList, Long userId);

    void updateSegment(Long botId, String docName, String oldContent, String newContent, Long userId);

    void deleteSegment(Long botId, String docName, String content, Long userId);
}