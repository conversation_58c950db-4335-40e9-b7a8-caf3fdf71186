package com.yiwise.dialogflow.service.remote;

import com.yiwise.base.model.enums.CodeDescEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.OssKeyCenter;
import com.yiwise.dialogflow.entity.vo.audio.TtsComposeResultVO;
import com.yiwise.dialogflow.entity.vo.audio.request.TtsComposeRequestVO;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import com.yiwise.middleware.objectstorage.serializer.AddOssPrefixSerializer;
import com.yiwise.middleware.tts.TtsConfig;
import com.yiwise.middleware.tts.convert.CustomVarConvertItem;
import com.yiwise.middleware.tts.convert.InterpretConvert;
import com.yiwise.middleware.tts.convert.InterpretConvertLoader;
import com.yiwise.middleware.tts.enums.TtsProviderEnum;
import com.yiwise.middleware.tts.enums.TtsVoiceEnum;
import com.yiwise.middleware.tts.utils.TtsCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TtsComposeService {

    @Resource
    private ObjectStorageHelper objectStorageHelper;

    public TtsComposeResultVO composeTextByConfig(TtsComposeRequestVO request) {
        if (Objects.isNull(request)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "request不能为空");
        }
        if (StringUtils.isBlank(request.getFilePath())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "filePath不能为空");
        }
        if (StringUtils.isBlank(request.getAnswerText())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "answerText不能为空");
        }
        if (Objects.isNull(request.getSpeed())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "speed不能为空");
        }
        if (Objects.isNull(request.getVolume())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "volume不能为空");
        }
        if (Objects.isNull(request.getVoice())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "voice不能为空");
        }
        TtsVoiceEnum voice = CodeDescEnum.getFromNameOrNull(TtsVoiceEnum.class, request.getVoice());
        if (Objects.isNull(voice)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "voice不存在");
        }
        String answerText = request.getAnswerText();

        TtsConfig ttsConfigBO = new TtsConfig();
        ttsConfigBO.setSpeed(request.getSpeed());
        ttsConfigBO.setVoice(voice);
        ttsConfigBO.setVolume(request.getVolume());
        log.info("TTS text={},config={}", answerText, ttsConfigBO);
        String wavPath = executeTtsByConfig(ttsConfigBO, answerText);

        File wavFile = new File(wavPath);
        String url = objectStorageHelper.upload(request.getFilePath(), wavFile);

        TtsComposeResultVO result = new TtsComposeResultVO();
        result.setAnswerText(answerText);
        result.setFullUrl(AddOssPrefixSerializer.getAddOssPrefixUrl(url));
        result.setRelativeUrl(objectStorageHelper.getKeyFromUrl(url));
        return result;
    }

    private String executeTtsByConfig(TtsConfig ttsConfigBO, String text) {
        return TtsCacheUtils.composeText(ttsConfigBO, text);
    }

    public TtsComposeResultVO varAudition(String varName,
                                          String varValue,
                                          String replacement,
                                          TtsProviderEnum provider) {

        TtsConfig config = buildTtsConfig(provider);
        InterpretConvert interpretConvert = InterpretConvertLoader.load(config::getVoice);
        Map<String, String> properties = new HashMap<>();
        properties.put(varName, varValue);

        List<CustomVarConvertItem> customVarConvertItemList = new ArrayList<>();
        CustomVarConvertItem customVarConvertItem = new CustomVarConvertItem();
        customVarConvertItem.setProvider(provider);
        customVarConvertItem.setVarName(varName);
        customVarConvertItem.setVarValue(varValue);
        customVarConvertItem.setReplacement(replacement);

        customVarConvertItemList.add(customVarConvertItem);

        String text = interpretConvert.convertVar(varName, properties, Collections.emptyMap(), customVarConvertItemList);

        log.info("varAudition text={}", text);
        String wavPath = executeTtsByConfig(config, text);
        File wavFile = new File(wavPath);
        String url = objectStorageHelper.upload(OssKeyCenter.getVarAuditionDir(), wavFile);

        TtsComposeResultVO result = new TtsComposeResultVO();
        result.setAnswerText(text);
        result.setFullUrl(AddOssPrefixSerializer.getAddOssPrefixUrl(url));
        result.setRelativeUrl(objectStorageHelper.getKeyFromUrl(url));
        return result;
    }

    private TtsConfig buildTtsConfig(TtsProviderEnum provider) {
        TtsConfig ttsConfigBO = new TtsConfig();
        ttsConfigBO.setSpeed(5.0f);
        ttsConfigBO.setVolume(70.0f);
        if (TtsProviderEnum.ALI.equals(provider)) {
            ttsConfigBO.setVoice(TtsVoiceEnum.WOMEN_Aiqi);
        } else if (TtsProviderEnum.YIWISE3.equals(provider)) {
            ttsConfigBO.setVoice(TtsVoiceEnum.WOMEN_XIAOLAN_PLUS);
        } else {
            ttsConfigBO.setVoice(TtsVoiceEnum.WOMEN_XiaoxiaoNeural);
        }
        return ttsConfigBO;
    }

}
