package com.yiwise.dialogflow.service.feishu;

import com.alibaba.fastjson.JSONArray;

import java.util.List;

public interface FeishuApiService {

     String getToken();

    List<String> getUserOpenIdList(List<String> emails, List<String> mobiles);

    String createChat(String name, List<String> userIdList);

    List<String> memberList(String chatId);

    void createChatMember(String chatId, List<String> userIdList);

    JSONArray searchChat(String query);

    String sendMsg(String receiveIdType, String receiveId, String msgType, String content);

    /**
     * 发送飞书群聊消息到webhook
     */
    void sendFeishuWarn(List<String> mobiles, String title, String content, String webhook);

    /**
     * 发送飞书群聊消息到webhook
     */
    void sendFeishuWarn(String body, String webhook);

    List<String> getUserIdsFeishu(List<String> mobiles);
}
