package com.yiwise.dialogflow.service.stats;

import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.po.stats.StepStatsPO;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;

import java.util.List;

/**
 * 流程统计
 */
public interface StepStatsService {

    List<StepStatsPO> queryAllStepStatsList(Long botId, List<String> stepIdList, BaseStatsQuery condition);

    void saveStepStats(BotStatsAnalysisResult analysisResult);
}
