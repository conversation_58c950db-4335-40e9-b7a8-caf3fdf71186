package com.yiwise.dialogflow.service.operationlog;

import com.yiwise.dialogflow.entity.po.KnowledgePO;

import java.util.List;

public interface KnowledgeOperationLogService {
    void compareAndCreateOperationLog(Long botId, KnowledgePO oldKnowledge, KnowledgePO newKnowledge, Long userId);

    void createDeleteOperationLog(Long botId, List<KnowledgePO> deleteKnowledgeList, Long userId);

    void batchCreate(Long botId, List<KnowledgePO> knowledgeList, Long userId);
}
