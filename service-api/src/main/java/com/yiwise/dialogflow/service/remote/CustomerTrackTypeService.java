package com.yiwise.dialogflow.service.remote;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yiwise.account.api.dto.CustomerTrackTypeDTO;
import com.yiwise.base.common.thread.ApplicationExecutorHolder;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.entity.po.remote.CustomerTrackTypePO;
import com.yiwise.dialogflow.service.remote.account.FeignCustomerTrackTypeApi;
import com.yiwise.middleware.redis.service.RedisOpsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/2/11
 */
@Slf4j
@Service
public class CustomerTrackTypeService {

    @Resource
    private FeignCustomerTrackTypeApi feignCustomerTrackTypeApi;

    @Resource
    RedisOpsService redisOpsService;

    private final ConcurrentHashMap<Integer, String> customerTrackTypeMap = new ConcurrentHashMap<>();

    private final AtomicLong localCacheLastUpdateTime = new AtomicLong(0);

    public String getCustomerTrackTypeName(Integer customerTrackType) {
        if (Objects.isNull(customerTrackType)) {
            return "";
        }
        return getCustomerTrackTypeNameFromLocalCache(customerTrackType);
    }

    private String getCustomerTrackTypeNameFromRedisCache(Integer customerTrackType) {
        if (Objects.isNull(customerTrackType)) {
            return "";
        }

        String redisKey = RedisKeyCenter.getCustomerTrackTypeRedisKey(customerTrackType);
        String customerTrackTypeName = redisOpsService.get(redisKey);
        if (StringUtils.isEmpty(customerTrackTypeName)) {
            String trackTypeName = feignCustomerTrackTypeApi.getCustomerTrackTypeName(customerTrackType);
            if (StringUtils.isNotBlank(trackTypeName)) {
                customerTrackTypeName = trackTypeName;
                redisOpsService.set(redisKey, customerTrackTypeName, ApplicationConstant.POJO_CACHE_TIMEOUT_OF_SECOND, TimeUnit.SECONDS);
            } else {
                log.warn("[LogHub_Warn]获取CustomerTrackTypeName出错");
            }
        } else {
            if (!customerTrackTypeMap.containsKey(customerTrackType)) {
                customerTrackTypeMap.put(customerTrackType, customerTrackTypeName);
            }
        }
        return customerTrackTypeName;
    }

    private String getCustomerTrackTypeNameFromLocalCache(Integer customerTrackType) {
        if (Objects.isNull(customerTrackType)) {
            return "";
        }
        asyncFlush();
        if (customerTrackTypeMap.containsKey(customerTrackType)) {
            return customerTrackTypeMap.get(customerTrackType);
        }
        return getCustomerTrackTypeNameFromRedisCache(customerTrackType);
    }

    private void asyncFlush() {
        long now = System.currentTimeMillis();
        long timeout = 10 * 60 * 1000;
        long lastUpdateTime = localCacheLastUpdateTime.get();
        if (now - lastUpdateTime > timeout && localCacheLastUpdateTime.compareAndSet(lastUpdateTime, now)) {
            doAsyncFlush();
        }
    }

    private void doAsyncFlush() {
        log.info("异步加载所有赛道信息");
        ApplicationExecutorHolder.execute("异步加载所有赛道信息", () -> {
            Map<Integer, String> typeNamePairList = feignCustomerTrackTypeApi.selectAllCustomerTraceTypeNamePairList();
            log.info("异步加载所有赛道信息, pairJson={}", typeNamePairList);
            if (MapUtils.isNotEmpty(typeNamePairList)) {
                customerTrackTypeMap.clear();
                typeNamePairList.forEach((key, value) -> {
                    if (Objects.nonNull(key) && Objects.nonNull(value)) {
                        try {
                            customerTrackTypeMap.put(Integer.valueOf(key.toString()), value);
                        } catch (Exception ignore) {

                        }
                    }
                });
                log.info("异步加载所有赛道信息成功, size={}, 最新赛道信息:{}", customerTrackTypeMap.size(), customerTrackTypeMap);
            }
        });
    }

    public List<CustomerTrackTypePO> getCustomerTrackList() {
        return feignCustomerTrackTypeApi.getCustomerTrackTypeListRemote().stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    private CustomerTrackTypePO convert(CustomerTrackTypeDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        return MyBeanUtils.copy(dto, CustomerTrackTypePO.class);
    }
}
