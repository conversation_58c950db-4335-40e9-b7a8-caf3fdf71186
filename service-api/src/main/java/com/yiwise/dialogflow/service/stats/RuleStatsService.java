package com.yiwise.dialogflow.service.stats;

import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.po.stats.IntentRuleStatsPO;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;

import java.util.List;

public interface RuleStatsService {

    List<IntentRuleStatsPO> queryBotAllIntentLevelRuleStatsList(Long botId, BaseStatsQuery condition);
    List<IntentRuleStatsPO> queryBotAllIntentLevelStatsList(Long botId, BaseStatsQuery condition);
    List<IntentRuleStatsPO> queryBotAllIntentActionRuleStatsList(Long botId, BaseStatsQuery condition);
    List<IntentRuleStatsPO> queryBotAllIntentActionStatsList(Long botId, BaseStatsQuery condition);

    void saveRuleStats(BotStatsAnalysisResult analysisResult);

}
