package com.yiwise.dialogflow.service;

/**
 * <AUTHOR>
 * @date 2022/7/29
 */
public interface DataFixService {

    void updateBackgroundVolume(Long botId);

    /**
     * 公共音频库添加‘全部音频’分组,如果不存在的话
     */
    void initAllAudioGroupIfNotExists();

    /**
     * 更新tts初始音量
     */
    void updateTtsVolume();

    /**
     * 变量库修复,历史数据“客户名”和“客户名_总”历史数据修复为自定义变量,新增内置变量"姓名"和“姓名_总"
     */
    void fixVar();


    void fixSystemVar();

    /**
     * 话术行业场景修复
     */
    void fixBot();

    /**
     * 将特殊语境“AI重复上一句”名称修改为“AI重复上句语音”
     */
    void fixAiRepeatName();

    /**
     * BOT自动化生成模板状态
     */
    void fixBotGenerateTemplateStatus();
}
