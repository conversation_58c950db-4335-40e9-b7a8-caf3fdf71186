package com.yiwise.dialogflow.service.llm;

import com.yiwise.dialogflow.entity.po.llm.RagDocumentPO;
import com.yiwise.dialogflow.entity.po.llm.RagDocumentSegmentPO;
import com.yiwise.dialogflow.entity.vo.llm.RagDocumentSegmentVO;

import java.util.List;

/**
 * 文档处理算法服务接口。
 * 该接口定义了对文档及其段落进行上传、删除、添加、更新和搜索等操作的方法。
 */
public interface AlgorithmRagDocService {

    /**
     * 上传文档。
     *
     * @param po 文档数据对象，包含文档的全部信息。
     * @return 返回上传操作是否成功的布尔值。
     */
    boolean uploadDoc(RagDocumentPO po);

    /**
     * 删除文档。
     *
     * @param docId 文档的唯一标识符。
     * @return 返回删除操作是否成功的布尔值。
     */
    boolean deleteDoc(String docId);

    /**
     * 删除文档段落。
     *
     * @param segmentId 段落的唯一标识符。
     * @return 返回删除操作是否成功的布尔值。
     */
    boolean deleteSegment(String segmentId);

    /**
     * 添加段落到文档。
     *
     * @param docId     文档的唯一标识符。
     * @param segmentId 段落的唯一标识符。
     * @param content   段落的内容。
     * @return 返回添加操作是否成功的布尔值。
     */
    boolean addSegment(String docId, String segmentId, String content);

    /**
     * 更新文档段落的内容。
     *
     * @param docId     文档的唯一标识符。
     * @param segmentId 段落的唯一标识符。
     * @param content   更新后的段落内容。
     * @return 返回更新操作是否成功的布尔值。
     */
    boolean updateSegment(String docId, String segmentId, String content);

    /**
     * 搜索文档段落。
     *
     * @param search    搜索关键字。
     * @param docIdList 文档ID列表，限定搜索的文档范围。
     * @return 返回匹配搜索条件的文档段落列表。
     */
    List<RagDocumentSegmentVO> search(String search, List<String> docIdList);

    /**
     * 批量上传文档。
     *
     * @param docId       文档的唯一标识符。
     * @param contentList 段落的内容列表。
     * @return 分片列表。
     */
    List<RagDocumentSegmentPO> batchUpload(String docId, List<String> contentList);
}