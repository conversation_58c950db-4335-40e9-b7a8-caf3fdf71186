package com.yiwise.dialogflow.service.llm;

import com.yiwise.dialogflow.entity.po.llm.RagDocumentSegmentPO;
import com.yiwise.dialogflow.entity.vo.llm.RagDocumentSegmentVO;

import java.util.List;

public interface RagDocumentSegmentService {

    /**
     * 根据文档 id, 查询列表
     *
     * @param botId 话术id
     * @param docId 文档id
     * @return 文档分片列表
     */
    List<RagDocumentSegmentVO> getByDocId(Long botId, String docId);

    /**
     * 根据id列表查询分片
     *
     * @param idList id列表
     * @return 分片列表
     */
    List<RagDocumentSegmentPO> getByIdList(List<String> idList);

    /**
     * 修改文档分片
     *
     * @param request form
     * @param userId  操作人id
     */
    void update(RagDocumentSegmentVO request, Long userId);

    /**
     * 删除文档分片
     *
     * @param docId  文档id
     * @param id     分片id
     * @param userId 操作人id
     */
    void delete(String docId, String id, Long userId);

    /**
     * 根据文档id删除全部分段
     *
     * @param docId 文档id
     */
    void deleteByDocId(String docId);

    /**
     * 根据话术id查询分片
     *
     * @param botId 话术id
     * @return 分片列表
     */
    List<RagDocumentSegmentPO> getByBotId(Long botId);
}
