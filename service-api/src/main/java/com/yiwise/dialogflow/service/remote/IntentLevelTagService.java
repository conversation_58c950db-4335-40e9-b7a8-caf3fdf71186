package com.yiwise.dialogflow.service.remote;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yiwise.dialogflow.api.dto.response.SimpleIntentLevelTag;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.remote.IntentLevelTagPO;
import com.yiwise.dialogflow.service.BotRefService;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.openfeign.RemoteServiceAdapter;
import com.yiwise.middleware.redis.service.RedisOpsService;
import lombok.extern.java.Log;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/2/11
 */
@Slf4j
@Service
public class IntentLevelTagService {

    @Resource
    RemoteServiceAdapter remoteServiceAdapter;

    @Resource
    RedisOpsService redisOpsService;

    @Lazy
    @Resource
    private BotRefService botRefService;

    @Lazy
    @Resource
    private BotService botService;

    @Resource
    private IntentLevelTagDetailService intentLevelTagDetailService;

    private final Cache<Long, String> intentLevelCache = CacheBuilder.newBuilder()
            .maximumSize(2000)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    /**
     * ai-call-back端查询时会写入缓存
     */
    public String getIntentLevelTagName(Long intentLevelTagId) {
        if (Objects.isNull(intentLevelTagId)) {
            return "";
        }
        if (intentLevelTagId == 0) {
            return "内置意向分类标签";
        }

        String intentLevelTagName = intentLevelCache.getIfPresent(intentLevelTagId);
        if (StringUtils.isNotBlank(intentLevelTagName)) {
            return intentLevelTagName;
        }

        log.info("getIntentLevelTagName, intentLevelTagId: {}", intentLevelTagId);
        String redisKey = RedisKeyCenter.getIntentLevelTagRedisKey(intentLevelTagId);
        IntentLevelTagPO intentLevelTagPO = redisOpsService.get(redisKey, IntentLevelTagPO.class);
        if (Objects.nonNull(intentLevelTagPO)) {
            intentLevelTagName = intentLevelTagPO.getName();
        } else {
            intentLevelTagName = remoteServiceAdapter.invoke("intentLevelTagServiceImpl", "getIntentLevelTagName", String.class, intentLevelTagId);
        }

        return intentLevelTagName;
    }

    public Map<Long, String> getIntentLevelTagNameMap(List<Long> intentLevelTagIdList) {
        if (CollectionUtils.isEmpty(intentLevelTagIdList)) {
            return Collections.emptyMap();
        }

        Map<Long, String> result = new HashMap<>();
        List<Long> miscacheIdList = new ArrayList<>();
        for (Long intentLevelTagId : intentLevelTagIdList) {
            if (intentLevelTagId == 0) {
                result.put(intentLevelTagId, "内置意向分类标签");
                continue;
            }
            String intentLevelTagName = intentLevelCache.getIfPresent(intentLevelTagId);
            if (StringUtils.isNotBlank(intentLevelTagName)) {
                result.put(intentLevelTagId, intentLevelTagName);
            } else {
                miscacheIdList.add(intentLevelTagId);
            }
        }

        if (CollectionUtils.isEmpty(miscacheIdList)) {
            return result;
        }

        Map invokeResult = remoteServiceAdapter.invoke("intentLevelTagServiceImpl", "getIntentLevelTagNameMap", Map.class, miscacheIdList.stream().distinct().collect(Collectors.toList()));
        if (MapUtils.isNotEmpty(invokeResult)) {
            try {
                invokeResult.forEach((k, v) -> {
                    Long id = Long.parseLong(k.toString());
                    String name = v.toString();
                    result.put(id, name);
                    intentLevelCache.put(id, name);
                });
            } catch (Exception ignore) {

            }
        }
        return result;
    }

    public SimpleIntentLevelTag getSimpleDetailInfoByDialogFlowId(Long dialogFlowId) {
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            return null;
        }

        BotPO bot = botService.getById(botId);
        if (Objects.isNull(bot)) {
            return null;
        }
        Long intentLevelTagId = bot.getIntentLevelTagId();
        SimpleIntentLevelTag simpleIntentLevelTag = new SimpleIntentLevelTag();
        simpleIntentLevelTag.setIntentLevelTagId(intentLevelTagId);
        simpleIntentLevelTag.setDetailList(intentLevelTagDetailService.getIntentLevelTagDetailList(intentLevelTagId));
        return simpleIntentLevelTag;
    }

}
