package com.yiwise.dialogflow.service;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.api.dto.response.variable.VariableInfoDTO;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.entity.enums.VariableTypeEnum;
import com.yiwise.dialogflow.entity.po.VariablePO;
import com.yiwise.dialogflow.entity.query.VariableQuery;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.UserVariableNameResult;
import com.yiwise.dialogflow.entity.vo.VariableBindInfoVO;
import com.yiwise.dialogflow.entity.vo.VariableVO;
import com.yiwise.dialogflow.entity.vo.sync.VariableSyncRequestVO;
import com.yiwise.dialogflow.entity.vo.sync.VariableSyncResultVO;
import javaslang.Tuple2;

import java.util.*;

public interface VariableService {

    List<VariablePO> recoverySystemVariable(Long botId);

    List<VariablePO> initOnCreateBot(Long botId);

    List<VariablePO> getListByBotId(Long botId);

    PageResultObject<VariableVO> getPageByBotId(VariableQuery query);

    List<VariablePO> getByIdList(Collection<String> variableIdList);

    Map<String, String> getNameIdMapByBotId(Long botId);

    VariablePO update(VariablePO variable, Long userId);

    VariablePO getById(Long botId, String id);

    VariablePO create(VariablePO variable);

    /**
     * 校验变量名称是否合法
     * @param varName 变量名称
     * @return T/F
     */
    boolean isValidVarName(String varName);

    /**
     * 自动创建不存在的变量
     * @param botId 话术id
     * @param varNameList 变量名称列表
     * @param userId 操作人id
     */
    Map<String, String> autoCreateIfNotExists(Long botId, List<String> varNameList, Long userId);

    Boolean autoCreateIfNotExists(Long botId, String text, Long userId);

    Tuple2<Boolean, Map<String, String>> autoCreateIfNotExists(Long botId, List<String> textList, VariableTypeEnum defaultType, Long userId);

    VariablePO deleteById(Long botId, String variableId, Long currentUserId);

    /**
     * 已使用的自定义变量/系统变量列表, 默认不包含动态变量
     * 查询的数据为当前数据, 不是已发布的快照中的数据
     * @param botId  botId
     * @return 已使用的自定义变量名称集合
     */
    Set<String> getRealUsedVariableNameSet(Long botId);

    Set<String> getRealUsedVariableNameSet(Long botId, Long callJobId, boolean containsDynamicVar, boolean containsOptionalVar);

    UserVariableNameResult getRealUsedVariableNameResult(Long botId, Long callJobId, boolean containsDynamicVar, boolean containsOptionalVar);

    Map<Long, Set<String>> getRealUsedVariableNameSetByBotIdList(List<Long> botIdList);

    Map<Long, Set<String>> getRealUsedVariableNameSetByBotIdList(List<Long> botIdList, boolean containsDynamicVar);

    /**
     * 从快照找获取已使用的变量名称列表
     * 如果快照中没有该字段, 则查询最新的数据
     * @param botId
     * @param snapshotType 快照类型(用于文本测试,语音测试,外呼)
     * @return
     */
    Set<String> getLastSnapshotUsedVariableNameSet(Long botId, RobotSnapshotUsageTargetEnum snapshotType);


    Set<String> getLastSnapshotUsedVariableNameSetByDialogFlowId(Long dialogFlowId, RobotSnapshotUsageTargetEnum snapshotType);

    /**
     * 获取已使用的自定义变量/系统变量, 不包含动态变量
     * @param dialogFlowId ope那边的话术id
     * @return 已使用的自定义变量
     */
    Set<String> getUsedVariableNameSetByDialogFlowId(Long dialogFlowId, Long callJobId);

    /**
     * 批量删除变量
     *
     * @param botId
     * @param idList
     * @param userId
     */
    void deleteByIdList(Long botId, List<String> idList, boolean selectAll, Long userId);

    List<VariablePO> getListByBotIdList(List<Long> botIdList);

    List<VariableBindInfoVO> getUsedVariableBindInfoList(Long botId, Long tenantId);

    List<IdNamePair<String, String>> getDynamicVariableIdNamePairList(Long botId);

    /**
     * 客户解绑时，清空动态变量中的客户属性id
     */
    void clearCustomerAttribute(Long botId);

    /**
     * 将srcBot下的srcVariableId复制到targetBot下, 判断srcVariableId同名变量是否存在, 存在则不复制
     * @param srcBotId
     * @param targetBotId
     * @param srcVariableId
     * @return
     */
    String copyVariableByNameIfAbsent(Long srcBotId, Long targetBotId, String srcVariableId, Long userId);

    List<VariableInfoDTO> getUsedVariableListByDialogFlowId(Long dialogFlowId);

    List<VariableInfoDTO> getUsingTemplateVariableListByDialogFlowId(Long dialogFlowId);

    /**
     * 根据老话术id查询动态变量名称集合
     *
     * @param dialogFlowId 老话术id
     * @return 动态变量名称集合
     */
    List<String> listAllDynamicVariableNameByDialogFlowId(Long dialogFlowId);

    List<VariablePO> getTemplateVariableByBotIdList(List<Long> botIdList);

    List<VariablePO> getUsedTemplateVarListByBotId(Long botId);

    /**
     * 变量库中是否存在模板变量
     *
     * @param botId 话术id
     * @return T/F
     */
    boolean existsTemplateVariable(Long botId);

    Map<String, String> syncDependVariable(List<VariablePO> variableList, Long targetBotId, Long currentUserId);

    VariableSyncResultVO sync(VariableSyncRequestVO syncRequest);
}
