package com.yiwise.dialogflow.service.remote;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yiwise.dialogflow.api.dto.response.SimpleIntentLevelTagDetail;
import com.yiwise.dialogflow.service.openfeign.RemoteServiceAdapter;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class IntentLevelTagDetailService {

    @Resource
    RemoteServiceAdapter remoteServiceAdapter;

    private static final Cache<Long, Map<Integer, String>> CACHE = CacheBuilder
            .newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(10, java.util.concurrent.TimeUnit.MINUTES)
            .build();

    public Map<Integer, String> getIntentLevelTagDetailCode2NameMap(Long intentLevelTagId) {
        if (Objects.isNull(intentLevelTagId)) {
            return Collections.emptyMap();
        }
        Map<Integer, String> map = CACHE.getIfPresent(intentLevelTagId);
        if (Objects.nonNull(map)) {
            return map;
        }
        TypeReference<HashMap<Integer, String>> typeRef = new TypeReference<HashMap<Integer, String>>() {};
        Map<Integer, String> result = remoteServiceAdapter.invoke("intentLevelTagDetailServiceImpl", "getIntentLevelTagDetailCode2NameMap", typeRef, intentLevelTagId);
        if (Objects.nonNull(result)) {
            CACHE.put(intentLevelTagId, result);
        }
        return result;
    }

    public List<SimpleIntentLevelTagDetail> getIntentLevelTagDetailList(Long intentLevelTagId) {
        Map<Integer, String> code2NameMap = getIntentLevelTagDetailCode2NameMap(intentLevelTagId);
        if (MapUtils.isEmpty(code2NameMap)) {
            return Collections.emptyList();
        }
        return new ArrayList<>(code2NameMap.entrySet())
               .stream()
               .map(entry -> {
                     SimpleIntentLevelTagDetail detail = new SimpleIntentLevelTagDetail();
                     detail.setCode(entry.getKey());
                     detail.setName(entry.getValue());
                     return detail;
               })
                .sorted(Comparator.comparing(SimpleIntentLevelTagDetail::getCode))
               .collect(Collectors.toList());
    }
}
