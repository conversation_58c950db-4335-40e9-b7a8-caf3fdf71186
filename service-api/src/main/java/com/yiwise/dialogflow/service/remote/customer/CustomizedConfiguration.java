package com.yiwise.dialogflow.service.remote.customer;


import com.yiwise.common.cloud.feign.helper.decode.FeignResultDecoder;
import feign.Request;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.codec.Decoder;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import static com.yiwise.common.cloud.feign.helper.constant.CommonConstant.MDC_TRACE_ID;

@Slf4j
public class CustomizedConfiguration implements RequestInterceptor {

    @Value("${service.feign.connectTimeout:30000}")
    private int connectTimeout;

    @Value("${service.feign.readTimeOut:3000}")
    private int readTimeout;

    @Bean
    public Request.Options options() {
        return new Request.Options(connectTimeout, readTimeout, true);
    }

    @Bean
    public Decoder feignDecoder() {
        return new FeignResultDecoder();
    }

    @Override
    public void apply(RequestTemplate requestTemplate) {
        String traceId = MDC.get(MDC_TRACE_ID);
        log.info("CustomizedConfiguration openFeign traceId={}", traceId);
        requestTemplate.header(MDC_TRACE_ID, traceId);
    }
}
