package com.yiwise.dialogflow.service;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.api.dto.response.knowledge.SimpleKnowledge;
import com.yiwise.dialogflow.entity.bo.ActionNameResourceBO;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.bo.SnapshotValidateConfigBO;
import com.yiwise.dialogflow.entity.po.KnowledgePO;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.entity.vo.stats.SimpleKnowledgeStatsInfoVO;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.KnowledgeSyncVO;
import javaslang.Tuple2;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface KnowledgeService {

    Map<String, String> getNameByIdList(Collection<String> knowledgeIdList);

    String getNameById(String knowledgeId);

    List<KnowledgePO> getByIdList(Collection<String> knowledgeIdList);

    KnowledgeVO create(KnowledgePO form, Long userId);

    void batchCreate(KnowledgeBatchCreateVO batchCreateVO, Long userId);

    KnowledgeVO update(KnowledgePO form, Long userId);

    void validAndThrow(KnowledgePO knowledge);

    void validAndThrow(KnowledgePO knowledge, DependentResourceBO dependentResource, ActionNameResourceBO actionResource, SnapshotValidateConfigBO validateConfig);

    PageResultObject<KnowledgeVO> queryByCondition(KnowledgeQueryVO condition);

    /**
     * 知识库导出
     *
     * @param condition 查询参数
     * @return oss链接
     */
    String exportKnowledge(KnowledgeBatchOperateRequestVO condition);

    /**
     * 导入知识
     *
     * @param excel  模板文件
     * @param botId  话术id
     * @param userId 操作人id
     * @return 错误信息链接
     */
    KnowledgeImportResultVO importKnowledge(MultipartFile excel, Long botId, Long userId);

    void changeGroup(KnowledgeChangeGroupVO condition, Long userId);

    List<KnowledgePO> getAllListByBotId(Long botId);

    KnowledgePO getById(Long botId, String id);

    KnowledgeVO getVOById(Long botId, String id);

    List<KnowledgePO> getByIdList(Long botId, List<String> idList);

    KnowledgePO deleteById(Long botId, String knowledgeId, Long userId);

    /**
     * 根据 id 批量删除, 会出现跨 bot 情况, 所以待删除的流程botId 一定在 botIdList 范围中
     * 删除时按 bot 分组分批删除, 删除过程中出现异常不会回滚, 并不会继续删除后续的 bot
     * @param botIdList botId 列表
     * @param knowledgeIdList knowledgeIdList 列表
     * @param userId 操作人
     * @return 删除的知识列表
     */
    List<KnowledgePO> deleteByBotIdsAndKnowledgeIdList(List<Long> botIdList, List<String> knowledgeIdList, Long userId);

    List<KnowledgePO> batchDelete(KnowledgeBatchOperateRequestVO condition, Long userId);

    void updateDependVariableName(Long botId, String variableId, String oldVariableName, String newVariableName);

    List<KnowledgePO> getByDependStepId(Long botId, String stepId);

    List<Tuple2<KnowledgePO, List<String>>> getByDependStepIdList(Long botId, List<String> stepIdList);

    void updateAnswerList(KnowledgePO knowledge);

    void updateActionList(KnowledgePO knowledge);

    List<String> listIdByBotIdAndGroupIds(Long botId, List<String> knowledgeGroupIdList);

    List<SimpleKnowledgeStatsInfoVO> queryAllKnowledgeStatsInfo(KnowledgeQueryVO condition);

    BotSyncResultVO sync(KnowledgeSyncVO syncVO);

    void resetAllKnowledgeLabel(Long newBotId);

    void resetResourceReferenceInfo(Long newBotId);

    void deleteByNameList(Long newBotId, List<String> ignoreKnowledgeList);

    void updateAnswerListAndVariableRefInfo(Long botId, List<KnowledgePO> updateKnowledgeList, Long userId);

    List<SimpleKnowledge> getSimpleKnowledgeListByDialogFlowId(Long dialogFlowId);

    void updateBySimpleKnowledge(SimpleKnowledge simpleKnowledge, Long userId);

    /**
     * 根据botId和groupName查询知识库
     * @param botId botId
     * @param groupName 分组名称, 忽略分组结构, 分组为空的话,返回空列表
     * @return 知识库列表
     */
    List<SimpleKnowledge> queryByGroupName(Long botId, String groupName);

    List<KnowledgePO> searchKnowledgeTitle(Long botId, String searchText);
}
