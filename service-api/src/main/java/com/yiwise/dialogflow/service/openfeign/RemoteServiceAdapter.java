package com.yiwise.dialogflow.service.openfeign;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.enums.CodeDescEnum;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/1/28
 */
@Slf4j
@Service
public class RemoteServiceAdapter extends com.yiwise.cloud.aicc.service.remote.DefaultRemoteServiceAdapter {

    @Resource
    protected OpeRemoteService opeRemoteService;

    public <E> E invoke(SystemEnum systemEnum, String serviceName, String methodName, TypeReference<E> typeReference, Object... args) {
        String result;
        if (systemEnum == SystemEnum.OPE) {
            result = opeRemoteService.invoke(serviceName, methodName, JsonUtils.object2String(args));
        } else {
            result = remoteService.invoke(serviceName, methodName, JsonUtils.object2String(args));
        }
        return handleResult(result, typeReference, null);
    }


    public <E> E invoke(SystemEnum systemEnum, String serviceName, String methodName, Class<E> clazz, Object... args) {
        String result;
        if (systemEnum == SystemEnum.OPE) {
            String param = JsonUtils.object2String(args);
            log.info("invoke serviceName:{}, methodName:{}, args:{}", serviceName, methodName, param);
            result = opeRemoteService.invoke(serviceName, methodName, param);
            log.info("invoke result:{}", result);
        } else {
            String param = JsonUtils.object2String(args);
            log.info("invoke serviceName:{}, methodName:{}, args:{}", serviceName, methodName, param);
            result = remoteService.invoke(serviceName, methodName, param);
            log.info("invoke result:{}", result);
        }
        return handleResult(result, null, clazz);
    }

    public <E> E handleResult(String result, TypeReference<E> typeReference, Class<E> clazz) {
        ResultObject r = JsonUtils.string2Object(result, ResultObject.class);
        if (SUCCESS.equals(r.getCode().toString())) {
            if (clazz == null) {
                return JsonUtils.string2Object(String.valueOf(r.getData()), typeReference);
            } else {
                return JsonUtils.string2Object(String.valueOf(r.getData()), clazz);
            }
        }
        throw new ComException(CodeDescEnum.getFromCodeOrNull(ComErrorCode.class, r.getCode()), r.getResultMsg());
    }
}
