package com.yiwise.dialogflow.service.feishu;

import com.yiwise.dialogflow.entity.bo.feishu.AbstractAlertConfig;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/10/17
 */
public interface FeishuAlertService {
    Optional<CircuitBreaker> getCircuitBreakerByTopic(String topic);

    void registryAlertConfig(AbstractAlertConfig config);
}
