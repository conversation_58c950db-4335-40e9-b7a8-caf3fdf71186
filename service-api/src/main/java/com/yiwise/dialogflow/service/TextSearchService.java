package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.entity.vo.BotTextSearchReplaceRequestVO;
import com.yiwise.dialogflow.entity.vo.BotTextSearchReplaceResultVO;
import com.yiwise.dialogflow.entity.vo.MutiBotTextSearchReplaceRequestVO;
import com.yiwise.dialogflow.entity.vo.MutiBotTextSearchReplaceResultVO;

import java.util.List;

public interface TextSearchService {
    List<MutiBotTextSearchReplaceResultVO> search(BotTextSearchReplaceRequestVO botTextSearchReplaceRequestVO);

    void replace(MutiBotTextSearchReplaceRequestVO mutiBotTextSearchReplaceRequestVO);

    void batchUpdate(MutiBotTextSearchReplaceRequestVO request);

    void delete(MutiBotTextSearchReplaceRequestVO mutiBotTextSearchReplaceRequestVO);
}
