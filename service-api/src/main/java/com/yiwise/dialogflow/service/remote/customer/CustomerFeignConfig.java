package com.yiwise.dialogflow.service.remote.customer;

import com.yiwise.cloud.aicc.service.remote.ServiceFeignConfig;
import com.yiwise.common.cloud.feign.helper.decode.FeignResultDecoder;
import feign.codec.Decoder;
import org.springframework.context.annotation.Bean;

public class CustomerFeignConfig extends ServiceFeignConfig {

    @Bean
    public Decoder feignDecoder() {
        return new FeignResultDecoder();
    }
}