package com.yiwise.dialogflow.service.operationlog;

import com.yiwise.dialogflow.entity.enums.SyncModeEnum;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.VariablePO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/11
 */
public interface VariableOperationLogService {

    void addVar(VariablePO variable);

    void batchCreateVar(Long botId, List<String> varNameList, Long userId);

    void updateVar(VariablePO oldVar, VariablePO newVar);

    void deleteVar(VariablePO variable, Long curUserId);

    void batchDeleteVar(Long botId, List<VariablePO> varList, Long curUserId);

    void syncVariable(BotPO srcBot, List<VariablePO> syncVarList, List<BotPO> targetBotList, SyncModeEnum syncMode, Long userId);
}
