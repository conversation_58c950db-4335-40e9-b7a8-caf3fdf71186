package com.yiwise.dialogflow.service;

import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

public interface CallStatsMongoService {

    /**
     * mongo某个key新增1
     * @param collectionName mongo集合名词
     * @param key mongo集合列名称
     * @param query  修改的条件
     */
    void incrementValue(String collectionName, Query query, String key);
    /**
     * mongo某个key新增1
     * @param collectionName mongo集合名词
     * @param key mongo集合列名称
     * @param query  修改的条件
     * @param value 修改的值
     */
    void incrementValue(String collectionName, Query query, String key, Long value);
    /**
     * 修改mongo数据
     */
    void updateMongoData(String collectionName, Query query, Update update);

    /**
     * 修改mongo数据，使用缓存
     * 由于频繁对mongo更新对mongo的写性能影响较大，可以先将数据缓存到内存中聚合，然后每隔5分钟定时写入mongo
     * 该方法的使用限制条件：
     * 1-统计数据不要求强实时，允许有一定的延迟
     * 2-update的内容都是inc操作
     * 3-对于upsert有可能插入多条记录，该方法没有synchronized关键字，也不要求表中建唯一索引，需要在查询聚合数据时使用aggreate查询
     * 4-数据能给在内存中聚合，不能聚合的数据使用缓存没有意义，而且会增加性能开销
     */
    void updateMongoDataUsingCache(String collectionName, Query query, Update update);

    /**
     * mongo某个key新增value
     * @param tenantId 客户ID
     * @param callStatsId 任务/话术ID
     * @param collectionName mongo集合名词
     * @param key mongo集合列名称
     * @param value 修改的值
     */
    void incrementTotalTask(Long tenantId, Long callStatsId, Long dialogFlowId, String collectionName, String key, int value);

    /**
     * 缓存是否还有待写回mongo的数据
     */
    boolean needFlushToMongo();

    /**
     * 将内存缓存的数据写回mongo
     */
    void flushToMongo();
    void flushMemoryToMongo(String collectionName);
    void flushRedisToMongo(String collectionName);

    void setCollectionCacheInfo(String collectionName, boolean isRedis, long flushInterval);
}
