package com.yiwise.dialogflow.service;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BotResourceSequenceService {

    String MAIN_STEP_ORDER_NUM = "MainStepOrderNum";



    /**
     * 为bot下的resource生成一个递增的序号
     * @param botId botId
     * @param resourceType 资源类型
     * @return 序号
     */
    Integer generate(Long botId, String resourceType);

    /**
     * 为bot下的resource生成 count 个递增的序号
     * @param botId botId
     * @param resourceType 资源类型
     * @param count 序号个数, > 0
     * @return count个序号列表
     */
    List<Integer> generate(Long botId, String resourceType, int count);

    void copySequence(Long fromBotId, Long toBotId);

    void cleanAllSequence(Long newBotId);

    void updateSeqIfCurrentLtNew(Long botId, String resourceType, Integer newSeq);

    void renameResourceType(Long botId, String oldResourceType, String newResourceType);
}
