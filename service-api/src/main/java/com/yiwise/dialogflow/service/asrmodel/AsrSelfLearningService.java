package com.yiwise.dialogflow.service.asrmodel;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.bo.asr.AsrLmModel;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrSelfLearningDetailPO;
import com.yiwise.dialogflow.entity.query.AsrSelfLearningDetailQuery;
import com.yiwise.dialogflow.entity.vo.BotVO;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrSelfLearningDetailVO;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/19 14:52:16
 */
public interface AsrSelfLearningService {
    PageResultObject<AsrSelfLearningDetailVO> list(AsrSelfLearningDetailQuery query);

    void update(AsrSelfLearningDetailVO asrSelfLearningDetailVO);

    void stop(AsrSelfLearningDetailVO asrSelfLearningDetailVO);

    void start(AsrSelfLearningDetailVO asrSelfLearningDetailVO);

    void delete(AsrSelfLearningDetailVO asrSelfLearningDetailVO);

    PageResultObject<BotVO> getBotList(Long asrSelfLearningDetailId, String botInfo, Integer pageSize, Integer pageNum);

    void unbindBot(AsrSelfLearningDetailVO asrSelfLearningDetailVO);

    void bindBot(AsrSelfLearningDetailVO asrSelfLearningDetailVO);

    AsrSelfLearningDetailPO getById(Long asrSelfLearningDetailId);

    void aliCreateAndTrainModel(AsrSelfLearningDetailPO asrSelfLearningDetailPO);

    void aliTrainModel(AsrSelfLearningDetailPO asrSelfLearningDetailPO, String asrLmModelId);

    void trainSelfLearning(Long tenantId, Long currentUserId, Long asrSelfLearningDetailId);

    void tencentCreateAndTrainModel(AsrSelfLearningDetailPO asrSelfLearningDetailPO, Long tenantId, Long userId);

    List<AsrSelfLearningDetailPO> getByName(String name);

    void deleteAllProviderData(Integer pageNumber, Integer pageSize);

    AsrLmModel.LmModelPage listAliData(Integer pageNumber, Integer pageSize);

    AsrLmModel.LmModelPage listTencentData(Integer pageSize);
}