package com.yiwise.dialogflow.service.remote;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yiwise.account.api.dto.UserDetailDTO;
import com.yiwise.account.api.dto.UserQueryDTO;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.service.remote.account.UserApiService;
import com.yiwise.middleware.redis.service.RedisOpsService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/17
 */
@Service
public class UserService {
    @Resource
    RedisOpsService redisOpsService;

    @Resource
    private UserApiService feignUserApi;

    private Cache<Long, UserPO> userCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    public UserPO getUserById(Long id) {
        if (id == null) {
            return null;
        }

        if (ApplicationConstant.OPEN_API_USER_ID.equals(id)) {
            UserPO user = new UserPO();
            user.setUserId(id);
            user.setName("开放平台");
            user.setPhoneNumber("0000");
            user.setGender("FEMALE");
            return user;
        }

        UserPO user = userCache.getIfPresent(id);
        if (Objects.nonNull(user)) {
            return user;
        }

        String redisKey = RedisKeyCenter.getUserPrimaryRedisKey(id);
        if (redisOpsService.isKeyExist(redisKey)) {
            user = redisOpsService.get(redisKey, UserPO.class);
        }
        if (Objects.isNull(user)) {
            UserDetailDTO userDetail = feignUserApi.getUserById(id);
            if (Objects.isNull(userDetail)) {
                return null;
            }
            user = convert(userDetail);
        }
        userCache.put(id, user);
        return user;
    }

    public Optional<UserPO> getUserByIdIfPresent(Long id) {
        try {
            return Optional.ofNullable(getUserById(id));
        } catch (ComException e) {
            return Optional.empty();
        }
    }

    public void logout(String token, Long userId, SystemEnum system) {
        feignUserApi.logout(token, userId, system);
    }

    public List<UserPO> getUserByIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        List<Long> missUserIdList = new ArrayList<>();
        List<UserPO> userList = userIdList.stream()
                .map(userId -> {
                    UserPO user = userCache.getIfPresent(userId);
                    if (Objects.isNull(user)) {
                        missUserIdList.add(userId);
                    }
                    return user;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(missUserIdList)) {
            return userList;
        }
        UserQueryDTO query = new UserQueryDTO();
        query.setUserIds(missUserIdList);

        List<UserDetailDTO> users = feignUserApi.getAllByIdListRemote(query);
        if (CollectionUtils.isNotEmpty(users)) {
            userList.addAll(users.stream().map(this::convert).collect(Collectors.toList()));
            users.forEach(user -> userCache.put(user.getUserId(), convert(user)));
        }
        return userList;
    }

    private UserPO convert(UserDetailDTO userDetail) {
        UserPO user = new UserPO();
        user.setUserId(userDetail.getUserId());
        user.setTenantId(userDetail.getTenantId());
        user.setDistributorId(userDetail.getDistributorId());
        user.setName(userDetail.getName());
        user.setPhoneNumber(userDetail.getPhoneNumber());
        if (Objects.nonNull(userDetail.getGender())) {
            user.setGender(userDetail.getGender().name());
        }
        return user;
    }
}
