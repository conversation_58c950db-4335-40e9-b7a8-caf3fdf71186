package com.yiwise.dialogflow.service.asrmodel;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrVocabDetailPO;
import com.yiwise.dialogflow.entity.query.AsrVocabDetailQuery;
import com.yiwise.dialogflow.entity.vo.BotVO;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrVocabDetailVO;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/19 09:44:11
 */
public interface AsrVocabService {
    List<AsrVocabDetailVO> list(AsrVocabDetailVO asrVocabDetailVO);

    PageResultObject<AsrVocabDetailVO> listWithPage(AsrVocabDetailQuery asrVocabDetailQuery);

    void update(AsrVocabDetailVO asrVocabDetailVO);

    void delete(AsrVocabDetailVO asrVocabDetailVO);

    void stop(AsrVocabDetailVO asrVocabDetailVO);

    PageResultObject<BotVO> getBotList(Long asrVocabId, String botInfo, Integer pageSize, Integer pageNum);

    void unbindBot(AsrVocabDetailVO asrVocabDetailVO);

    void bindBot(AsrVocabDetailVO asrVocabDetailVO);

    void start(AsrVocabDetailVO asrVocabDetailVO);

    AsrVocabDetailPO getById(Long asrVocabId);

    List<AsrVocabDetailPO> getByName(String name);
}