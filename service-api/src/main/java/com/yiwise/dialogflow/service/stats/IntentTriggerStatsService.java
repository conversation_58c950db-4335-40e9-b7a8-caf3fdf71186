package com.yiwise.dialogflow.service.stats;

import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.po.stats.IntentTriggerStatsPO;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;
import com.yiwise.dialogflow.entity.vo.stats.IntentTriggerStatsVO;
import com.yiwise.dialogflow.entity.vo.stats.NodeJumpStatsVO;

import java.util.List;

public interface IntentTriggerStatsService {
    List<IntentTriggerStatsPO> queryIntentTriggerStats(Long botId, BaseStatsQuery condition);
    List<IntentTriggerStatsPO> queryIntentTriggerKnowledgeStats(Long botId, List<String> knowledgeIdList, BaseStatsQuery condition);
    List<IntentTriggerStatsPO> queryIntentTriggerSpecialAnswerStats(Long botId, BaseStatsQuery condition);

    void saveIntentTriggerStats(BotStatsAnalysisResult analysisResult);

}
