package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.entity.enums.PosterTypeEnum;
import lombok.Data;

import javax.persistence.Column;

@Data
public class BatchCreateQRCodeRequest extends PreBatchCreateQRCodeRequest{

    /**
     * 线路ID
     */
    private Long phoneNumberId;

    /**
     * 二维码呼频限制
     */
    private Integer callOutLimit;

    /**
     * 二维码有效时长
     */
    private Integer timeoutHour;

    /**
     * 海报类型
     */
    @Column
    private PosterTypeEnum posterType;

    private Long userId;
}
