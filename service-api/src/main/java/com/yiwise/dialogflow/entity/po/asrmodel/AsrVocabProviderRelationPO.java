package com.yiwise.dialogflow.entity.po.asrmodel;


import com.yiwise.base.model.bean.po.BaseTimePO;
import com.yiwise.dialogflow.engine.share.enums.AsrProviderEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 热词组与厂商关联表
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "asr_vocab_provider_relation")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrVocabProviderRelationPO extends BaseTimePO implements Serializable {

	@Id
	@GeneratedValue(generator = "JDBC")
	Long asrVocabProviderRelationId;

    /**
     * 热词组id
     */
    Long asrVocabId;

    /**
     * 供应商
     */
    AsrProviderEnum provider;

    /**
     * 供应商侧热词组id
     */
    String vocabularyId;

}
