package com.yiwise.dialogflow.entity.vo.node;

import com.yiwise.dialogflow.entity.enums.NodeTypeEnum;
import com.yiwise.dialogflow.entity.po.NodeAnswer;
import lombok.Data;

import java.util.List;

@Data
public class SimpleNodeInfoVO {
    /**
     * 节点id
     */
    String id;
    /**
     * 节点名称
     */
    String name;
    /**
     * 节点标签
     */
    String label;
    /**
     * 流程id
     */
    String stepId;
    /**
     * 流程名称
     */
    String stepName;
    /**
     * 流程标签
     */
    String stepLabel;
    /**
     * 节点类型
     */
    NodeTypeEnum type;

    List<NodeAnswer> answerList;
}
