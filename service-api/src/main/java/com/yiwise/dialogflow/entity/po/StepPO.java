package com.yiwise.dialogflow.entity.po;


import com.google.common.collect.Lists;
import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.entity.enums.StepCategoryEnum;
import com.yiwise.dialogflow.entity.enums.StepSubTypeEnum;
import com.yiwise.dialogflow.entity.enums.StepTypeEnum;
import com.yiwise.dialogflow.entity.enums.llm.LlmStepTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Document(collection = StepPO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_botId", def = "{botId: 1}")
)
public class StepPO extends BaseTimeUserIdPO implements Labelled, Serializable {

    public static final String COLLECTION_NAME = "step";

    /**
     * id
     */
    @Id
    private String id;

    /**
     * botId
     */
    private Long botId;

    /**
     * 标签
     */
    private String label;

    /**
     * 名称
     */
    private String name;

    /**
     * 区分主流程和独立流程
     */
    private StepTypeEnum type;

    /**
     * 作用于主流程的序号
     */
    private Integer orderNum;

    /**
     * 区分一般和业务
     */
    private StepCategoryEnum category;

    /**
     * 是否意图触发
     */
    private Boolean triggerByIntent;

    public Boolean getTriggerByIntent() {
        if (StepTypeEnum.INDEPENDENT.equals(type)) {
            return true;
        }
        return triggerByIntent;
    }

    /**
     * 触发意图列表
     */
    private List<String> triggerIntentIdList;

    public List<String> getTriggerIntentIdList() {
        if (Objects.isNull(triggerIntentIdList)) {
            triggerIntentIdList = Lists.newArrayList();
        }
        return triggerIntentIdList;
    }

    /**
     * 是否是客户关注点
     */
    private Boolean isCustomerConcern;

    /**
     * 是否开启流程跳过条件
     */
    private Boolean enableSkipCondition;

    public Boolean getEnableSkipCondition() {
        return BooleanUtils.isTrue(enableSkipCondition) && StepTypeEnum.MAIN.equals(getType());
    }

    /**
     * 流程跳过条件
     */
    private StepSkipConditionPO skipCondition;

    /**
     * 流程子类别
     */
    private StepSubTypeEnum subType;

    public StepSubTypeEnum getSubType() {
        if (Objects.isNull(subType)) {
            subType = StepSubTypeEnum.COMMON;
        }
        return subType;
    }

    /**
     * 大模型流程类型
     */
    private LlmStepTypeEnum llmStepType;

    /**
     * 流程描述
     */
    private String desc;

    public boolean isLlmStep() {
        return StepSubTypeEnum.isLlm(subType);
    }

    /**
     * 大模型模型名称, 如果是大模型流程时, 可以选择该名称
     */
    private String llmModelName;

    public String getLlmModelName() {
        if (StringUtils.isBlank(llmModelName)) {
            llmModelName = "基础版";
        }
        return llmModelName;
    }
}
