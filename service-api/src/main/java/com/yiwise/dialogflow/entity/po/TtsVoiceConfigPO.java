package com.yiwise.dialogflow.entity.po;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
public class TtsVoiceConfigPO {

    /**
     * tts 音量
     */
    private Float ttsVolume;

    /**
     * tts 语速
     */
    private Float ttsSpeed;

    /**
     * tts音色
     */
    private String ttsVoice;

    /**
     * 音色名称缓存
     */
    private String ttsVoiceName;

    public String getTtsVoiceName() {
        if (StringUtils.isBlank(ttsVoice)) {
            return null;
        }
        return ttsVoiceName;
    }


}
