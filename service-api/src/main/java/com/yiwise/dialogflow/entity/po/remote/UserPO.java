package com.yiwise.dialogflow.entity.po.remote;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserPO implements Serializable {

    Long tenantId;

    Long distributorId;

    Long userId;

    String name;

    String phoneNumber;

    /**
     * 性别:
     *     UNKNOWN(0, "未知"),
     *     MALE(1, "男"),
     *     FEMALE(2, "女");
     */
    String gender;

}
