package com.yiwise.dialogflow.entity.vo.analyze;

import com.yiwise.dialogflow.entity.enums.AnalyzeTemplateModelTypeEnum;
import com.yiwise.dialogflow.entity.po.analyze.AnalyzeTaskPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AnalyzeTaskVO extends AnalyzeTaskPO {

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板类型
     */
    private AnalyzeTemplateModelTypeEnum templateModelType;

    /**
     * 状态名称
     */
    private String statusName;
}
