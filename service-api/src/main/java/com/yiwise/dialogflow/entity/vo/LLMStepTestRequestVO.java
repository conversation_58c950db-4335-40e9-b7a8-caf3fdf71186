package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.engine.share.model.SimpleChatHistory;
import lombok.Data;

import java.util.List;

@Data
public class LLMStepTestRequestVO {

    /**
     * botId
     */
    Long botId;

    /**
     * 用户输入
     */
    String userInput;

    /**
     * stepId
     */
    String stepId;

    /**
     * 对话历史
     */
    List<SimpleChatHistory> historyList;

    /**
     * 算法需要的 trackInfo 信息, 需要将上一次接口响应的 trackInfo 传过来就可以了
     */
    String trackInfo;
}
