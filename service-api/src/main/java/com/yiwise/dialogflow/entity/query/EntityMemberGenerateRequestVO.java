package com.yiwise.dialogflow.entity.query;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EntityMemberGenerateRequestVO implements Serializable {

    /**
     * 成员名列表
     */
    @NotEmpty(message = "memberNameList不能为空")
    List<String> memberNameList;

    /**
     * 数量
     */
    @NotNull(message = "num不能为空")
    @Min(value = 1, message = "num不能小于1")
    Integer num;
}