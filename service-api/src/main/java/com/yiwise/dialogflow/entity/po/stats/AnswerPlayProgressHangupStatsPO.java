package com.yiwise.dialogflow.entity.po.stats;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class AnswerPlayProgressHangupStatsPO extends BaseStatsPO {
    String answerTemplate;

    public String getAnswerTemplate() {
        return StringUtils.trimToEmpty(answerTemplate);
    }
    int _0;
    int _1;
    int _2;
    int _3;
    int _4;
    int _5;
    int _6;
    int _7;
    int _8;
    int _9;
    int _10;
    int _11;
    int _12;
    int _13;
    int _14;
    int _15;
    int _16;
    int _17;
    int _18;
    int _19;
    int _20;
    int _21;
    int _22;
    int _23;
    int _24;
    int _25;
    int _26;
    int _27;
    int _28;
    int _29;
    int _30;
    int _31;
    int _32;
    int _33;
    int _34;
    int _35;
    int _36;
    int _37;
    int _38;
    int _39;
    int _40;
    int _41;
    int _42;
    int _43;
    int _44;
    int _45;
    int _46;
    int _47;
    int _48;
    int _49;
    int _50;
    int _51;
    int _52;
    int _53;
    int _54;
    int _55;
    int _56;
    int _57;
    int _58;
    int _59;
    int _60;
    int _61;
    int _62;
    int _63;
    int _64;
    int _65;
    int _66;
    int _67;
    int _68;
    int _69;
    int _70;
    int _71;
    int _72;
    int _73;
    int _74;
    int _75;
    int _76;
    int _77;
    int _78;
    int _79;
    int _80;
    int _81;
    int _82;
    int _83;
    int _84;
    int _85;
    int _86;
    int _87;
    int _88;
    int _89;
    int _90;
    int _91;
    int _92;
    int _93;
    int _94;
    int _95;
    int _96;
    int _97;
    int _98;
    int _99;
    int _100;
}
