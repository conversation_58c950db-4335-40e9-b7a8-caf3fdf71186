package com.yiwise.dialogflow.entity.vo.stats;

import lombok.Data;

/**
 * 其实就是对特殊语境和问答知识包了一层
 */
@Data
public class IntentTriggerStatsVO {

    /**
     * id
     */
    private String id;

    /**
     * 标签
     */
    private String label;

    /**
     * 标题名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 命中数
     */
    private CommonPercentVO reach = new CommonPercentVO();

    /**
     * 命中通话数
     */
    private CommonPercentVO reachCall = new CommonPercentVO();

    /**
     * 拒绝
     */
    private CommonPercentVO decline = new CommonPercentVO();

    /**
     * 客户挂机
     */
    private CommonPercentVO customerHangup = new CommonPercentVO();

    /**
     * 命中占比
     */
    private CommonPercentVO hitRatio = new CommonPercentVO();

    public int getReachCount() {
        return reach.getCount();
    }

    public double getReachPercent() {
        return reach.getPercent();
    }

    public String getReachDisplayPercent() {
        return reach.getDisplayPercent();
    }

    public int getReachCallCount() {
        return reachCall.getCount();
    }

    public double getReachCallPercent() {
        return reachCall.getPercent();
    }

    public String getReachCallDisplayPercent() {
        return reachCall.getDisplayPercent();
    }

    public int getCustomerHangupCount() {
        return customerHangup.getCount();
    }

    public double getCustomerHangupPercent() {
        return customerHangup.getPercent();
    }

    public String getCustomerHangupDisplayPercent() {
        return customerHangup.getDisplayPercent();
    }

    public int getDeclineCount() {
        return decline.getCount();
    }

    public double getDeclinePercent() {
        return decline.getPercent();
    }

    public String getDeclineDisplayPercent() {
        return decline.getDisplayPercent();
    }

    public int getHitRatioCount() {
        return hitRatio.getCount();
    }

    public double getHitRatioPercent() {
        return hitRatio.getPercent();
    }

    public String getHitRatioDisplayPercent() {
        return hitRatio.getDisplayPercent();
    }


}
