package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.entity.enums.ActionCategoryEnum;
import com.yiwise.dialogflow.entity.enums.KnowledgeCategoryEnum;
import com.yiwise.dialogflow.entity.enums.PostActionTypeEnum;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KnowledgeQueryVO extends BaseStatsQuery {
    String search;

    KnowledgeCategoryEnum category;

    List<PostActionTypeEnum> filterPostActionList;

    String groupId;

    /**
     * 意向分类code列表
     */
    List<Integer> intentLevelDetailCodeList;

    /**
     * 动作类型列表
     */
    List<ActionCategoryEnum> actionTypeList;

    /**
     * 知识id集合
     */
    List<String> knowledgeList;

    /**
     * 关注点
     */
    Boolean isCustomerConcern;

    public Long getSkip() {
        if (Objects.nonNull(getPageNum()) && Objects.nonNull(getPageSize())) {
            return ((long) (getPageNum() - 1) * getPageSize());
        }
        return null;
    }
}
