package com.yiwise.dialogflow.entity.vo.audio.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/15
 */
@Data
public class PublicAudioSearchRequestVO extends AbstractQueryVO implements Serializable {

    /**
     * 名称、内容、音频描述模糊查询
     */
    private String search;

    /**
     * 录音师id列表
     */
    private List<Long> recordUserIdList;

    /**
     * 来源botId列表
     */
    private List<Long> sourceBotIdList;

    /**
     * 更新人id列表
     */
    private List<Long> updateUserIdList;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime endTime;

    /**
     * id列表
     */
    private List<String> idList;

    /**
     * 分组id
     */
    private String groupId;

    /**
     * 是否展示自己音频
     */
    private Boolean showSubLevel;

    /**
     * 取消勾选的id列表
     */
    private List<String> excludeIdList;
}