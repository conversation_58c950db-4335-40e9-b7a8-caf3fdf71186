package com.yiwise.dialogflow.entity.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OneTimeTtsRequestVO {
    @NotBlank(message = "text不能为空")
    String text;

    @NotNull(message = "合成语速不能为空")
    Float ttsSpeed;
    @NotNull(message = "合成音量不能为空")
    Float ttsVolume;
    @NotBlank(message = "合成音色不能为空")
    String ttsVoice;
}
