package com.yiwise.dialogflow.entity.bo.feishu;

import com.google.common.collect.Lists;
import com.yiwise.base.common.utils.PropertyLoaderUtils;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022/08/17
 */
@Data
@Slf4j
@FieldDefaults(level = AccessLevel.PROTECTED)
public abstract class AbstractAlertConfig implements AlertConfig {

	/**
	 * 告警名称
	 */
	String name;

	/**
	 * 主题，决定是否同一个群聊
	 */
	String topic;

	/**
	 * 前缀，决定是否使用同一个bean、告警名称及对接人
	 */
	String prefix;

	/**
	 * 统计的时间长度, 单位分钟
	 */
	Integer statsMinute = 10;
	/**
	 * 请求错误数阈值
	 */
	Integer errorCountThreshold = 6;
	/**
	 * 开始计算请求错误率阈值的最低次数
	 */
	Integer errorRateBaseCount = 20;
	/**
	 * 请求错误率阈值
	 */
	Double errorRateThreshold = 0.6d;
	/**
	 * 请求超时时间, 单位毫秒
	 */
	Integer timeoutMilliSecondThreshold = 5000;
	/**
	 * 请求超时数阈值
	 */
	Integer timeoutCountThreshold = 10;

	/**
	 * 预警接收人手机号列表
	 */
	List<String> phoneList = Lists.newArrayList();

	@Override
	public boolean reload(Set<String> changedKeys) {
		boolean changed = false;
		try {
			changedKeys.forEach(key -> {
				String[] split = key.split("\\.", 2);
				updateField(split[1], PropertyLoaderUtils.getProperty(key));
			});
		} catch (Exception e) {
			log.error("加载技术预警加微配置出错", e);
		}
		return changed;
	}

	public void updateField(String k, String v) {
		Field field = ReflectionUtils.findField(AbstractAlertConfig.class, k);
		if (Objects.isNull(field)) {
			return;
		}
		ReflectionUtils.makeAccessible(field);
		if (field.getType().isAssignableFrom(Double.class)) {
			ReflectionUtils.setField(field, this, Double.valueOf(String.valueOf(v)));
		} else if (field.getType().isAssignableFrom(Integer.class)) {
			ReflectionUtils.setField(field, this, Integer.valueOf(String.valueOf(v)));
		} else if (field.getType().isAssignableFrom(List.class)) {
			ReflectionUtils.setField(field, this, Arrays.stream(v.split(",")).map(String::trim).collect(Collectors.toList()));
		} else if (field.getType().isAssignableFrom(Set.class)) {
			ReflectionUtils.setField(field, this, Arrays.stream(v.split(",")).map(String::trim).collect(Collectors.toSet()));
		} else {
			ReflectionUtils.setField(field, this, v);
		}
	}
}

