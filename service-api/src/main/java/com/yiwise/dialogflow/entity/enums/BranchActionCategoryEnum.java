package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum BranchActionCategoryEnum implements CodeDescEnum {

    /**
     * 清空
     */
    CLEAR(1, "清空"),

    /**
     * 赋值
     */
    ASSIGN(2, "赋值"),
    ;

    private final Integer code;
    private final String desc;

    public static boolean isClear(BranchActionCategoryEnum category) {
        return CLEAR.equals(category);
    }

    public static boolean isAssign(BranchActionCategoryEnum category) {
        return ASSIGN.equals(category);
    }
}
