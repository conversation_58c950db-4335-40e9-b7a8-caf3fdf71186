package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum NodeJumpTargetTypeEnum implements CodeDescEnum {
    NODE(0, "跳转到下个节点"),
    KNOWLEDGE(1, "跳转到问答知识"),
    STEP(2, "跳转到新的流程了"),
    SPECIAL_ANSWER(3, "跳转到特殊语境")
    ;
    final Integer code;
    final String desc;
    NodeJumpTargetTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
