package com.yiwise.dialogflow.entity.bo.asr;

/**
 * <AUTHOR>
 * @date 2022/11/2 11:18:50
 */

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class AsrLmData {
    private static final Logger logger = LoggerFactory.getLogger(AsrLmData.class);

    @Data
    public static class LmData {
        public String Name;
        public String Status;
        public String Description;
        public String Url;
        public String CreateTime;
        public String UpdateTime;
        public String Id;
        public String ErrorMessage;   // 只有Status为错误状态时有该信息。
        public int Size;
    }

    @Data
    public static class LmDataPage {
        public int PageNumber;
        public int PageSize;
        public int TotalItems;
        public int TotalPages;
        public List<LmData> Content = new ArrayList<LmData>();
    }

    private static final String VERSION = "2018-11-20";
    private static final String DOMAIN = "nls-slp.cn-shanghai.aliyuncs.com";
    private static ProtocolType PROTOCOL_TYPE = ProtocolType.HTTPS;
    private static final String KEY_NAME = "Name";
    private static final String KEY_URL = "Url";
    private static final String KEY_DESCRIPTION = "Description";
    private static final String KEY_DATA_ID = "DataId";
    private static final String KEY_DATA = "Data";
    private static final String KEY_PAGE = "Page";
    private static final String KEY_PAGE_NUMBER = "PageNumber";
    private static final String KEY_PAGE_SIZE = "PageSize";
    private static final String KEY_MODEL_ID = "ModelId";
    private IAcsClient client;

    private CommonRequest newRequest(String action) {
        CommonRequest request = new CommonRequest();
        request.setDomain(DOMAIN);
        request.setProtocol(PROTOCOL_TYPE);
        request.setVersion(VERSION);
        request.setMethod(MethodType.POST);
        request.setAction(action);
        return request;
    }

    public AsrLmData(IAcsClient client) {
        this.client = client;
    }

    /**
     * 创建数据集
     *
     * @param name        创建的数据集名称，必填。
     * @param fileUrl     数据集文件的链接，需要服务端可下载，必填。
     * @param description 数据集描述信息，可选。
     * @return String 创建的数据集ID。
     */
    public String createAsrLmData(String name, String fileUrl, String description) {
        CommonRequest request = newRequest("CreateAsrLmData");
        request.putBodyParameter(KEY_NAME, name);
        request.putBodyParameter(KEY_URL, fileUrl);
        request.putBodyParameter(KEY_DESCRIPTION, description);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.error("【阿里】创建数据集错误：{}", e);
        }
        if (response == null || response.getHttpStatus() != 200) {
            logger.error("【阿里】创建数据集失败，返回结果：{}", response.getData());
            return null;
        }
        JSONObject result = JSONObject.parseObject(response.getData());
        String dataId = result.getString(KEY_DATA_ID);
        logger.debug("【阿里】创建数据集成功，name：{}", name);
        return dataId;
    }

    /**
     * 查询数据集
     *
     * @param dataId 数据集ID
     * @return LmData 获取的数据集对象
     */
    public LmData getAsrLmData(String dataId) {
        CommonRequest request = newRequest("GetAsrLmData");
        request.putBodyParameter(KEY_DATA_ID, dataId);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.error("【阿里】查询数据集错误：{}", e);
        }
        if (response == null || response.getHttpStatus() != 200) {
            logger.error("【阿里】查询数据集失败，返回结果：{}", response.getData());
            return null;
        }
        JSONObject result = JSONObject.parseObject(response.getData());
        String dataJson = result.getString(KEY_DATA);
        LmData data = JSONObject.parseObject(dataJson, LmData.class);
        return data;
    }

    /**
     * 删除数据集
     *
     * @param dataId 需要删除的数据集ID，必填。
     * @return 是否删除成功。
     */
    public boolean deleteAsrLmData(String dataId) {
        CommonRequest request = newRequest("DeleteAsrLmData");
        request.putBodyParameter(KEY_DATA_ID, dataId);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.error("【阿里】删除数据集错误：{}", e);
        }
        if (response == null || response.getHttpStatus() != 200) {
            logger.error("【阿里】删除数据集失败，返回结果：{}", response.getData());
            return false;
        }
        logger.debug("【阿里】删除数据集成功，dataId:{}", dataId);
        return true;
    }

    /**
     * 列举数据集
     *
     * @param pageNumber 页号，从1开始编号，可选，默认值是1。
     * @param pageSize   页大小，从10到100，可选，默认值是10。
     * @param modelId    模型ID，用于搜索被指定模型使用到的数据集，可选，默认列出所有数据集。
     * @return 数据集信息。
     */
    public LmDataPage listAsrLmData(int pageNumber, int pageSize, String modelId) {
        CommonRequest request = newRequest("ListAsrLmData");
        request.putBodyParameter(KEY_PAGE_NUMBER, pageNumber);
        request.putBodyParameter(KEY_PAGE_SIZE, pageSize);
        request.putBodyParameter(KEY_MODEL_ID, modelId);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.error("【阿里】列举数据集错误：{}", e);
        }
        if (response == null || response.getHttpStatus() != 200) {
            logger.error("【阿里】列举数据集失败，返回结果：{}", response.getData());
            return null;
        }
        JSONObject result = JSONObject.parseObject(response.getData());
        String pageJson = result.getString(KEY_PAGE);
        LmDataPage page = JSONObject.parseObject(pageJson, LmDataPage.class);
        return page;
    }

    public LmDataPage listAsrLmData(Integer pageNumber, Integer pageSize) {
        return listAsrLmData(pageNumber, pageSize, null);
    }
}