package com.yiwise.dialogflow.entity.bo.algorithm;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/13
 */
@Data
public class PredictResultWrapper implements Serializable {

    private static final long serialVersionUID = -2717948091408340380L;

    /**
     * 算法或正则的单侧预测结果，已过滤
     */
    PredictResult predictResult;

    /**
     * 未经过滤的备选预测结果列表，已排序
     */
    List<PredictResult> intentCandidateList = new ArrayList<>();

    /**
     * 算法端的请求ID
     */
    String requestId;
}
