package com.yiwise.dialogflow.entity.vo.sync;

import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.entity.enums.SyncScopeEnum;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/9/1
 */
@Data
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BotSyncResultVO implements Serializable {
    /**
     * 同步成功bot个数
     */
    Integer successNum;

    /**
     * 同步失败bot个数
     */
    Integer failNum;

    /**
     * 同步失败bot名称集合
     */
    List<String> failBotNameList;

    /**
     * 同步失败的botId集合
     */
    List<Long> failBotIdList;

    public static BotSyncResultVO defaultInstance() {
        return BotSyncResultVO.builder().successNum(0).failNum(0).failBotNameList(new ArrayList<>()).build();
    }

}
