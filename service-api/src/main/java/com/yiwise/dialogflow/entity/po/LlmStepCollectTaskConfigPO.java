package com.yiwise.dialogflow.entity.po;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LlmStepCollectTaskConfigPO implements LlmStepVariableAssign, Serializable {

    /**
     * 任务描述
     */
    String desc;

    /**
     * 推荐引导话术
     */
    String guideAnswer;

    /**
     * 是否开启动态变量赋值
     */
    Boolean enableAssign;

    /**
     * 变量赋值配置列表
     */
    List<LlmStepVariableAssignConfigPO> variableAssignConfigList;
}