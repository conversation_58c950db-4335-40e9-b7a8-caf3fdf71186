package com.yiwise.dialogflow.entity.vo.step;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
public class StepSortRequestVO {
    @NotNull(message = "机器人id不能为空")
    Long botId;
    @NotEmpty(message = "流程id不能为空")
    List<String> stepIdList;
}
