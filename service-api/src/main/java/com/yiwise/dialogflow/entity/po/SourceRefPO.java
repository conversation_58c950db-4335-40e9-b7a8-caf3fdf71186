package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.entity.enums.IntentRefTypeEnum;
import com.yiwise.dialogflow.entity.enums.SourceTypeEnum;
import com.yiwise.middleware.mysql.handler.EnabledStatusEnumHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/7/20
 * @class <code>SourceRefPO</code>
 * @see
 * @since JDK1.8
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "source_ref")
public class SourceRefPO extends BaseTimeUserIdPO {

    @Id
    @GeneratedValue(generator = "JDBC")
    private Long sourceRefId;

    /**
     * 机器人id
     */
    private Long botId;

    /**
     * 资源类型
     */
    @Column
    @ColumnType(typeHandler = EnabledStatusEnumHandler.class)
    private SourceTypeEnum sourceType;

    /**
     * 资源id
     */
    private String sourceId;

    /**
     * 上级关联id
     */
    private String parentRefId;

    /**
     * 上级关联类型
     */
    @Column
    private IntentRefTypeEnum parentRefType;

    /**
     * 关联id
     */
    private String refId;

    /**
     * 关联类型
     */
    @Column
    private IntentRefTypeEnum refType;

    /**
     * 上级标签
     */
    private String parentRefLabel;

    /**
     * 关联标签
     */
    private String refLabel;
}
