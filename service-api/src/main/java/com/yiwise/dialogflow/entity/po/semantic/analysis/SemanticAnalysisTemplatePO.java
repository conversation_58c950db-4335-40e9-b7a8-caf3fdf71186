package com.yiwise.dialogflow.entity.po.semantic.analysis;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/11/28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Document(collection = SemanticAnalysisTemplatePO.COLLECTION_NAME)
public class SemanticAnalysisTemplatePO extends BaseTimeUserIdPO implements Serializable {

    public static final String COLLECTION_NAME = "semanticAnalysisTemplate";

    @Id
    String templateId;

    String templateName;

    List<SemanticAnalysisIntentPO> intentList;

    List<SemanticAnalysisConditionPO> conditionList;

    Set<Long> callDetailIdSet;

    Boolean exportDetail;

    Integer totalCount;
}
