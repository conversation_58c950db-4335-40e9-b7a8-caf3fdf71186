package com.yiwise.dialogflow.entity.po.analyze;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.entity.enums.AnalyzeTemplateModelTypeEnum;
import com.yiwise.dialogflow.entity.enums.AnalyzeTemplateTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Document(collection = AnalyzeTemplatePO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_name", def = "{name: 1}")
)
public class AnalyzeTemplatePO extends BaseTimeUserIdPO {

    public static final String COLLECTION_NAME = "analyzeTemplate";

    @Id
    private String id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板类型
     */
    private AnalyzeTemplateTypeEnum type;

    /**
     * 模板类型
     */
    private AnalyzeTemplateModelTypeEnum modelType;

    /**
     * 领域
     */
    private String domain;

    /**
     * 是否为内置模板
     */
    private Boolean isBuiltin;

    /**
     * 描述
     */
    private String desc;

    public AnalyzeTemplateModelTypeEnum getModelType() {
        return Objects.isNull(modelType) ? AnalyzeTemplateModelTypeEnum.REJECT_REASON : modelType;
    }
}
