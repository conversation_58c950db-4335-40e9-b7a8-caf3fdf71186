package com.yiwise.dialogflow.entity.vo.analyze;

import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import com.yiwise.dialogflow.entity.enums.AnalyzeTemplateIntentCategoryEnum;
import com.yiwise.dialogflow.entity.enums.AnalyzeTemplateIntentTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyzeTemplateIntentQueryVO extends AbstractQueryVO implements Serializable {

    /**
     * 模板id
     */
    @NotBlank(message = "templateId不能为空")
    private String templateId;

    /**
     * 意图名称模糊查询
     */
    private String search;

    /**
     * 意图类型
     */
    private AnalyzeTemplateIntentTypeEnum type;

    /**
     * 意图分类
     */
    private AnalyzeTemplateIntentCategoryEnum category;

    /**
     * 是否存在语料
     */
    private Boolean corpusSizeGtZero;

    /**
     * id列表
     */
    private List<String> idList;

    /**
     * 是否为新意图
     */
    private Boolean isNew;

    @Max(value = 500, message = "分页最大数量是500")
    private Integer pageSize = 20;
}
