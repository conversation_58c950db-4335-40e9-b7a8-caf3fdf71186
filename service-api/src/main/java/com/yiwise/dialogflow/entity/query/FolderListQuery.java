package com.yiwise.dialogflow.entity.query;

import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.batch.model.vo.AbstractQueryVO;
import lombok.Data;

import java.util.List;

@Data
public class FolderListQuery extends AbstractQueryVO {

    /**
     * 系统类别
     */
    SystemEnum systemType;
    /**
     * 搜索名称
     */
    String name;
    /**
     * 父文件夹id
     */
    Long parentFolderId;

    /**
     * 创建者id列表, 如果为空则表示所有创建者
     */
    List<Long> createUserIdList;

    /**
     * 话术类型
     */
    Integer botType;
}
