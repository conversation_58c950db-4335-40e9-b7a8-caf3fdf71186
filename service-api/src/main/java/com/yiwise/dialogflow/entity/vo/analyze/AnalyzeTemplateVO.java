package com.yiwise.dialogflow.entity.vo.analyze;

import com.yiwise.dialogflow.entity.po.analyze.AnalyzeTemplatePO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyzeTemplateVO extends AnalyzeTemplatePO {

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 领域名称
     */
    private String domainName;
}
