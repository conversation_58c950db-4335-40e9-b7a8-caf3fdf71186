package com.yiwise.dialogflow.entity.vo.botgenerate;

import com.yiwise.dialogflow.entity.enums.RewriteProgressEnum;
import com.yiwise.dialogflow.entity.enums.RewriteStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BotRewriteProgressVO {
    String taskId;

    Long botId;

    Integer totalCount;

    Integer successCount;

    Integer failCount;

    RewriteProgressEnum taskProgress;

    RewriteStatusEnum taskStatus;

    String failReason;
}
