package com.yiwise.dialogflow.entity.bo;

import com.yiwise.dialogflow.entity.enums.EntityTypeEnum;
import com.yiwise.dialogflow.entity.enums.SystemEntityCategoryEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EntityValueBO {

    /**
     * 归一值
     */
    String value;

    /**
     * 原始值
     */
    String originValue;

    /**
     * 输入内容
     */
    String inputText;

    /**
     * 原始值在输入内容中的开始偏移, 从 0 开始, 包含关系
     */
    int startOffset;

    /**
     * 原始值在输入内容中的结束偏移, 从 0 开始, 包含关系
     */
    int endOffset;

    /**
     * 实体名称
     */
    String entityName;

    /**
     * 实体 id, 系统实体使用其枚举值
     */
    String entityId;

    /**
     * 匹配的正则等
     */
    String matchInfo;

    EntityTypeEnum entityType;

    SystemEntityCategoryEnum systemEntityCategory;

}
