package com.yiwise.dialogflow.entity.vo.audio.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AdjustVolumeRequestVO {
    @NotNull(message = "botId不能为空")
    Long botId;
    @NotBlank(message = "url不能为空")
    String url;
    @NotNull(message = "volume不能为空")
    @Range(min = 0, max = 200, message = "音量值不合法, 最大值200, 最小值0")
    Integer volume;
}
