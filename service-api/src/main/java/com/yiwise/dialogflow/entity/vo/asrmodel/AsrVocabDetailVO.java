package com.yiwise.dialogflow.entity.vo.asrmodel;

import com.yiwise.dialogflow.entity.po.asrmodel.AsrVocabDetailPO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/19 09:59:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrVocabDetailVO extends AsrVocabDetailPO {

    /**
     * 调用通话数
     */
    Integer callCount;

    /**
     * 话术id集合
     */
    List<Long> botIdList;

    /**
     * 话术id
     */
    Long botId;

    /**
     * 更新人
     */
    String updateUserName;

    /**
     * 是否绑定话术
     */
    Boolean isBind;

    Long currentUserId;
}