package com.yiwise.dialogflow.entity.vo.audio.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.entity.enums.DialogFlowConditionOperationTypeEnum;
import com.yiwise.dialogflow.entity.enums.RecordingResultsEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Sort;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AnswerAudioRequestVO implements Serializable {

    @NotNull(message = "botId不能为空")
    private Long botId;

    private AnswerSourceEnum answerSource;

    private DialogFlowConditionOperationTypeEnum comparison;

    private Integer volume;

    private String search;

    private List<RecordingResultsEnum> recordingResultsList;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    /**
     * 录音时间排序规则
     */
    private Sort.Direction recordingTimeDirection;
}
