package com.yiwise.dialogflow.entity.bo;

import lombok.Data;

@Data
public class SnapshotValidateConfigBO {

    /**
     * 校验音频
     */
    Boolean requireValidAudio;

    /**
     * 校验特殊动作
     */
    Boolean requireValidActionRule;

    /**
     * 校验特殊动作依赖的外部资源
     */
    Boolean requireValidOutsideResource;

    /**
     * 模型不是正在训练
     */
    Boolean intentModelNotTraining;

    /**
     * 存在意图模型
     */
    Boolean requireExistIntentModel;

    /**
     * 需要绑定asr模型
     */
    Boolean requireBindAsrModel;

    /**
     * 需要所有使用的变量都绑定了客户属性
     */
    Boolean requireUsedVariableBind;
}
