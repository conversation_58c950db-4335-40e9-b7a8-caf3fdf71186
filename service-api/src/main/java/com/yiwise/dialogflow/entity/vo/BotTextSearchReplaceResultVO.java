package com.yiwise.dialogflow.entity.vo;


import com.yiwise.dialogflow.entity.bo.LlmStepTextLocateBO;
import com.yiwise.dialogflow.entity.enums.BotTextSearchTypeEnum;
import com.yiwise.dialogflow.entity.po.intent.RuleActionParam;
import lombok.Data;

import java.util.List;

@Data
public class BotTextSearchReplaceResultVO extends BotAnswerReplaceRequestItem {
    /**
     * 关键词、语料、话术、变量内容
     */
    private String originText;

    /**
     * 拼接的位置信息
     */
    private String location;

    /**
     * 意图id
     */
    private String intentId;

    /**
     * 查找类型
     */
    private BotTextSearchTypeEnum searchType;

    /**
     * 回答后操作
     */
    private String action;

    /**
     * 替换后的内容
     */
    private String replaceResultText;

    private RuleActionParam replaceRuleActionParam;

    private String targetId;

    private String title;

    private String defaultAnswer;

    private Boolean canDelete;

    private LlmStepTextLocateBO llmStepTextLocate;

    private List<RuleActionParam> actionList;
}
