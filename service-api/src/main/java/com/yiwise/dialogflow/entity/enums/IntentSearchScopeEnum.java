package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum IntentSearchScopeEnum implements CodeDescEnum {
    INTENT_NAME(0, "意图名"),
    KEYWORD(1, "关键词"),
    CORPUS(2, "问法预料"),
    DESC(3, "意图描述"),
    ;

    private int code;
    private String desc;

    IntentSearchScopeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return null;
    }

    @Override
    public Integer getCode() {
        return null;
    }
}
