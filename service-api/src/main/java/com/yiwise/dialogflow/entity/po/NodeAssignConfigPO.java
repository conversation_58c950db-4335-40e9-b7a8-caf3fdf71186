package com.yiwise.dialogflow.entity.po;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.enums.VariableTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.*;

@Data
public abstract class NodeAssignConfigPO implements Serializable {

    @JsonIgnore
    public abstract List<String> getDependentEntityIdList();

    @JsonIgnore
    public abstract List<String> getDependIntentIdList();

    @JsonIgnore
    public abstract List<String> getDependVariableIdList();

    @JsonIgnore
    public Set<String> getDependentEntityIdSet() {
        return new HashSet<>(getDependentEntityIdList());
    }


    public abstract void mappingIntentId(Map<String, String> oldIntentId2newIntentIdMap);

    public abstract void mappingVariableId(Map<String, String> oldId2newIdMap);

    public abstract void mappingEntityId(Map<String, String> oldId2newIdMap);

    protected void validateDependVariable(DependentResourceBO resource, DialogBaseNodePO node) {
        List<String> dependVariableIdList = getDependVariableIdList();
        for (String variableId : dependVariableIdList) {
            if (!resource.getVariableIdNameMap().containsKey(variableId)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点动态变量赋值变量不存在");
            }
            VariableTypeEnum variableType = resource.getVarIdTypeMap().get(variableId);
            if (VariableTypeEnum.isNotDynamicVariable(variableType)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点动态变量赋值变量类型不正确");
            }
        }
    }

    protected void validateDependEntity(DependentResourceBO resource, DialogBaseNodePO node) {
        List<String> dependEntityIdList = getDependentEntityIdList();
        for (String entityId : dependEntityIdList) {
            if (!resource.getEntityId2NameMap().containsKey(entityId)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "节点动态变量赋值实体不存在");
            }
        }
    }

    public void validateConfig(StepPO step, DependentResourceBO resource, DialogBaseNodePO node) {
        validateDependVariable(resource, node);
        validateDependEntity(resource, node);
    }

    public String toDisplayString(DependentResourceBO resource) {
        return "";
    }
}
