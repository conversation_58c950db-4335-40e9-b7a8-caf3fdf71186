package com.yiwise.dialogflow.entity.bo.asr;

/**
 * <AUTHOR>
 * @date 2022/11/2 11:19:20
 */

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.tencentcloudapi.asr.v20190614.models.Model;
import com.yiwise.dialogflow.utils.asr.AliSelfLearningApiUtil;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class AsrLmModel {
    private static final Logger logger = LoggerFactory.getLogger(AsrLmModel.class);

    @Data
    public static class LmModel {
        public String Name;
        public String Status;
        public String Description;
        public String CreateTime;
        public String UpdateTime;
        public String Id;
        public String BaseId;
        public String ErrorMessage;   // 只有Status为错误状态时有该信息。
        public int Size;
    }

    @Data
    public static class LmModelPage {
        public int PageNumber;
        public int PageSize;
        public int TotalItems;
        public int TotalPages;
        public List<LmModel> Content = new ArrayList<LmModel>();
        public Model[] models;
    }

    private static final String VERSION = "2018-11-20";
    private static final String DOMAIN = "nls-slp.cn-shanghai.aliyuncs.com";
    private static ProtocolType PROTOCOL_TYPE = ProtocolType.HTTPS;
    private static final String KEY_NAME = "Name";
    private static final String KEY_BASE_ID = "BaseId";
    private static final String KEY_DESCRIPTION = "Description";
    private static final String KEY_MODEL_ID = "ModelId";
    private static final String KEY_MODEL = "Model";
    private static final String KEY_PAGE = "Page";
    private static final String KEY_PAGE_NUMBER = "PageNumber";
    private static final String KEY_PAGE_SIZE = "PageSize";
    private static final String KEY_DATA_ID = "DataId";
    private IAcsClient client;

    private CommonRequest newRequest(String action) {
        CommonRequest request = new CommonRequest();
        request.setDomain(DOMAIN);
        request.setProtocol(PROTOCOL_TYPE);
        request.setVersion(VERSION);
        request.setMethod(MethodType.POST);
        request.setAction(action);
        return request;
    }

    public AsrLmModel(IAcsClient client) {
        this.client = client;
    }

    /**
     * 创建模型
     *
     * @param name        自学习模型名称，必填。
     * @param baseId      基础模型ID，创建成功后不可修改，必填。
     * @param description 自学习模型描述信息，可选。
     * @return 创建的模型ID。
     */
    public String createAsrLmModel(String name, String baseId, String description) {
        CommonRequest request = newRequest("CreateAsrLmModel");
        request.putBodyParameter(KEY_NAME, name);
        request.putBodyParameter(KEY_BASE_ID, baseId);
        request.putBodyParameter(KEY_DESCRIPTION, description);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.error("【阿里】创建自学习模型错误：{}", e);
        }
        if (response == null || response.getHttpStatus() != 200) {
            logger.debug("【阿里】创建自学习模型失败，返回结果:{}", response.getData());
            return null;
        }
        JSONObject result = JSONObject.parseObject(response.getData());
        String modelId = result.getString(KEY_MODEL_ID);
        return modelId;
    }

    /**
     * 查询自学习模型
     *
     * @param modelId 模型ID
     * @return 获取的模型对象
     */
    public LmModel getAsrLmModel(String modelId) {
        CommonRequest request = newRequest("GetAsrLmModel");
        request.putBodyParameter(KEY_MODEL_ID, modelId);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.error("【阿里】查询自学习模型错误：{}", e);
            e.printStackTrace();
        }
        if (response == null || response.getHttpStatus() != 200) {
            logger.error("【阿里】查询自学习模型是失败，返回结果:{}", response.getData());
            return null;
        }
        JSONObject result = JSONObject.parseObject(response.getData());
        String modelJson = result.getString(KEY_MODEL);
        LmModel model = JSONObject.parseObject(modelJson, LmModel.class);
        return model;
    }

    /**
     * 删除自学习模型
     *
     * @param modelId 需要删除的自学习模型ID
     * @return 是否删除成功
     */
    public boolean deleteAsrLmModel(String modelId) {
        CommonRequest request = newRequest("DeleteAsrLmModel");
        request.putBodyParameter(KEY_MODEL_ID, modelId);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.error("【阿里】删除自学习模型错误：{}", e);
            e.printStackTrace();
        }
        if (response == null || response.getHttpStatus() != 200) {
            logger.error("【阿里】删除自学习模型失败，返回结果：{}", response.getData());
            return false;
        }
        logger.debug("【阿里】删除自学习模型成功，modelId:{}", modelId);
        return true;
    }

    /**
     * 列举自学习模型
     *
     * @param pageNumber 页号，从1开始，可选，默认值是1。
     * @param pageSize   页大小，从1到100，可选，默认值是10。
     * @param dataId     数据集ID，用于搜索使用了指定数据集的模型，可选，默认列出所有自学习模型。
     * @return 自学习模型信息。
     */
    public LmModelPage listAsrLmModel(int pageNumber, int pageSize, String dataId) {
        CommonRequest request = newRequest("ListAsrLmModel");
        request.putBodyParameter(KEY_PAGE_NUMBER, pageNumber);
        request.putBodyParameter(KEY_PAGE_SIZE, pageSize);
        request.putBodyParameter(KEY_DATA_ID, dataId);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.error("【阿里】列举自学习模型错误：{}", e);
        }
        if (response == null || response.getHttpStatus() != 200) {
            logger.error("【阿里】列举自学习模型失败，返回结果：{}", response.getData());
            return null;
        }
        JSONObject result = JSONObject.parseObject(response.getData());
        String pageJson = result.getString(KEY_PAGE);
        LmModelPage page = JSONObject.parseObject(pageJson, LmModelPage.class);
        return page;
    }

    public LmModelPage listAsrLmModel(Integer pageNumber,Integer pageSize) {
        return listAsrLmModel(pageNumber, pageSize, null);
    }

    /**
     * 添加数据集到自学习模型
     *
     * @param dataId  需要添加的数据集ID
     * @param modelId 自学习模型ID
     * @return 是否添加成功
     */
    public boolean addDataToAsrLmModel(String dataId, String modelId) {
        CommonRequest request = newRequest("AddDataToAsrLmModel");
        request.putBodyParameter(KEY_DATA_ID, dataId);
        request.putBodyParameter(KEY_MODEL_ID, modelId);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.error("【阿里】添加数据到自学习模型错误：{}", e);
        }
        if (response == null || response.getHttpStatus() != 200) {
            logger.error("【阿里】添加数据到自学习模型失败，返回结果：{}", response.getData());
            return false;
        }
        logger.debug("【阿里】添加数据集至自学习模型成功，dataId:{},modelId:{}", dataId, modelId);
        return true;
    }

    /**
     * 从自学习模型中删除数据集
     *
     * @param dataId  需要删除的数据集
     * @param modelId 自学习模型ID
     * @return 是否删除成功
     */
    public boolean removeDataFromAsrLmModel(String dataId, String modelId) {
        CommonRequest request = newRequest("RemoveDataFromAsrLmModel");
        request.putBodyParameter(KEY_DATA_ID, dataId);
        request.putBodyParameter(KEY_MODEL_ID, modelId);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.error("【阿里】从自学习模型中删除数据集错误：{}", e);
        }
        if (response == null || response.getHttpStatus() != 200) {
            logger.error("【阿里】从自学习模型中删除数据集失败，返回结果：{}", response.getData());
            return false;
        }
        logger.debug("【阿里】将数据集移除自学习模型成功，dataId：{},modelId:{}", dataId, modelId);
        return true;
    }

    /**
     * 开始训练自学习模型
     *
     * @param modelId 需要开始训练的自学习模型ID
     * @return 是否开始训练成功
     */
    public boolean trainAsrLmModel(String modelId) {
        CommonRequest request = newRequest("TrainAsrLmModel");
        request.putBodyParameter(KEY_MODEL_ID, modelId);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.error("【阿里】训练自学习模型错误：{}", e);
        }
        if (response == null || response.getHttpStatus() != 200) {
            logger.error("【阿里】训练自学习模型失败，返回结果：{}", response.getData());
            return false;
        }
        return true;
    }

    /**
     * 上线自学习模型
     *
     * @param modelId 需要上线的自学习模型ID
     * @return 是否上线成功
     */
    public boolean deployAsrLmModel(String modelId) {
        CommonRequest request = newRequest("DeployAsrLmModel");
        request.putBodyParameter(KEY_MODEL_ID, modelId);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.debug("【阿里】上线自学习模型错误：{}", e);
        }
        if (response == null || response.getHttpStatus() != 200) {
            logger.error("【阿里】上线自学习模型失败，返回结果：{}", response.getData());
            return false;
        }
        return true;
    }

    /**
     * 下线自学习模型
     *
     * @param modelId 需要下线的自学习模型ID
     * @return 是否下线成功
     */
    public boolean undeployAsrLmModel(String modelId) {
        CommonRequest request = newRequest("UndeployAsrLmModel");
        request.putBodyParameter(KEY_MODEL_ID, modelId);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.error("【阿里】下线自学习模型错误：{}", e);
        }
        if (response == null || response.getHttpStatus() != 200) {
            logger.error("【阿里】下线自学习模型失败，返回结果：{}", response.getData());
            return false;
        }
        return true;
    }
}