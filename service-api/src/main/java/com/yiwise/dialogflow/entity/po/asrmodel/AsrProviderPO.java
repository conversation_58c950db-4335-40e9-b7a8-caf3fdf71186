package com.yiwise.dialogflow.entity.po.asrmodel;


import com.yiwise.base.model.bean.po.BaseTimePO;
import com.yiwise.dialogflow.engine.share.enums.AsrProviderEnum;
import com.yiwise.middleware.mysql.handler.MapToJsonTypeHandler;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Map;

/**
 * asr基础模型
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "asr_provider")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrProviderPO extends BaseTimePO implements Serializable {

	@Id
	@GeneratedValue(generator = "JDBC")
	Long asrProviderId;

	/**
	 * 供应商
	 */
	@Column
	AsrProviderEnum provider;

	/**
	 * 供应商名称
	 */
	String providerName;

	/**
	 * 语言类型
	 */
	String language;

	/**
	 * 模型id
	 */
	String modelId;

	/**
	 * 基础模型名称
	 */
	String name;

	/**
	 * 模型描述
	 */
	String description;

	/**
	 * ASR参数
	 */
	@ColumnType(typeHandler = MapToJsonTypeHandler.class)
	Map<String, String> params;

}
