package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Document(PublicAudioPO.COLLECTION_NAME)
public class PublicAudioPO extends BaseTimeUserIdPO implements Serializable {

    public static final String COLLECTION_NAME = "publicAudio";

    @Id
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 录音师id
     */
    private Long recordUserId;

    /**
     * 录音师名称
     */
    private String recordUserName;

    /**
     * 音频来源botId
     */
    private Long botId;

    /**
     * bot名称
     */
    private String botName;

    /**
     * 话术内容
     */
    private String text;

    /**
     * 话术内容正则
     */
    private String regex;

    /**
     * 音频url
     */
    private String url;

    /**
     * 音量
     */
    private Integer volume;

    /**
     * 音频时长
     */
    private Integer duration;

    /**
     * 分组id
     */
    private String groupId;

    /**
     * 更新人名称
     */
    private String updateUserName;
}