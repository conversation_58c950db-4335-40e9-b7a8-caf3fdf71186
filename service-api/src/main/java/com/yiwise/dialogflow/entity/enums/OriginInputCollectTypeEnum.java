package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 */
public enum OriginInputCollectTypeEnum implements CodeDescEnum {

    ALL(1, "采集全部信息"),
    FILTERED(2, "采集过滤后信息");

    private final int code;

    private final String desc;

    OriginInputCollectTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    public static boolean isFiltered(OriginInputCollectTypeEnum type) {
        return FILTERED.equals(type);
    }
}
