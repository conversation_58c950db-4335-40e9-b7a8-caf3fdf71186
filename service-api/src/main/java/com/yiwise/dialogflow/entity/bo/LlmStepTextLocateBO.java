package com.yiwise.dialogflow.entity.bo;

import com.yiwise.dialogflow.entity.enums.LlmStepTextLocateEnum;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LlmStepTextLocateBO implements Serializable {

    Long botId;

    String stepId;

    String stepName;

    String stepLabel;

    LlmStepTextLocateEnum locate;

    Integer taskConfigIndex;

    Integer assignConfigIndex;

    public String toDisplayString() {
        switch (locate) {
            case PROMPT:
                return String.format("%s:提示词", stepLabel);
            case BACKGROUND:
                return String.format("%s:背景信息", stepLabel);
            case ROLE_DESC:
                return String.format("%s:角色描述", stepLabel);
            case TASK_DESC:
                return String.format("%s:任务配置-任务%s-任务描述", stepLabel, taskConfigIndex);
            case GUIDE_ANSWER:
                return String.format("%s:任务配置-任务%s-推荐引导话术", stepLabel, taskConfigIndex);
            case COLLECT_TASK_VARIABLE_DESC:
                return String.format("%s:任务配置-任务%s-变量赋值%s-变量描述", stepLabel, taskConfigIndex, assignConfigIndex);
            case FREE_CONFIG_VARIABLE_DESC:
                return String.format("%s:自由配置-变量赋值%s-变量描述", stepLabel, assignConfigIndex);
            default:
                return "";
        }
    }
}