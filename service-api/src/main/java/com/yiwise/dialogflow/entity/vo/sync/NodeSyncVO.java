package com.yiwise.dialogflow.entity.vo.sync;

import com.yiwise.dialogflow.entity.enums.ActionCategoryEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.BooleanUtils;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class NodeSyncVO extends BasicSyncVO implements Serializable {

    /**
     * 同步不允许用户打断
     */
    Boolean syncUninterrupted;

    /**
     * 同步不关联
     */
    Boolean syncMismatch;

    /**
     * 同步客户无应答
     */
    Boolean syncCustomUserSilence;

    /**
     * 同步AI应答时长
     */
    Boolean syncWaitUserSayFinish;

    /**
     * 同步意向分类
     */
    Boolean syncIntentLevel;

    /**
     * 同步返回时执行跳转原主动流程逻辑
     */
    Boolean syncPullback;

    /**
     * 同步返回时语音播报设置
     */
    Boolean syncCustomReplay;

    /**
     * 同步触发动作
     */
    Boolean syncAction;

    /**
     * 同步动作类型列表
     */
    List<ActionCategoryEnum> syncActionCategoryList;

    /**
     * 同步动态变量赋值
     */
    Boolean syncAssign;

    /**
     * 同步跳转至
     */
    Boolean syncJump;

    /**
     * 是否同步答案
     */
    Boolean syncAnswer;

    /**
     * 是否同步音频
     */
    Boolean syncAudio;

    /**
     * 是否同步节点名称
     */
    Boolean syncNodeName;

    public boolean mapStep() {
        return BooleanUtils.isTrue(getSyncUninterrupted()) || BooleanUtils.isTrue(getSyncMismatch());
    }

    public boolean mapKnowledge() {
        return BooleanUtils.isTrue(getSyncUninterrupted()) || BooleanUtils.isTrue(getSyncMismatch());
    }

    public boolean mapSpecialAnswerConfig() {
        return BooleanUtils.isTrue(getSyncUninterrupted()) || BooleanUtils.isTrue(getSyncMismatch());
    }

    public boolean mapIntent() {
        return BooleanUtils.isTrue(getSyncUninterrupted()) || BooleanUtils.isTrue(getSyncWaitUserSayFinish()) || BooleanUtils.isTrue(getSyncAssign());
    }

    public boolean mapVariable() {
        return BooleanUtils.isTrue(getSyncAssign()) || BooleanUtils.isTrue(getSyncAnswer());
    }

    public boolean mapEntity() {
        return BooleanUtils.isTrue(getSyncAssign());
    }
}