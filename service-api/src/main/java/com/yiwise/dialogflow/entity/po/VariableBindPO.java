package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.persistence.Id;

import javax.persistence.Table;

/**
 * 话术的变量和客户属性进行绑定
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "variable_bind")
public class VariableBindPO extends BaseTimeUserIdPO {
    /**
     * 主键id
     */
    @Id
    private Long variableBindId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * bot id
     */
    private Long botId;

    /**
     * 变量id
     */
    private String variableId;

    /**
     * 客户属性id
     */
    private Long customerAttributeId;

    /**
     * 绑定时的变量名称, 用于判断变量是否重命名了
     */
    private String bindVariableName;

    /**
     * 绑定时的客户属性名称, 用于判断客户属性是否重命名了
     */
    private String bindAttributeName;
}
