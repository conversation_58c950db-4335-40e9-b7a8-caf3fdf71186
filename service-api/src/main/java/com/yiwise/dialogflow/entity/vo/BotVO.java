package com.yiwise.dialogflow.entity.vo;

import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.entity.po.BotPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BotVO extends BotPO {

    /**
     * 租户ID
     */
    Long tenantId;

    /**
     * 意向标签组名称
     */
    String intentLevelTagName;

    /**
     * 赛道名称
     */
    String customerTrackTypeName;

    /**
     * 二级赛道名称
     */
    String customerTrackSubTypeName;

    /**
     * 录音师姓名
     */
    String recorderName;

    /**
     * 意图识别算法开关
     */
    Boolean enableAlgorithm;

    /**
     * 对应的话术id
     */
    Long dialogFlowId;

    /*
     * 来源BotId
     * 这个字段有值表示是复制话术
     */
    Long srcBotId;

    /**
     * 操作平台
     */
    SystemEnum systemType;

    /**
     * 创建者
     */
    String createUserName;

    /**
     * 文件夹ID
     */
    Long folderId;

    /**
     * 文件夹路径
     */
    List<IdNamePair<Long, String>> folderPath;

    /**
     * 场景
     */
    private String customerSceneName;

    /**
     * 是否有改写成功记录
     */
    Boolean hasRewriteSuccessRecord;

    /**
     * 算法标签领域
     */
    String algorithmLabelDomain;
}
