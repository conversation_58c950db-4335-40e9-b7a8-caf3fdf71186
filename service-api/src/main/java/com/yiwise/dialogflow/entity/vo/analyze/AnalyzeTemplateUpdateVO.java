package com.yiwise.dialogflow.entity.vo.analyze;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AnalyzeTemplateUpdateVO implements Serializable {

    /**
     * id
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    private String name;
}