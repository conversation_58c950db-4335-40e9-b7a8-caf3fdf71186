package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 * @date 2020/12/2
 */
public enum AlgorithmTrainTypeEnum implements CodeDescEnum {

    DISPOSAL(0, "废弃语料库"),
    DIALOG_FLOW(1, "对话流"),
    INTENT(2, "意图库"),
    KNOWLEDGE(3, "知识库"),
    ENTITY(4, "实体库"),
    SLOT(5, "词槽库"),
    ENTRY_INTENT(6, "入口意图"),
    SUB_INTENT(7, "子意图"),
    GENERAL_INTENT(8, "通用意图"),
    FILL_SLOT(9, "填槽"),
    CHAT_SUBJECT(10, "闲聊"),
    KNOWLEDGE_GRAPH_ATTR(11, "知识图谱-属性"),
    MIXED_MODEL(21, "领域模型自定义语料"),
    QC_TAG_STAFF(101, "质检-客服标签"),
    QC_TAG_CUSTOMER(102, "质检-客户标签"),
    ASR_ERROR_CORRECTION(103, "ASR纠错"),
    ;

    Integer code;
    String desc;
    AlgorithmTrainTypeEnum(Integer code, String desc) {
        this.code =code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
