package com.yiwise.dialogflow.entity.po.semantic.analysis;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/28
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SemanticAnalysisIntentPO implements Serializable {

    String intentId;

    String intentName;

    List<String> keywords;
}
