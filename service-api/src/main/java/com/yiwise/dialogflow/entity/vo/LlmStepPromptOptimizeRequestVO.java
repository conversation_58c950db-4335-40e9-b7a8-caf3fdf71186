package com.yiwise.dialogflow.entity.vo;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LlmStepPromptOptimizeRequestVO implements Serializable {

    @NotBlank(message = "prompt不能为空")
    String prompt;
}
