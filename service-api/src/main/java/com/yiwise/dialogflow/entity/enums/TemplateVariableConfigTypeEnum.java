package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum TemplateVariableConfigTypeEnum implements CodeDescEnum {
    DEFAULT_VALUE(0, "变量默认值"),
    PROMPT(1, "提示词配置")
    ;


    final String desc;
    final Integer code;
    TemplateVariableConfigTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
