package com.yiwise.dialogflow.entity.po.asrmodel;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.entity.enums.asr.AsrErrorCorrectionTrainStatusEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 纠错模型信息
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Document(collection = AsrErrorCorrectionDetailPO.COLLECTION_NAME)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrErrorCorrectionDetailPO extends BaseTimeUserIdPO {
    public static final String COLLECTION_NAME = "asrErrorCorrectionDetail";

    @Id
    String asrErrorCorrectionDetailId;

    /**
     * 模型名称
     */
    String name;

    /**
     * 描述
     */
    String description;

    /**
     * 训练状态（0-未训练；1-训练中；2-已训练）
     */
    AsrErrorCorrectionTrainStatusEnum trainStatus;

    /**
     * 纠错阈值
     */
    @DecimalMin("0.01")
    @DecimalMax("1.00")
    BigDecimal threshold;

    /**
     * 纠错语料
     */
    List<String> corpusList;

    /**
     * 纠错白名单
     */
    List<String> whiteList;

    /**
     * 当前模型ID
     */
    String modelId;

    /**
     * 上次训练时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime lastTrainTime;
}
