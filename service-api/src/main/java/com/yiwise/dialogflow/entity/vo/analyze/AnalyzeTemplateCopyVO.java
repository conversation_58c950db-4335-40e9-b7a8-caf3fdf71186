package com.yiwise.dialogflow.entity.vo.analyze;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AnalyzeTemplateCopyVO implements Serializable {

    /**
     * srcTemplateId
     */
    @NotBlank(message = "srcTemplateId不能为空")
    private String srcTemplateId;

    /**
     * 模板名称
     */
    @NotBlank(message = "name不能为空")
    private String name;
}
