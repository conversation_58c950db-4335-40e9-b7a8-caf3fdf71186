package com.yiwise.dialogflow.entity.po;

import com.yiwise.dialogflow.entity.enums.LargeModelEntityModelTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LargeModelEntityPO extends BaseEntityPO {

    /**
     * 模型类型
     */
    LargeModelEntityModelTypeEnum modelType;

    /**
     * 实体描述
     */
    String desc;

    /**
     * 实体成员列表
     */
    List<EntitySynonymPO> memberList;

    /**
     * 提示词
     */
    String prompt;

    public LargeModelEntityModelTypeEnum getModelType() {
        return modelType == null ? LargeModelEntityModelTypeEnum.COMMON : modelType;
    }
}