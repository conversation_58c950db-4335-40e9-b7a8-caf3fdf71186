package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;
import javaslang.Tuple;
import javaslang.Tuple2;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public enum SystemEntityCategoryEnum implements CodeDescEnum {
    ADDRESS(1,true, "地址", true,"地址街道提取"),
    PHONE(2, true,"手机号",false, "对国内11位手机号的识别和提取，如13812345678."),
    ID_CARD(3, true,"身份证", false,"对身份证号进行提取"),
    PERSON(4, true,"人名", true,"支持对中文姓名的提取"),
    ORGANIZATION(5, true,"组织名", true,"清华大学, 中国银行等组织机构"),
    NUMBER(8, true, "数字",false,  "一、二、三...十，壹、贰、叁...拾，百，千，万，十万，百万，千万，亿，十亿，百亿，千亿"),
    CITY(11, true,"城市", false, "北京、杭州、上海等涵盖国内的所有城市"),
    PROVINCE(12, true,"省份",false,  "浙江、河北、江苏等全国23个省份及5个自治区"),
    COUNTRY(13,true, "国家或地区",false,  "中国、新加坡、印度尼西亚等全世界所有主要的国家和地区"),
    AGE(14, true,"年龄",true, "15岁、30了、五十岁等"),
    CURRENCY(15, true,"货币",false, "元、角、分、厘、美元等"),
    ORIGIN_INPUT(16, false, "原话采集", false, "原话采集"),
    FUTURE_TIME(17, false,"时间", true,"上午九点、中午12点半、14:00等"),
    FUTURE_DATE(18, true,"日期", true,"未来的半结构化时间，如：“2024年4月8日早上8点 - 中午12点”\n“2024年4月13日下午5点”\n“2024年4月6日 - 7日”"),
    FUTURE_DATETIME(19, false,"日期时间", true,"明天中午十二点半、下周三早上八点一刻、2019年5月6日16:07等"),
    ORIGINAL_DATE(20, true,"原始日期", true,"用户的原话里的时间信息，如：“明天早上”、“周六下午五点”、“周末”、“上一周”"),
    PAST_DATE(21, true,"过去日期", true,"过去的半结构化时间，如：“2024年3月25日 - 31日”"),
    PAST_TIME(22, false,"过去时间", true,"上午九点、中午12点半、14:00等"),
    PAST_DATETIME(23, false,"过去日期时间", true,"明天中午十二点半、下周三早上八点一刻、2019年5月6日16:07等"),
    GRADE(24, true, "年级", true, "幼儿园、小班、中班、大班、一年级、九年级、高三等"),
    PROVINCE_ADVANCED(25, true, "省份(高耗时语义版)", true, "提取用户语料中的省份信息，示例：不在河北了，现在在浙江，提取：浙江省；"),
    CITY_ADVANCED(26, true, "城市(高耗时语义版)", true, "提取用户语料中的城市信息，示例：不是重庆，是杭州，提取：杭州市；"),
    COUNTY_ADVANCED(27, true, "区县(高耗时语义版)", true, "提取用户语料中的区县信息，示例：杭州萧山，提取：萧山区；"),
    COUNTY_SUBDETAIL_ADVANCED(28, true, "区县以下地址(高耗时语义版)", true, "提取用户语料中的区县以下地址信息，示例：萧山区北干街道启迪路108号，提取：北干街道启迪路108号；"),
    TOWN_ADVANCED(29, true, "乡镇(高耗时语义版)", true, "提取用户语料中的乡镇信息，示例：浙江省杭州市萧山区楼塔镇洲口路2号，提取：楼塔镇；"),
    TOWN_SUBDETAIL_ADVANCED(30, true, "乡镇以下地址(高耗时语义版)", true, "提取用户语料中的乡镇以下地址信息，示例：浙江省衢州市龙游县小南海镇茶圩里村，提取：小南海镇茶圩里村"),
    FULL_ADDRESS_ADVANCED(31, true, "完整地址(高耗时语义版)", true, "提取用户语料中的完整地址信息并自动补齐缺失信息，按照标准化地址格式输出，示例：我家住杭州萧山宁围通惠北路108号东方名府1-1-203，实体提取为：浙江省杭州市萧山区宁围街道宁围通惠北路108号东方名府1-1-203；"),
    ;

    private Integer code;
    private String desc;
    private String effect;
    private Boolean showOnList;
    private Boolean isAlgorithmSupport;

    SystemEntityCategoryEnum(Integer code, Boolean showOnList, String desc, boolean isAlgorithmSupport, String effect) {
        this.code = code;
        this.desc = desc;
        this.showOnList = showOnList;
        this.effect = effect;
        this.isAlgorithmSupport = isAlgorithmSupport;
    }

    public static List<SystemEntityCategoryEnum> getOfflineCategoryList() {
        return Arrays.stream(SystemEntityCategoryEnum.values()).filter(item -> BooleanUtils.isFalse(item.getShowOnList()))
                .collect(Collectors.toList());
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public Boolean getShowOnList() {
        return showOnList;
    }

    public String getEffect() {
        return effect;
    }

    /**
     * 算法是否支持, 当前支持 时间, 地址, 手机号, 身份证, 人名, 组织名
     */
    public static boolean algorithmSupported(SystemEntityCategoryEnum field) {
        return BooleanUtils.isTrue(field.isAlgorithmSupport);
    }

    public static boolean isLocalization(SystemEntityCategoryEnum field) {
        return (field == CITY || field == PROVINCE || field == COUNTRY);
    }

    public static String convertToId(SystemEntityCategoryEnum type) {
        if (type == null) {
            return null;
        }
        return String.format("%s", type.name());
    }


    public static Optional<Tuple2<SystemEntityCategoryEnum, SystemEntityCategoryEnum>> idConvertToEnum(String id) {
        if (StringUtils.isBlank(id)) {
            return Optional.empty();
        }
        try {
            if (id.contains("#")) {
                String[] enumNames = id.split("#");
                String typeName = enumNames[0];
                String timeType = enumNames[1];
                return Optional.of(Tuple.of(SystemEntityCategoryEnum.valueOf(typeName), SystemEntityCategoryEnum.valueOf(timeType)));
            } else {
                return Optional.of(Tuple.of(SystemEntityCategoryEnum.valueOf(id), null));
            }
        } catch (Exception e) {
            // ignore
        }
        return Optional.empty();
    }
}
