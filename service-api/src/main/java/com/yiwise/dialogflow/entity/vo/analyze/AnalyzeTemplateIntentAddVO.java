package com.yiwise.dialogflow.entity.vo.analyze;

import com.yiwise.dialogflow.entity.enums.AnalyzeTemplateIntentCategoryEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AnalyzeTemplateIntentAddVO implements Serializable {

    /**
     * 模板id
     */
    @NotBlank(message = "templateId不能为空")
    String templateId;

    /**
     * 意图名称
     */
    @NotBlank(message = "name不能为空")
    String name;

    /**
     * 意图分类
     */
    AnalyzeTemplateIntentCategoryEnum category;

    /**
     * 语料列表
     */
    @NotEmpty(message = "corpusList不能为空")
    List<String> corpusList;
}
