package com.yiwise.dialogflow.entity.po.intent;

import com.yiwise.dialogflow.entity.enums.SpecialIntentRuleTypeEnum;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/5/10
 * @class <code>IntentRuleTemplate</code>
 * @see
 * @since JDK1.8
 */
@Data
@Document(collection = "defaultIntentRuleTemplate")
public class IntentRuleTemplate {
    public static final String COLLECTION_NAME = "defaultIntentRuleTemplate";

    /**
     * 默认特殊规则
     */
    private SpecialIntentRuleTypeEnum specialIntentRuleType;

    /**
     * 规则排序
     */
    private Long matchOrder = 0L;

    /**
     * 意向等级标签
     */
    private Integer intentLevelTagDetailCode;


}
