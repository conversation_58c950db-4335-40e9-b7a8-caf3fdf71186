package com.yiwise.dialogflow.entity.dto;

import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OperationLogDTO implements Serializable {

    /**
     * botId
     */
    private Long botId;

    /**
     * 操作日志类型
     */
    private OperationLogTypeEnum type;

    /**
     * 操作对象
     */
    private OperationLogResourceTypeEnum resourceType;

    /**
     * 操作日志详情
     */
    private String detail;

    /**
     * 操作人id
     */
    private Long operatorId;
}
