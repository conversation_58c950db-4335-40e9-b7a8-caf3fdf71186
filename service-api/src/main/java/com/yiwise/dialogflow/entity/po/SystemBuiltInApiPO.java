package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.engine.share.DialogQueryNodeHttpParamInfo;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.http.HttpMethod;

import java.util.List;
import java.util.Map;

/**
 * 系统内置的api配置, 用于查询节点选择
 */
@Data
@Document(collection = SystemBuiltInApiPO.COLLECTION_NAME)
public class SystemBuiltInApiPO {

    public static final String COLLECTION_NAME = "systemBuiltInApi";

    @Id
    String id;

    /**
     * 接口名称
     */
    String name;

    /**
     * 描述
     */
    String description;

    /**
     * url地址
     */
    String url;

    /**
     * 请求方式,GET POST
     */
    HttpMethod httpMethod;

    /**
     * 超时时间，单位秒
     */
    Integer timeout;

    /**
     * query
     */
    List<DialogQueryNodeHttpParamInfo> queryList;

    /**
     * header
     */
    List<DialogQueryNodeHttpParamInfo> headerList;

    /**
     * body
     */
    String body;

    /**
     * <动态变量id,jsonPath表达式>
     */
    Map<String, String> resTemplateMap;

    /**
     * 启用状态
     */
    EnabledStatusEnum enabledStatus;

}
