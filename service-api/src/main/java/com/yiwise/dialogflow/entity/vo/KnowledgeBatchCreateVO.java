package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.entity.enums.KnowledgeCategoryEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/29
 */
@Data
public class KnowledgeBatchCreateVO implements Serializable {

    /**
     * botId
     */
    @NotNull(message = "botId不能为空")
    private Long botId;

    /**
     * 知识类型
     */
    @NotNull(message = "知识类型不能为空")
    private KnowledgeCategoryEnum category;

    /**
     * 分组id
     */
    @NotBlank(message = "分组id不能为空")
    private String groupId;

    /**
     * 触发意图列表
     */
    @NotEmpty(message = "意图id列表不能为空")
    private List<String> triggerIntentIdList;
}