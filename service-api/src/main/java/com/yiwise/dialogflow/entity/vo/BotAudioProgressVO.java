package com.yiwise.dialogflow.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.dialogflow.entity.enums.AuditStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 机器人录音进度, 小程序使用
 * <AUTHOR>
 */
@Data
public class BotAudioProgressVO {
    Long botId;

    String name;

    Long recordUserId;

    Integer percent;

    AuditStatusEnum audioStatus;

    /**
     * 总数
     */
    Integer totalCount;

    /**
     * 去重后总数
     */
    Integer distTotalCount;

    /**
     * 已完成
     */
    Integer completedCount;

    /**
     * 去重后已完成
     */
    Integer distCompletedCount;
    /**
     * 未完成
     */
    Integer incompleteCount;

    /**
     * 去重后未完成
     */
    Integer distIncompleteCount;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime updateTime;

}
