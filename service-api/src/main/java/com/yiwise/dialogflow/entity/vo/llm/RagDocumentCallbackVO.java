package com.yiwise.dialogflow.entity.vo.llm;

import com.yiwise.dialogflow.entity.po.llm.RagDocumentSegmentPO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RagDocumentCallbackVO implements Serializable {

    String fileId;

    List<RagDocumentSegmentPO> segments;

    Integer code;

    String info;

    public boolean isSuccess() {
        return Objects.equals(code, 0);
    }
}