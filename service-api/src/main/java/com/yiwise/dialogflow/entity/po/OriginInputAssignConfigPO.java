package com.yiwise.dialogflow.entity.po;

import com.alibaba.excel.util.StringUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.enums.OriginInputCollectTypeEnum;
import com.yiwise.dialogflow.entity.enums.SystemEntityCategoryEnum;
import com.yiwise.dialogflow.utils.ParseUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Data
public class OriginInputAssignConfigPO extends NodeAssignConfigPO implements Serializable {

    List<OriginInputAssignConfigItem> assignActionList;

    @Override
    public List<String> getDependentEntityIdList() {
        if (CollectionUtils.isEmpty(assignActionList)) {
            return new ArrayList<>();
        }
        List<String> result = new ArrayList<>();
        result.add(SystemEntityCategoryEnum.convertToId(SystemEntityCategoryEnum.ORIGIN_INPUT));
        return result;
    }

    @Override
    public List<String> getDependIntentIdList() {
        if (CollectionUtils.isEmpty(assignActionList)) {
            return new ArrayList<>();
        }

        List<String> intentIdList = new ArrayList<>();

        for (OriginInputAssignConfigItem item : assignActionList) {
            if (OriginInputCollectTypeEnum.isFiltered(item.getOriginInputCollectType()) && CollectionUtils.isNotEmpty(item.getFilteredIntentIdList())) {
                intentIdList.addAll(item.getFilteredIntentIdList());
            }
        }

        return intentIdList;
    }

    @Override
    public List<String> getDependVariableIdList() {
        if (CollectionUtils.isEmpty(assignActionList)) {
            return new ArrayList<>();
        }

        return assignActionList.stream()
                .map(OriginInputAssignConfigItem::getVariableId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    @Override
    public void mappingIntentId(Map<String, String> oldIntentId2newIntentIdMap) {
        if (CollectionUtils.isEmpty(assignActionList)) {
            return;
        }
        assignActionList.forEach(item -> {
            if (CollectionUtils.isNotEmpty(item.getFilteredIntentIdList())) {
                List<String> newIntentIdList = item.getFilteredIntentIdList().stream()
                        .map(oldIntentId2newIntentIdMap::get)
                        .collect(Collectors.toList());
                item.setFilteredIntentIdList(newIntentIdList);
            }
        });
    }

    @Override
    public void mappingVariableId(Map<String, String> oldId2newIdMap) {
        if (CollectionUtils.isEmpty(assignActionList)) {
            return;
        }
        assignActionList.forEach(item -> item.setVariableId(oldId2newIdMap.get(item.getVariableId())));
    }

    @Override
    public void mappingEntityId(Map<String, String> oldId2newIdMap) {

    }


    @Override
    protected void validateDependEntity(DependentResourceBO resource, DialogBaseNodePO node) {

    }

    @Override
    public void validateConfig(StepPO step, DependentResourceBO resource, DialogBaseNodePO node) {
        super.validateConfig(step, resource, node);
        if (CollectionUtils.isEmpty(assignActionList)) {
            return;
        }
        for (OriginInputAssignConfigItem assignConfig : assignActionList) {
            if (OriginInputCollectTypeEnum.isFiltered(assignConfig.getOriginInputCollectType())) {
                if (CollectionUtils.isEmpty(assignConfig.getFilteredIntentIdList()) && CollectionUtils.isEmpty(assignConfig.getFilteredRegexList())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "原话采集过滤意图和过滤关键词必须配置一个");
                }
                if (CollectionUtils.isNotEmpty(assignConfig.getFilteredIntentIdList())) {
                    if (assignConfig.getFilteredIntentIdList().stream().anyMatch(i -> !resource.getIntentIdNameMap().containsKey(i))) {
                        throw new ComException(ComErrorCode.VALIDATE_ERROR, "过滤意图不存在");
                    }
                }
                if (CollectionUtils.isNotEmpty(assignConfig.getFilteredRegexList())) {
                    for (String regex : assignConfig.getFilteredRegexList()) {
                        if (ParseUtil.invalid(regex)) {
                            throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("过滤关键词 %s 格式错误", regex));
                        }
                    }
                }
            }
        }
    }


    @Override
    public String toDisplayString(DependentResourceBO resource) {
        if (CollectionUtils.isEmpty(assignActionList)) {
            return "";
        }

        return this.getAssignActionList().stream()
                .map(item -> {
                    return generateOriginInputDisplayString(resource, item);
                }).collect(Collectors.joining("; "));
    }

    private String generateOriginInputDisplayString(DependentResourceBO resource, OriginInputAssignConfigItem item) {
        String variableName = resource.getVariableIdNameMap().getOrDefault(item.getVariableId(), "未知");

        if (!OriginInputCollectTypeEnum.isFiltered(item.getOriginInputCollectType())) {
            return String.format("【原话采集: 保存到变量:%s, 采集方式:%s】", variableName, item.getOriginInputCollectType().getDesc());
        }

        List<String> filteredIntentNameList;
        if (CollectionUtils.isNotEmpty(item.getFilteredIntentIdList())) {
            filteredIntentNameList = item.getFilteredIntentIdList().stream()
                    .map(resource.getIntentIdNameMap()::get)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
        } else {
            filteredIntentNameList = Collections.emptyList();
        }
        return String.format("【原话采集: 保存到变量:%s, 采集方式:%s, 过滤意图:%s, 过滤关键词:%s】",
                variableName, item.getOriginInputCollectType().getDesc(), filteredIntentNameList, item.getFilteredRegexList());
    }
}
