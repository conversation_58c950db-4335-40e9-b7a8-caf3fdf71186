package com.yiwise.dialogflow.entity.po.magic;

import java.util.List;
import java.util.Map;

import javax.persistence.Id;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;

import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper = false)
public class MagicTemplateVarGenerateRecordPO extends BaseTimeUserIdPO {

    public static final String COLLECTION_NAME = "magicTemplateVarGenerateRecord";

    @Id
    private String id;

    private Long botId;

    /**
     * 模板变量ID列表
     */
    private List<String> variableIdList;

    /**
     * 表单数据
     */
    private Map<String, String> formData;
}
