package com.yiwise.dialogflow.entity.po.remote;


import com.yiwise.base.model.bean.po.BaseTimePO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 通话记录会话详情
 * 
 * <AUTHOR>
 * @date 2022/12/21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "call_detail_session_info")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CallDetailSessionInfoPO extends BaseTimePO implements Serializable {

	/**
	 * 通话详情ID
	 */
	@Id
	Long callDetailId;

	/**
	 * 会话ID
	 */
	String sessionId;

	/**
	 * 序号
	 */
	Integer seq;

	/**
	 * 用户输入
	 */
	String userInput;

}
