package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/7/20
 * @class <code>SourceTypeEnum</code>
 * @see
 * @since JDK1.8
 */
public enum SourceTypeEnum implements CodeDescEnum {
    INTENT(1, "意图"),
    VARIABLE(2, "变量"),
    ENTITY(3, "实体"),
    ;

    private Integer code;
    private String desc;

    SourceTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
