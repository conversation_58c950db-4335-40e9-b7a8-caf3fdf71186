package com.yiwise.dialogflow.entity.vo.stats;

import lombok.Data;

import java.io.Serializable;

@Data
public class IntentRuleStatsVO implements Serializable {
    CommonPercentVO reach = new CommonPercentVO();

    public int getReachCount() {
        return reach.getCount();
    }

    public double getReachPercent() {
        return reach.getPercent();
    }

    public String getReachDisplayPercent() {
        return reach.getDisplayPercent();
    }

    public CommonPercentVO getReachCall() {
        return reach;
    }

    public int getReachCallCount() {
        return reach.getCount();
    }

    public double getReachCallPercent() {
        return reach.getPercent();
    }

    public String getReachCallDisplayPercent() {
        return reach.getDisplayPercent();
    }
}