package com.yiwise.dialogflow.entity.po;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class DialogFlowInfoPO {

    public final static String SORT_ATTRIBUTE = "create_time";

    private static final long serialVersionUID = 6773625169948500277L;

    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;
    private String name;
    private String description;
    private Long createUserId;
    //    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private LocalDateTime createTime;
    private Long updateUserId;
    //    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private LocalDateTime updateTime;
    private Long recordUserId;
    //    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private LocalDateTime lastRecordingTime;
    private Boolean sendHangOffMessage;
    private Long aliyunKnowledgeRepositoryId;
    private Long dialogFlowAsrModelId;
    private Long familyNameTemplateId;
    private Long intentLevelTagId = 0L;
    private Boolean enabledStatus;

    private Long auditUserId;
    //    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private LocalDateTime auditTime;
    private String rejectReason;
    private Boolean published;
    private Long distributorId;
    private Long tenantId;


    /**
     * 灵敏度等级参数, 这里是差值,减去基数1500以后的差值
     */
    private Integer vadGateMute;
    /**
     * 反应灵敏度
     */
    private Integer maxSentenceSilence;

    /**
     * tts 语速参数
     */
    private Float ttsSpeech;

    /**
     * tts 音量
     */
    private Float ttsVolume;


    /**
     * 开启自定义tts配置
     */
    private Boolean enableCustomTtsConfig;
    /**
     * 是否开启通用FQA
     */
    private Integer enableAskService;
    /**
     * 问法介入字数设置，默认值=3
     */
    private Integer askNumber;

    /**
     * 问法阈值上限
     */
    private Double knowledgeUpperThreshold;

    /**
     * 问法阈值下限
     */
    private Double knowledgeLowerThreshold;

    /**
     * nlp机器人id
     */
    private Long nlpRobotId;

    /**
     * 自学习模型ID
     */
    @Deprecated
    private Long selfLearningModelId;


    /**
     * 机器人审核原因
     */
    private String robotRejectReason;


    /**
     * 算法模型种类
     */
    private String modelType;

    /**
     * 模型插件种类
     */
    private String patch;

    /**
     * 话术体验二维码ID
     */
    private String qrCodeId;

    /**
     * 是否开启自定义/领域混合模型
     */
    private Boolean mixedModel;

    /**
     * 赛道类型
     */
    private Integer customerTrackType;

    /**
     * 二级赛道类型
     */
    private Integer customerTrackSubType;
}
