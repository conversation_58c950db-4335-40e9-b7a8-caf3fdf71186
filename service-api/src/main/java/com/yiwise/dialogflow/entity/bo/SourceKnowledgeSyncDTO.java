package com.yiwise.dialogflow.entity.bo;

import com.yiwise.base.common.utils.bean.DeepCopyUtils;
import com.yiwise.dialogflow.entity.po.KnowledgePO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.vo.BotVO;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/9/14 14:50:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SourceKnowledgeSyncDTO implements Serializable {
    /**
     * 源知识意图映射集合（name->List<IntentPO>）
     */
    Map<String, List<IntentPO>> sourceNameToIntentPOMap;

    /**
     * 源botVO
     */
    BotVO sourceBotVO;

    /**
     * 源bot的变量映射map（变量id->变量名）
     */
    Map<String, String> SourceIdToVariableNameMap;

    /**
     * 源知识
     */
    KnowledgePO knowledgePO;

    DependentResourceBO sourceDependResource;

    public KnowledgePO getKnowledgePO() {
        return DeepCopyUtils.copyObject(knowledgePO);
    }

    Set<String> preCreateIntetnIdList;

    public Map<String, List<IntentPO>> getSourceNameToIntentPOMap() {
        return DeepCopyUtils.copyObject(sourceNameToIntentPOMap);
    }

}
