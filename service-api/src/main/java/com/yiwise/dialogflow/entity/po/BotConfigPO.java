package com.yiwise.dialogflow.entity.po;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
@Document(collection = BotConfigPO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_botId", def = "{botId: 1}")
)
public class BotConfigPO {

    public static final String COLLECTION_NAME = "botConfig";

    @Id
    Long botId;

    /**
     * 音频设置
     */
    BotAudioConfigPO audioConfig;

    /**
     * 语音设置
     */
    BotSpeechConfigPO speechConfig;
}
