package com.yiwise.dialogflow.entity.query;

import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RobotSnapshotCreateRequest {
    Long botId;

    RobotSnapshotUsageTargetEnum usageTarget;

    Boolean requireValidAudio;

    Boolean requireValidActionRule;

    Boolean requireValidOutsideResource;

    /**
     * 模型不是正在训练
     */
    Boolean intentModelNotTraining;

    /**
     * 存在意图模型
     */
    Boolean requireExistIntentModel;

    Long userId;

    Boolean ignoreWarning;

    Boolean ignoreRequireVariableBind;

    /**
     * 保存快照时, 是否保存语料数据
     */
    boolean keepCorpus;
}
