package com.yiwise.dialogflow.entity.po.remote;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR> xiayu
 * @date : 2021/7/19 15:52
 **/
@Data
public class CustomerTrackTypePO {

    @Id
    @GeneratedValue(generator = "JDBC")
    private Long customerTrackTypeId;

    /**
     * 客户赛道类型
     */
    private Integer customerTrackType;

    /**
     * 最大外呼前缀
     */
    private Long customerTrackMaxPre;

    /**
     * 客户赛道名称
     */
    private String customerTrackTypeName;

    /**
     * 隶属的一级客户赛道
     */
    private Integer customerTrackParentType;

    /**
     * 隶属的客户行业
     */
    private String customerIndustryType;

    /**
     * 是否有效
     */
    private Boolean enabledStatus;

}
