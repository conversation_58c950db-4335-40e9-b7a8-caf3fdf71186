package com.yiwise.dialogflow.entity.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class IdNamePair<K, V> implements Serializable {
    K id;

    V name;

    public IdNamePair() {

    }

    public IdNamePair(K id, V name) {
        this.id = id;
        this.name = name;
    }

    public static <K, V> IdNamePair<K, V> of(K id, V name) {
        return new IdNamePair<K, V>(id, name);
    }
}
