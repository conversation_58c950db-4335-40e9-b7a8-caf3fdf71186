package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum LargeModelEntityModelTypeEnum implements CodeDescEnum {

    /**
     * 微调模型
     */
    TUNED(0, "微调模型"),

    /**
     * 通用模型
     */
    COMMON(1, "通用模型"),
    ;

    LargeModelEntityModelTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    final Integer code;
    final String desc;

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public static boolean isTuneModel(LargeModelEntityModelTypeEnum type) {
        return TUNED == type;
    }

    public static boolean isCommonModel(LargeModelEntityModelTypeEnum type) {
        return COMMON == type;
    }
}