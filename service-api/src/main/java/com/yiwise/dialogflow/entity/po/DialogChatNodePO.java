package com.yiwise.dialogflow.entity.po;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yiwise.dialogflow.entity.enums.ExceedThresholdReplayStrategyEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DialogChatNodePO extends DialogBaseNodePO implements NonLeafNode {
    /**
     *  节点选择的意图列表
     */
    private List<String> selectIntentIdList;

    /**
     * 意图关联的节点
     * key: 意图 id
     * value: 节点 id
     */
    private Map<String, String> intentRelatedNodeMap;

    /**
     * 是否开启自定义重播
     */
    private Boolean enableCustomReplay;

    /**
     * 自定义重播阈值
     */
    private Integer customReplayThreshold;

    /**
     * 在回到原主流程时, 如果超过重播阈值, 重播策略
     */
    private ExceedThresholdReplayStrategyEnum exceedThresholdReplayStrategy = ExceedThresholdReplayStrategyEnum.DEFAULT_BRANCH;

    /**
     * 是否开启用户无应答时长
     */
    private Boolean enableCustomUserSilence;

    /**
     * 针对当前这个答案设置的用户无应答时长
     */
    private Double customUserSilenceSecond;

    /**
     * 是否启用节点意图优先
     */
    private boolean enableNodeIntentFirst;

    /**
     * 是否开启等待用户说完(ai应答时长)
     */
    private Boolean enableWaitUserSayFinish;

    /**
     * 等待时长, 单位毫秒
     */
    private Integer waitUserSayFinishMs;

    /**
     * 触发意图id列表, 仅触发此意图时, 才会执行等待用户说完逻辑
     * 此字段应该是当前节点关联意图子集
     */
    private List<String> triggerWaitIntentIdList;

    /**
     * 是否启用强制拉回
     * 该逻辑是, 跳转到问答知识时, 如果问答知识配置的是等待用户应答, 那么也会执行强制执行回到原主动流程的逻辑
     * 所以该配置也仅可以在主流程中配置, 但是先不校验吧
     */
    private Boolean enablePullback;

    @JsonIgnore
    @Override
    public Map<String, String> getRelatedNodeMap() {
        if (intentRelatedNodeMap == null) {
            intentRelatedNodeMap = new HashMap<>();
        }
        return intentRelatedNodeMap;
    }

    @Override
    public void setRelatedNodeMap(Map<String, String> relatedNodeMap) {
        this.intentRelatedNodeMap = relatedNodeMap;
    }

    @JsonIgnore
    @Override
    public List<String> getNextNodeIdList() {
        if (MapUtils.isEmpty(intentRelatedNodeMap)) {
            return new ArrayList<>();
        }
        return intentRelatedNodeMap.values()
                .stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 如果节点关联的意图已经变更了, 此时需要对触发意图列表进行过滤
     */
    public void resetWaitUserSayTriggerIntentIdList() {
        if (BooleanUtils.isTrue(enableWaitUserSayFinish) && CollectionUtils.isNotEmpty(triggerWaitIntentIdList)) {
            Set<String> selectIntentIdSet = new HashSet<>();
            if (CollectionUtils.isNotEmpty(selectIntentIdList)) {
                selectIntentIdSet.addAll(selectIntentIdList);
            }
            triggerWaitIntentIdList = triggerWaitIntentIdList.stream()
                    .filter(selectIntentIdSet::contains)
                    .collect(Collectors.toList());
        }
    }
}
