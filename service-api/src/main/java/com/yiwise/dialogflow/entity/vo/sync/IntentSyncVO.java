package com.yiwise.dialogflow.entity.vo.sync;

import com.yiwise.dialogflow.entity.enums.SyncModeEnum;
import com.yiwise.dialogflow.entity.query.IntentQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/1
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IntentSyncVO extends BasicSyncVO {

    /**
     * 意图相同处理
     */
    SyncModeEnum sameIntent;

    /**
     * 意图查询条件
     */
    IntentQuery intentQuery;

    /**
     * 是否同步模型训练
     */
    Boolean syncIntentConfig;
}
