package com.yiwise.dialogflow.entity.vo;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlgorithmLabelUpdateVO implements Serializable {

    /**
     * 话术id
     */
    @NotNull(message = "botId不能为空")
    Long botId;

    /**
     * 流程id
     */
    @NotBlank(message = "stepId不能为空")
    String stepId;

    /**
     * 节点id
     */
    @NotBlank(message = "nodeId不能为空")
    String nodeId;

    /**
     * 标签
     */
    String label;
}