package com.yiwise.dialogflow.entity.po.llm;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

/**
 * 文档片段
 */
@Data
@Document(collection = RagDocumentSegmentPO.COLLECTION_NAME)
@CompoundIndexes({
        @CompoundIndex(name = "idx_ragDocumentId", def = "{ragDocumentId: 1}"),
        @CompoundIndex(name = "idx_botId", def = "{botId: 1}")
}
)
public class RagDocumentSegmentPO implements Serializable {

    public static final String COLLECTION_NAME = "ragDocumentSegment";

    /**
     * 文档片段 id
     */
    @Id
    private String id;

    /**
     * botId
     */
    private Long botId;

    /**
     * 文档 id
     */
    private String ragDocumentId;

    /**
     * 片段内容
     */
    private String content;
}
