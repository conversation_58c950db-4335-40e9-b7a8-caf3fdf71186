package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.entity.po.SourceRefPO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IntentVO extends IntentPO {

    /**
     * 问法列表
     */
    List<String> corpusList;

    /**
     * 正则列表
     */
    List<String> regexList;

    /**
     * 描述列表
     */
    List<String> descList;

    /**
     * 问法数量
     */
    Integer corpusCount;

    /**
     * 正则数量
     */
    Integer regexCount;

    /**
     * 描述数量
     */
    Integer descCount;

    /**
     * 关联列表
     */
    List<SourceRefPO> intentRefPOList;

    /**
     * 是否被引用
     */
    Boolean hasReference;

    /**
     * 分组路径
     */
    String groupPath;

    /**
     * 是否存在同名知识
     */
    Boolean existsSameNameKnowledge;
}
