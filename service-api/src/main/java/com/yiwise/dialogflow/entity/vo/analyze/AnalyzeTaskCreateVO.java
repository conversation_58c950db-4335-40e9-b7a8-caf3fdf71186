package com.yiwise.dialogflow.entity.vo.analyze;

import com.yiwise.dialogflow.entity.po.analyze.AnalyzeTaskCorpusPO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AnalyzeTaskCreateVO implements Serializable {

    /**
     * 任务名称
     */
    @NotBlank(message = "name不能为空")
    String name;

    /**
     * 分析模板id
     */
    @NotBlank(message = "templateId不能为空")
    String templateId;

    /**
     * 语料列表
     */
    @Valid
    @NotEmpty(message = "corpusList不能为空")
    List<AnalyzeTaskCorpusPO> corpusList;
}
