package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum DialogFlowStatusEnum implements CodeDescEnum {
    DRAFT(0, "编辑中"),
    PENDING_APPROVAL(1, "等待审核"),
    REJECTED(2, "拒绝"),
    APPROVED(3, "审核通过");

    private Integer code;
    private String desc;

    DialogFlowStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

}
