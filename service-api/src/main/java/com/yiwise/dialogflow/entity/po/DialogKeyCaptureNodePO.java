package com.yiwise.dialogflow.entity.po;

import com.yiwise.dialogflow.engine.share.enums.KeyCaptureCommitModeEnum;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DialogKeyCaptureNodePO extends DialogChatNodePO implements NonLeafNode, Serializable {

    /**
     * 按键采集结果存储的动态变量id
     */
    private String resultVarId;

    /**
     * 按键提交模式, HASH-号后提交, ANY-按键后立即提交
     */
    private KeyCaptureCommitModeEnum commitMode;

    /**
     * 按键超时时间
     */
    private Integer timeout;

    /**
     * 是否开启*键重播
     */
    private Boolean enableReplay;

    /**
     * 是否开启失败后重试采集
     */
    private Boolean enableRetryOnFailed;

    /**
     * 重试次数
     */
    private Integer retryTimes;

    @Override
    public Set<String> calDependVariableIdSet(DependentResourceBO resource) {
        Set<String> variableIdSet = super.calDependVariableIdSet(resource);
        if (StringUtils.isNotBlank(resultVarId)) {
            variableIdSet.add(resultVarId);
        }
        return variableIdSet;
    }
}
