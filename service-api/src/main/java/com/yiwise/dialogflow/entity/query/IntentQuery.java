package com.yiwise.dialogflow.entity.query;

import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import com.yiwise.dialogflow.entity.enums.IntentSearchScopeEnum;
import com.yiwise.dialogflow.entity.enums.CorpusTypeEnum;
import com.yiwise.dialogflow.entity.enums.IntentPropertiesEnum;
import com.yiwise.dialogflow.entity.enums.IntentTypeEnum;
import com.yiwise.dialogflow.entity.enums.TrainStatusEnum;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/15
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IntentQuery extends AbstractQueryVO {

    /**
     * 话术ID
     */
    Long botId;

    /**
     * 意图ID
     */
    String intentId;

    /**
     * 意图ID列表
     */
    Collection<String> intentIdList;

    /**
     * 关键词
     */
    String keyword;

    /**
     * 意图类型
     */
    IntentTypeEnum intentType;

    /**
     * 语料类型
     */
    CorpusTypeEnum corpusType;

    /**
     * 意图属性
     */
    IntentPropertiesEnum intentProperties;

    /**
     * 训练状态
     */
    TrainStatusEnum trainStatus;

    /**
     * 搜索的scope集合
     */
    List<IntentSearchScopeEnum> intentSearchScopeList;

    /**
     * 分组id
     */
    String groupId;
}
