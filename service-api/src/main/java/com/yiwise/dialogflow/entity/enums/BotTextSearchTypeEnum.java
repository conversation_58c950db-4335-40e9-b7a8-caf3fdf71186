package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum BotTextSearchTypeEnum implements CodeDescEnum {
    ANSWER(0, "话术"),
    KEYWORD(1, "关键词"),
    CORPUS(2, "问法语料"),
    CUSTOM_VARIABLE(3, "自定义变量"),
    DYNAMIC_VARIABLE(4, "动态变量"),
    KNOWLEDGE_TITLE(5, "知识库标题"),
    STEP_TITLE(6, "对话流标题"),
    SMS_TEMPLATE(7, "短信模板"),
    LLM_STEP(8, "大模型流程"),
    SEND_SMS_ACTION(9, "发短信动作"),
    ACTION(10, "触发动作"),
    TEMPLATE_VAR_PROMPT(11, "模板变量提示词配置"),
    ;


    private int code;
    private String desc;

    BotTextSearchTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return null;
    }

    @Override
    public Integer getCode() {
        return null;
    }
}
