package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum BotGenerateStatusEnum implements CodeDescEnum {
    NONE(-1, "NONE"),
    // 生成成功
    SUCCESS(0, "生成成功"),
    // 生成失败
    FAIL(1, "生成失败"),

    READY(2, "准备中"),
    // 生成中
    PROCESSING(3, "处理中"),
    ;

    BotGenerateStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    final Integer code;
    final String desc;
    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
