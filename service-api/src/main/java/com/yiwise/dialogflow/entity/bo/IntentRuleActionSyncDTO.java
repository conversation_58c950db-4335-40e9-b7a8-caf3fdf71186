package com.yiwise.dialogflow.entity.bo;

import com.yiwise.base.common.utils.bean.DeepCopyUtils;
import com.yiwise.dialogflow.entity.vo.BotVO;
import com.yiwise.dialogflow.entity.vo.IntentRuleActionVO;
import com.yiwise.dialogflow.entity.vo.sync.RuleActionSyncVO;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/8 16:18:14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IntentRuleActionSyncDTO implements Serializable {

    /**
     * 待同步的动作配置
     */
    IntentRuleActionVO intentRuleActionVO;

    /**
     * 同步参数
     */
    RuleActionSyncVO ruleActionSyncVO;

    /**
     * 目标botId
     */
    Long targetBotId;

    /**
     * 源botVO
     */
    BotVO sourceBotVO;

    /**
     * 源 bot 意图 id -> 意图名称映射关系
     */
    Map<String, String> sourceIntentId2NameMapping = new HashMap<>();

    /**
     * 目标 bot, 意图名称-> 意图 id 映射关系
     */
    Map<String, String> targetIntentName2IdMapping = new HashMap<>();

    Map<String, String> srcVariableId2NameMapping = new HashMap<>();

    Map<String, String> targetVariableName2IdMapping = new HashMap<>();

    Map<String, String> srcEntityId2NameMap = new HashMap<>();

    Map<String, String> targetEntityName2IdMap = new HashMap<>();

    Map<String, String> stepIdLabelMap = new HashMap<>();

    Map<String, Map<String, String>> stepIdNodeIdLabelMap = new HashMap<>();

    Map<String, String> llmLabelIdMap = new HashMap<>();

    public IntentRuleActionVO getIntentRuleActionVO() {
        return DeepCopyUtils.copyObject(intentRuleActionVO);
    }
}