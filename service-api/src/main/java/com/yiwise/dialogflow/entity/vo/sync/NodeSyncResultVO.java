package com.yiwise.dialogflow.entity.vo.sync;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class NodeSyncResultVO implements Serializable {

    /**
     * 成功bot个数
     */
    Integer successNum;

    /**
     * 失败bot个数
     */
    Integer failNum;

    /**
     * <话术名称,失败原因>
     */
    Map<Long, String> failBotMap;
}