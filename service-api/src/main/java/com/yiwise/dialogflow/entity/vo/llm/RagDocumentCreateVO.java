package com.yiwise.dialogflow.entity.vo.llm;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RagDocumentCreateVO implements Serializable {

    @NotNull(message = "botId不能为空")
    Long botId;

    @NotBlank(message = "文档名称不能为空")
    String docName;

    @NotBlank(message = "url不能为空")
    String url;
}