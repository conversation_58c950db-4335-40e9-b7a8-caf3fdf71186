package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum QueryNodeApiTypeEnum implements CodeDescEnum {
    CUSTOM(0, "外部接口"),
    BUILT_IN(1, "内置接口"),

    ;

    final Integer code;
    final String desc;
    QueryNodeApiTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }


}
