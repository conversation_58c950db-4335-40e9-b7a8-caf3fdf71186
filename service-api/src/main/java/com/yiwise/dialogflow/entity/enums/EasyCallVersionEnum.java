package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum EasyCallVersionEnum implements CodeDescEnum {
    V1(1, "易呼1.0"),
    V2(2, "易呼2.0"),
    ;
    final Integer code;

    final String desc;
    EasyCallVersionEnum(Integer code , String desc) {
        this.code = code ;
        this.desc = desc;
    }


    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
