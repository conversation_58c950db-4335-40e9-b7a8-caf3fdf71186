package com.yiwise.dialogflow.entity.po;


import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date 2022-02-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "bot_ref")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BotRefPO extends BaseTimeUserIdPO implements Serializable {

	/**
	 * BotID
	 */
	Long botId;

	/**
	 * AICC侧的话术ID
	 */
	Long dialogFlowId;

	/**
	 * 租户ID
	 */
	Long tenantId;

	/**
	 * 删除状态
	 */
	EnabledStatusEnum enabledStatus;

}
