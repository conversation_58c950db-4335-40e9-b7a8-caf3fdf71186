package com.yiwise.dialogflow.entity.enums.llm;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum LlmGuideAnswerConfigEnum implements CodeDescEnum {
    ENABLE_GUIDE_ANSWER(0, "承接语设置"),
    COMPOSE_OPTIMIZATION(1, "短句合成"),


    ;
    LlmGuideAnswerConfigEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    final String desc;
    final Integer code;

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
