package com.yiwise.dialogflow.entity.po.intent;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/14
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Document(indexName = IntentCorpusPO.INDEX_NAME, type = IntentCorpusPO.TYPE_NAME)
public class IntentCorpusPO implements Serializable {

    public static final String INDEX_NAME = "intent_corpus";
    public static final String TYPE_NAME = "intent_corpus";

    @Id
    String id;

    @MultiField(
            mainField = @Field(type = FieldType.Text),
            otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)}
    )
    String name;

    @Field(type = FieldType.Long)
    Long botId;

    @Field(type = FieldType.Keyword)
    String intentId;

    /**
     * 对外展示的问法列表
     */
    @MultiField(
            mainField = @Field(type = FieldType.Text),
            otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)}
    )
    List<String> corpusList;

    /**
     * 内置的问法列表
     */
    @MultiField(
            mainField = @Field(type = FieldType.Text),
            otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)}
    )
    List<String> buildInCorpusList;

    /**
     * 对外展示的正则列表
     */
    @MultiField(
            mainField = @Field(type = FieldType.Text),
            otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)}
    )
    List<String> regexList;

    /**
     * 内置的正则列表
     */
    @MultiField(
            mainField = @Field(type = FieldType.Text),
            otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)}
    )
    List<String> buildInRegexList;

    /**
     * 描述列表
     */
    @MultiField(
            mainField = @Field(type = FieldType.Text),
            otherFields = {@InnerField(suffix = "keyword", type = FieldType.Keyword)}
    )
    List<String> descList;
}
