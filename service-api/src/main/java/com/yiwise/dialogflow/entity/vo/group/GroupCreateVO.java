package com.yiwise.dialogflow.entity.vo.group;

import com.yiwise.dialogflow.entity.enums.GroupTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/7/26
 */
@Data
public class GroupCreateVO implements Serializable {

    /**
     * 分组类型
     */
    private GroupTypeEnum type;

    /**
     * 上级id
     */
    private String parentId;

    /**
     * botId
     */
    private Long botId;

    /**
     * 创建人id
     */
    private Long userId;

    public Long getBotId() {
        return GroupTypeEnum.PUBLIC_AUDIO.equals(type) ? 0L : botId;
    }
}
