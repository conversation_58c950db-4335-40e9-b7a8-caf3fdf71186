package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/9/26
 * @class <code>AlgorithmPrivateIntentLevelEnum</code>
 * @see
 * @since JDK1.8
 */
public enum AlgorithmPrivateIntentLevelEnum implements CodeDescEnum {
    A(0, "A(同意加微信)"),
    B(1, "B级(意向一般)"),
    C(2, "C级(已经加微信)"),
    D(3, "D级(不同意加微信)"),
    G(4, "G级别(客户忙)"),
    H(5, "H级别(快速挂机)"),
    I(6, "I级别(已经退货)"),
    J(7, "J级别(待筛选)"),
    K(8, "K级别(不是机主本人)"),
    M(9, "M级别(免打扰客户)"),
    O(10, "O级别(提及发短信)"),
    P(11, "P级别(手机号不是微信号)"),
    Q(12, "Q级别(语音助手)"),
    R(13, "R级别(静音挂机)");

    private int code;
    private String desc;

    AlgorithmPrivateIntentLevelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @Override
    public String getDesc() {
        return this.desc;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }
}
