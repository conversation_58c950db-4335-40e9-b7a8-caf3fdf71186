package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.engine.share.QueryNodeApiTestResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryNodeApiTestResultVO extends QueryNodeApiTestResult implements Serializable {

    /**
     * 解析结果列表
     */
    private List<ParseResultVO> parseResultList;

    @Data
    public static class ParseResultVO implements Serializable {

        /**
         * 变量id
         */
        private String varId;

        /**
         * 变量名
         */
        private String varName;

        /**
         * jsonPath表达式
         */
        private String jsonPath;

        /**
         * 提取结果
         */
        private String value;
    }
}
