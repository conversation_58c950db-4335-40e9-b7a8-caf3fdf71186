package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum StepSubTypeEnum implements CodeDescEnum {

    /**
     * 普通流程
     */
    COMMON(1, "普通流程"),

    /**
     * LLM流程
     */
    LLM(2, "大模型流程"),
    ;

    private final Integer code;
    private final String desc;

    public static Boolean isLlm(StepSubTypeEnum type) {
        return LLM.equals(type);
    }

    public static Boolean isCommon(StepSubTypeEnum type) {
        return COMMON.equals(type);
    }
}