package com.yiwise.dialogflow.entity.vo.botgenerate;

import com.yiwise.dialogflow.entity.vo.BotCreateRequestVO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class BotCopyAndRewriteRequestVO extends BotCreateRequestVO {
    /**
     * 是否是批量操作, 因为批量操作和单个操作对于bot名称的处理是不一样的, 所以使用这个字段来区分
     */
    @NotEmpty(message = "isBatch不能为空")
    private Boolean isBatch;

    /**
     * 话术offer, 每行代表一个bot信息
     */
    private List<BotRewritePromptInfoVO> offerInfo;
}
