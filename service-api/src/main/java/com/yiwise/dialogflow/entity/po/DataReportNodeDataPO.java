package com.yiwise.dialogflow.entity.po;


import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/12/10
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Deprecated
public class DataReportNodeDataPO implements Serializable {

    /**
     * 序号
     */
    Integer seq;

    /**
     * 节点挂机率-最高
     */
    String maxHangupRate;

    /**
     * 节点挂机率-最低
     */
    String minHangupRate;

    /**
     * 节点挂机数-总和
     */
    Integer totalHangupCount = 0;

    /**
     * 节点挂机率-平均
     */
    String avgHangupRate;

    /**
     * 节点拒绝率-最高
     */
    String maxDeclineRate;

    /**
     * 节点拒绝率-最低
     */
    String minDeclineRate;

    /**
     * 拒绝数-总和
     */
    Integer totalDeclineCount = 0;

    /**
     * 节点拒绝率-平均
     */
    String avgDeclineRate;

    /**
     * 节点走向总数
     */
    Integer totalCount = 0;

    public void setHangupRate(String hangupRate) {
        if (StringUtils.isEmpty(this.maxHangupRate)) {
            this.maxHangupRate = hangupRate;
        }
        if (StringUtils.isEmpty(this.minHangupRate)) {
            this.minHangupRate = hangupRate;
        }
        this.maxHangupRate = String.valueOf(Math.max(Double.parseDouble(this.maxHangupRate), Double.parseDouble(hangupRate)));
        this.minHangupRate = String.valueOf(Math.min(Double.parseDouble(this.minHangupRate), Double.parseDouble(hangupRate)));
    }

    public void addHangupCount(Integer hangupCount) {
        this.totalHangupCount += hangupCount;
    }

    public void setDeclineRate(String declineRate) {
        if (StringUtils.isEmpty(this.maxDeclineRate)) {
            this.maxDeclineRate = declineRate;
        }
        if (StringUtils.isEmpty(this.minDeclineRate)) {
            this.minDeclineRate = declineRate;
        }
        this.maxDeclineRate = String.valueOf(Math.max(Double.parseDouble(this.maxDeclineRate), Double.parseDouble(declineRate)));
        this.minDeclineRate = String.valueOf(Math.min(Double.parseDouble(this.minDeclineRate), Double.parseDouble(declineRate)));
    }

    public void addDeclineCount(Integer declineCount) {
        this.totalDeclineCount += declineCount;
    }

    public void addTotalCount(Integer totalCount) {
        this.totalCount += totalCount;
    }
}