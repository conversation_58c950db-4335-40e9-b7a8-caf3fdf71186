package com.yiwise.dialogflow.entity.query;

import com.yiwise.dialogflow.entity.enums.SyncModeEnum;
import com.yiwise.dialogflow.entity.vo.sync.BasicSyncVO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class EntitySyncVO extends BasicSyncVO implements Serializable {

    /**
     * 相同实体处理逻辑
     */
    SyncModeEnum sameEntity;

    /**
     * 实体查询参数
     */
    EntityQueryVO query;
}
