package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 * @date 2022/10/27 14:22:38
 */
public enum AsrSourceConcurrencyRecordTypeEnum implements CodeDescEnum {
    ASR_VOCOB(1, "热词组"),
    ASR_SELF_LEARNING(2, "自学习模型");

    Integer code;
    String desc;

    AsrSourceConcurrencyRecordTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}