package com.yiwise.dialogflow.entity.enums.llm;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum DocumentSplitTypeEnum implements CodeDescEnum {

    // 自动分段
    AUTO(0, "自动分段"),
    // 自定义分段
    CUSTOM(2, "自定义分段"),
    ;

    private final Integer code;
    private final String desc;

    DocumentSplitTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public static boolean isAuto(DocumentSplitTypeEnum type) {
        return AUTO.equals(type);
    }
}