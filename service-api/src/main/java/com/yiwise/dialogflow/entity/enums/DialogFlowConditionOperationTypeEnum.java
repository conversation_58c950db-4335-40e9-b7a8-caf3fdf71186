package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum DialogFlowConditionOperationTypeEnum implements CodeDescEnum {
    GREATER_OR_EQUAL(0, ">="),
    LESS_OR_EQUAL(1, "<="),
    EQUAL(2, "="),
    CONTAIN_ANY(3, "包含任一"),
    CONTAIN_ALL(4, "包含所有"),
    TRUE(5, "是"),
    FALSE(6, "否"),
    CONSECUTIVE(7, "连续"),
    NOT_CONTAIN(8, "不包含"),
    SUCCESS(9, "成功"),
    FAILURE(10, "失败"),
    ALL_SUCCESS(11, "全部成功"),
    ALL_FAILURE(12, "全部失败"),
    ANY_SUCCESS(13, "任一成功"),
    ANY_FAILURE(14, "任一失败"),
    ALL_HIT(15, "全部命中"),
    ALL_MISS(16, "全部未命中"),
    ANY_HIT(17, "任一命中"),
    ANY_MISS(18, "任一未命中"),
    ANY_NULL(19, "任一为空"),
    ANY_NOT_NULL(20, "任一不为空"),
    ALL_NULL(21, "全部为空"),
    ALL_NOT_NULL(22, "全部不为空"),
    ;

    private Integer code;
    private String desc;

    DialogFlowConditionOperationTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

}
