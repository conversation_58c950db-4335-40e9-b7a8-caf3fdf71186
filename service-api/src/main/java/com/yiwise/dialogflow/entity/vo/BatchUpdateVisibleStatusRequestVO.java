package com.yiwise.dialogflow.entity.vo;

import com.yiwise.base.model.enums.EnabledStatusEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BatchUpdateVisibleStatusRequestVO extends BotBatchExportRequestVO implements Serializable {

    /**
     * 可见状态
     */
    @NotNull(message = "visibleStatus不能为空")
    EnabledStatusEnum visibleStatus;
}