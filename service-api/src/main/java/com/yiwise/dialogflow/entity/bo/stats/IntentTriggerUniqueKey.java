package com.yiwise.dialogflow.entity.bo.stats;

import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@EqualsAndHashCode
@ToString
public class IntentTriggerUniqueKey {
    AnswerSourceEnum targetType;
    String targetId;
    String targetLabel;

    public IntentTriggerUniqueKey(AnswerSourceEnum targetType, String targetId, String targetLabel) {
        this.targetType = targetType;
        this.targetId = targetId;
        this.targetLabel = targetLabel;
    }
    public static IntentTriggerUniqueKey of(AnswerSourceEnum targetType, String targetId, String targetLabel) {
        return new IntentTriggerUniqueKey(targetType, targetId, targetLabel);
    }
}
