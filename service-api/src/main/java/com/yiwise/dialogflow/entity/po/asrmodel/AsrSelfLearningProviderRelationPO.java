package com.yiwise.dialogflow.entity.po.asrmodel;


import com.yiwise.base.model.bean.po.BaseTimePO;
import com.yiwise.dialogflow.engine.share.enums.AsrProviderEnum;
import com.yiwise.dialogflow.entity.enums.asr.AsrSlefLearningTrainStatusEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 自学习模型与供应商关联表
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "asr_self_learning_provider_relation")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrSelfLearningProviderRelationPO extends BaseTimePO implements Serializable {

    @Id
    @GeneratedValue(generator = "JDBC")
    Long asrSelfLearningProviderRelationId;

    /**
     * 自学习模型主键
     */
    Long asrSelfLearningId;

    /**
     * 供应商名称
     */
    AsrProviderEnum provider;

    /**
     * 供应商侧模型id
     */
    String providerModelId;

    /**
     * 训练状态
     */
    AsrSlefLearningTrainStatusEnum trainStatus;

    /**
     * 厂商侧数据集id（腾讯无此参数）
     */
    String dataId;

}
