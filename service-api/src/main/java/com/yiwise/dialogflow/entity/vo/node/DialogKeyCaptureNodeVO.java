package com.yiwise.dialogflow.entity.vo.node;

import com.yiwise.dialogflow.engine.share.enums.KeyCaptureCommitModeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DialogKeyCaptureNodeVO extends DialogChatNodeVO implements Serializable {

    /**
     * 按键采集结果存储的动态变量id
     */
    private String resultVarId;

    /**
     * 按键提交模式
     */
    private KeyCaptureCommitModeEnum commitMode;

    /**
     * 按键超时时间
     */
    private Integer timeout;

    /**
     * 是否开启*键重播
     */
    private Boolean enableReplay;

    /**
     * 是否开启失败后重试采集
     */
    private Boolean enableRetryOnFailed;

    /**
     * 重试次数
     */
    private Integer retryTimes;
}
