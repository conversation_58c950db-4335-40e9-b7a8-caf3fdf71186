package com.yiwise.dialogflow.entity.vo.sync;

import com.yiwise.dialogflow.entity.enums.StepTypeEnum;
import com.yiwise.dialogflow.entity.enums.SyncModeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/9/1
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StepSyncVO extends BasicSyncVO {

    /**
     * 同步至
     */
    StepTypeEnum stepTypeEnum;

    /**
     * 流程相同处理
     */
    SyncModeEnum sameStep;

    /**
     * 触发意图相同处理
     */
    SyncModeEnum sameTriggerIntent;

    /**
     * 节点意图相同处理
     */
    SyncModeEnum sameNodeIntent;

    /**
     * 相同实体处理
     */
    SyncModeEnum sameEntity;

    /**
     * 同步真人语音
     */
    Boolean syncAudio;

}
