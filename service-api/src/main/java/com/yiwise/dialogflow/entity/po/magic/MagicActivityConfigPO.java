package com.yiwise.dialogflow.entity.po.magic;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.entity.po.TtsVoiceConfigPO;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;

/**
 * 轻量化活动配置, 配置活动的模板变量和 tts 配置信息
 */
@Data
@Document(collection = MagicActivityConfigPO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_botId_callJob", def = "{botId: 1, callJobId: 1}")
)
public class MagicActivityConfigPO extends BaseTimeUserIdPO {

    public static final String COLLECTION_NAME = "magicActivityConfig";

    @Id
    private String id;

    /**
     * botId
     */
    private Long botId;

    /**
     * 活动 id
     */
    private Long activityId;

    /**
     * 活动关联的新版外呼任务 id
     */
    private Long callJobId;

    /**
     * 租户 id
     */
    private Long tenantId;

    /**
     * 模板变量值 map, key 变量名, value: 变量值
     */
    private Map<String, String> templateVarValueMap;

    /**
     * tts配置
     */
    private TtsVoiceConfigPO ttsConfig;

    /**
     * 客户在创建轻量化活动时选择的可选变量名称集合
     */
    private List<String> selectedVariableNameList;
}
