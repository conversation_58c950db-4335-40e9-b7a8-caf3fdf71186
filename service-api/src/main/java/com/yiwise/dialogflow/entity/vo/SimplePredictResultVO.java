package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.entity.enums.CorpusTypeEnum;
import com.yiwise.dialogflow.entity.enums.IntentPropertiesEnum;
import com.yiwise.dialogflow.entity.enums.IntentTypeEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/4
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SimplePredictResultVO implements Serializable {
    /**
     * 意图id
     */
    String intentId;

    /**
     * 意图名称
     */
    String intentName;

    /**
     * 意图属性
     */
    IntentPropertiesEnum intentProperties;

    /**
     * 匹配关键词列表
     */
    List<String> matchKeywordList;

    /**
     * 算法置信度
     */
    Double confidence;

    /**
     * 意图类型
     */
    IntentTypeEnum intentType;

    /**
     * 语料类型
     */
    CorpusTypeEnum corpusType;
}