package com.yiwise.dialogflow.entity.po;

import com.yiwise.dialogflow.entity.enums.SyncModeEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BatchSyncAudioRequestVO implements Serializable {

    /**
     * 来源botId
     */
    @NotNull(message = "srcBotId不能为空")
    Long srcBotId;

    /**
     * 目标botId列表
     */
    @NotEmpty(message = "targetBotIdList不能为空")
    List<Long> targetBotIdList;

    /**
     * 答案id列表
     */
    @NotEmpty(message = "answerIdList不能为空")
    private List<String> answerIdList;

    /**
     * 同步模式
     */
    @NotNull(message = "syncMode不能为空")
    private SyncModeEnum syncMode;
}