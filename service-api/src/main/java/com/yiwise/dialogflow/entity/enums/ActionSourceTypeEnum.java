package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/5/31
 * @class <code>ActionSourceTypeEnum</code>
 * @see
 * @since JDK1.8
 */
public enum ActionSourceTypeEnum implements CodeDescEnum {
    NODE(1, "节点"),
    KNOWLEDGE(2, "知识"),
    INTENT_RULE(3, "意向规则"),
    SPECIAL_ANSWER(4, "特殊语境"),
    ;

    String desc;
    Integer code;

    ActionSourceTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
