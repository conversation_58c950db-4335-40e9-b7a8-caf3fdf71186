package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum IntentLevelEnum implements CodeDescEnum {
    A(0, "A级(较强)"),
    B(1, "B级(一般)"),
    C(2, "C级(无法判断)"),
    D(3, "D级(很少)"),
    E(4, "E级别(需要再次跟进)"),
    F(5, "F级别(无需再次跟进)"),
    <PERSON>(6, "G级别(无需再次跟进)"),
    H(7, "H级别(无需再次跟进)"),
    I(8, "I级别(无需再次跟进)"),
    J(9, "J级别(无需再次跟进)"),
    <PERSON>(10, "K级别(无需再次跟进)"),
    L(11, "L级别(无需再次跟进)"),
    M(12, "M级别(无需再次跟进)"),
    N(13, "N级别(无需再次跟进)"),
    <PERSON>(14, "O级别(无需再次跟进)"),
    P(15, "P级别(无需再次跟进)"),
    <PERSON>(16, "Q级别(无需再次跟进)"),
    R(17, "R级别(无需再次跟进)"),
    <PERSON>(18, "S级别(无需再次跟进)"),
    T(19, "T级别(无需再次跟进)"),
    U(20, "T级别(无需再次跟进)"),
    V(21, "T级别(无需再次跟进)"),
    W(22, "T级别(无需再次跟进)"),
    X(23, "T级别(无需再次跟进)"),
    Y(24, "T级别(无需再次跟进)"),
    Z(25, "T级别(无需再次跟进)"),
    a(26, "T级别(无需再次跟进)"),
    b(27, "T级别(无需再次跟进)"),
    c(28, "T级别(无需再次跟进)"),
    d(29, "T级别(无需再次跟进)"),
    e(30, "T级别(无需再次跟进)"),
    f(31, "T级别(无需再次跟进)"),
    g(32, "T级别(无需再次跟进)"),
    h(33, "T级别(无需再次跟进)"),
    i(34, "T级别(无需再次跟进)"),
    j(35, "T级别(无需再次跟进)"),
    k(36, "T级别(无需再次跟进)"),
    l(37, "T级别(无需再次跟进)"),
    m(38, "T级别(无需再次跟进)"),
    n(39, "T级别(无需再次跟进)"),
    o(40, "T级别(无需再次跟进)"),
    p(41, "T级别(无需再次跟进)"),
    q(42, "T级别(无需再次跟进)"),
    r(43, "T级别(无需再次跟进)"),
    s(44, "T级别(无需再次跟进)"),
    t(45, "T级别(无需再次跟进)"),
    u(46, "T级别(无需再次跟进)"),
    v(47, "T级别(无需再次跟进)"),
    w(48, "T级别(无需再次跟进)"),
    x(49, "T级别(无需再次跟进)"),
    y(50, "T级别(无需再次跟进)"),
    z(51, "T级别(无需再次跟进)"),
    ;

    private Integer code;
    private String desc;

    IntentLevelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public boolean isStrong() {
        return A.getCode().equals(code) || B.getCode().equals(code);
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
