package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum AnalyzeTemplateIntentCategoryEnum implements CodeDescEnum {

    /**
     * 黑名单
     */
    BLACK(0, "黑名单"),

    /**
     * 白名单
     */
    WHITE(1, "白名单"),
    ;

    private final Integer code;
    private final String desc;

    AnalyzeTemplateIntentCategoryEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Boolean isBlack(AnalyzeTemplateIntentCategoryEnum category) {
        return category == BLACK;
    }

    public static Boolean isWhite(AnalyzeTemplateIntentCategoryEnum category) {
        return category == WHITE;
    }
}
