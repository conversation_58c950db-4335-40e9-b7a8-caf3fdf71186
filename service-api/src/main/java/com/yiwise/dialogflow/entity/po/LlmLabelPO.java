package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.entity.enums.LlmLabelTypeEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@Document(collection = LlmLabelPO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_botId", def = "{botId: 1}")
)
public class LlmLabelPO extends BaseTimeUserIdPO implements Serializable {

    public static final String COLLECTION_NAME = "llmLabel";

    @Id
    String id;

    /**
     * 话术id
     */
    Long botId;

    /**
     * 名称
     */
    String name;

    /**
     * 描述列表
     */
    List<String> descList;

    /**
     * 类型
     */
    LlmLabelTypeEnum type;
}