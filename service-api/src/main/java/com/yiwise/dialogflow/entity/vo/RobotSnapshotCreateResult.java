package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.entity.bo.SnapshotInvalidFailItemMsg;
import com.yiwise.dialogflow.entity.po.RobotSnapshotPO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class RobotSnapshotCreateResult {

    RobotSnapshotPO snapshot;

    /**
     * 是否成功
     */
    boolean success;

    /**
     * 失败原因列表
     */
    List<SnapshotInvalidFailItemMsg> msgList;

    /**
     * 是否需要绑定变量
     */
    Boolean needBindVariable;

    /**
     * 变量绑定信息列表
     */
    List<VariableBindInfoVO> variableBindInfoList;

    public void setMsgList(List<SnapshotInvalidFailItemMsg> msgList) {
        if (CollectionUtils.isNotEmpty(msgList)) {
            this.msgList = msgList.stream()
                    .sorted(Comparator.comparing(item -> BooleanUtils.isTrue(item.getIsWarning())? 1 : 0))
                    .collect(Collectors.toList());
        } else {
            this.msgList = Collections.emptyList();
        }
    }
}
