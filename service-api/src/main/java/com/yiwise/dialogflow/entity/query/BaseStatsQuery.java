package com.yiwise.dialogflow.entity.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import com.yiwise.base.model.enums.SystemEnum;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Data
public class BaseStatsQuery extends AbstractQueryVO {
    /**
     * 是否开启统计数据, 默认关闭, 开启才返回统计数据信息
     */
    Boolean enableStatsInfo;

    /**
     * 租户id, 从登录信息中获取
     */
    Long tenantId;

    /**
     * botId
     */
    @NotNull(message = "botId不能为空")
    Long botId;

    /**
     * 开始时间(包含) yyyy-MM-dd HH:mm:ss
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss", iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime beginTime;

    /**
     * 结束时间(包含) yyyy-MM-dd HH:mm:ss
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss", iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime endTime;

    /**
     * 查询ma流程数据, 默认false
     */
    Boolean queryMaFlow;

    /**
     * 是否查询新版外呼任务数据, 默认false
     */
    Boolean queryNewCallJob;

    /**
     * 查询呼入统计
     */
    Boolean queryCallIn;

    /**
     * 任务id列表, 全选传空
     */
    List<Long> callJobIdList;

    /**
     * ma流程id列表, 全选传空
     */
    List<Long> maFlowIdList;

    /**
     * 新版外呼任务id列表, 全选传空
     */
    List<Long> newCallJobIdList;

    String direction;

    /**
     * 排序字段
     */
    String orderBy;

    public Long getTenantId() {
        if (Objects.nonNull(tenantId) && tenantId == 0) {
            return null;
        }
        return tenantId;
    }

    SystemEnum systemType;
}
