package com.yiwise.dialogflow.entity.vo.analyze;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AnalyzeTemplateIntentRevertVO implements Serializable {

    /**
     * 模板id
     */
    @NotBlank(message = "templateId不能为空")
    String templateId;

    /**
     * 意图id
     */
    @NotBlank(message = "id不能为空")
    String id;
}
