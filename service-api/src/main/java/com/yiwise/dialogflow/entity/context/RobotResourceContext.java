package com.yiwise.dialogflow.entity.context;

import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.entity.bo.*;
import com.yiwise.dialogflow.entity.enums.BotCreateSourceEnum;
import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.RobotSnapshotPO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 复制bot时用到的上下文
 *
 * <AUTHOR>
 * @date 2022/6/13
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RobotResourceContext {

    Long srcBotId;

    Long targetBotId;

    Long srcTenantId;

    Long targetTenantId;

    Long currentUserId;

    Long intentLevelTagId;

    Integer customerTrackType;

    Integer customerTrackSubType;

    RobotSnapshotPO snapshot;

    SystemEnum systemEnum;

    ResourceCopyReferenceMappingBO resourceCopyReferenceMapping;

    SnapshotValidateConfigBO validateConfig;

    List<SnapshotInvalidFailItemMsg> invalidMsgList;

    DependentResourceBO dependentResource;

    ActionNameResourceBO actionNameResource;

    List<Integer> intentTagDetailCodeList;

    BotCreateSourceEnum botCreateSource;

    RobotSnapshotUsageTargetEnum usageTarget;

    V3BotTypeEnum srcBotType;

    V3BotTypeEnum targetBotType;

    boolean isExport;

    BotPO srcBot;

    BotPO targetBot;

    private RobotResourceContext() {

    }

    public static RobotResourceContext init(Long srcBotId, Long targetBotId, Long currentUserId, SystemEnum systemEnum) {
        RobotResourceContext context = new RobotResourceContext();
        context.setSrcBotId(srcBotId);
        context.setTargetBotId(targetBotId);
        context.setCurrentUserId(currentUserId);
        context.setSystemEnum(systemEnum);
        context.setSnapshot(new RobotSnapshotPO());
        context.setResourceCopyReferenceMapping(new ResourceCopyReferenceMappingBO());
        context.setInvalidMsgList(new ArrayList<>());
        context.setIntentTagDetailCodeList(Collections.emptyList());
        return context;
    }

    public static RobotResourceContext init(Long srcBotId, Long targetBotId, Long currentUserId, SystemEnum systemEnum, Long intentLevelTagId, List<Integer> intentTagDetailCodeList) {
        RobotResourceContext context = init(srcBotId, targetBotId, currentUserId, systemEnum);
        context.setIntentLevelTagId(intentLevelTagId);
        context.setIntentTagDetailCodeList(intentTagDetailCodeList);
        return context;
    }

    public boolean isCopy() {
        return !this.srcBotId.equals(this.targetBotId);
    }

    public boolean isImport() {
        return Long.valueOf(0).equals(this.srcBotId);
    }

    public boolean noNeedSensitiveWordsCheck() {
        // 通过open api创建的bot在发布审核的时候进行敏感词检测
        return !RobotSnapshotUsageTargetEnum.CALL_OUT.equals(usageTarget) || !BotCreateSourceEnum.OPEN_API.equals(snapshot.getBot().getCreateSource());
    }

    public boolean isCopyFromCommonBotToMagicTemplateBot() {
        return V3BotTypeEnum.isCommon(srcBotType) && V3BotTypeEnum.isMagicTemplate(targetBotType);
    }

    public boolean isMagicTemplateBot() {
        return V3BotTypeEnum.isMagicTemplate(srcBotType);
    }
}
