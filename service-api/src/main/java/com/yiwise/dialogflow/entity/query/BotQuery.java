package com.yiwise.dialogflow.entity.query;

import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.entity.enums.AuditStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/2/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BotQuery extends AbstractQueryVO {

    /**
     * 租户ID
     */
    Long tenantId;

    /**
     * 名称
     */
    String name;

    /**
     * BotID
     */
    Long botId;

    /**
     * AICC侧话术ID
     */
    Long dialogFlowId;

    /**
     * 审核状态
     */
    Set<AuditStatusEnum> auditStatusSet;

    /**
     * 赛道ID
     */
    List<Integer> customerTrackIdList;

    /**
     * 二级赛道ID
     */
    List<Integer> customerTrackSubIdList;

    /**
     * 录音师id
     */
    Long recordUserId;

    /**
     * botId列表, 用于批量查询
     */
    List<Long> botIdList;

    /**
     * 话术文件夹ID
     */
    Long folderId;

    /**
     * 系统类型
     */
    SystemEnum systemType;

    Boolean orderByUpdateTimeDesc;

    /**
     * 热词组id
     */
    Long asrVocabId;

    /**
     * 自学习模型id
     */
    Long asrSelfLearningDetailId;

    /**
     * 热词组id集合
     */
    List<Long> asrVocabIdList;

    /**
     * 自学习模型id集合
     */
    List<Long> asrSelfLearningDetailIdList;

    /**
     * 创建人id列表
     */
    List<Long> createUserIdList;

    /**
     * 场景
     */
    private List<Integer> sceneIdList;

    /**
     * 话术文件夹ID列表
     */
    private List<Long> folderIdList;

    /**
     * 不在任何文件夹下
     */
    private Boolean notInAnyFolder;

    /**
     * 是否已发布过
     */
    private Boolean published;

    /**
     * 类型列表
     */
    private List<Integer> typeList;

    /**
     * 可见状态
     */
    private EnabledStatusEnum visibleStatus;

    private List<String> getSearchKeywords() {
        if (StringUtils.isNotBlank(name) && name.contains(" ")) {
            return Arrays.stream(name.split("\\s+"))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 录音师id列表
     */
    private List<Long> recordUserIdList;
}
