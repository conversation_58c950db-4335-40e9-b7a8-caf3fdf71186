package com.yiwise.dialogflow.entity.po;

import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public interface MismatchAndUninterrupted extends Mismatch, Uninterrupted {

    default List<String> validMismatchAndInterruptConflict(String prefix, DependentResourceBO resource) {
        List<String> errorMsgList = new ArrayList<>();
        if (BooleanUtils.isTrue(getMismatchKnowledgeAndStep())) {
            // 都是不关联部分的情况下
            // 且三个关联的都是空
            boolean needSelectKnowledge = false;
            boolean needSelectStep = false;
            boolean needSelectSpecialAnswer = false;
            Set<String> validStepIdSet = Collections.emptySet();
            Set<String> validIdSet = Collections.emptySet();
            Set<String> validSpecialAnswerIdSet = Collections.emptySet();
            if (BooleanUtils.isNotTrue(getMismatchAllStep())) {
                if (CollectionUtils.isNotEmpty(getMismatchStepIdList())) {
                    validStepIdSet = getMismatchStepIdList().stream().filter(resource.getStepIdNameMap()::containsKey).collect(Collectors.toSet());
                }
                if (CollectionUtils.isEmpty(validStepIdSet)) {
                    needSelectStep = true;
                }
            }

            if (BooleanUtils.isNotTrue(getMismatchAllKnowledge())) {
                if (CollectionUtils.isNotEmpty(getMismatchKnowledgeIdList())) {
                    validIdSet = getMismatchKnowledgeIdList().stream().filter(resource.getKnowledgeIdNameMap()::containsKey).collect(Collectors.toSet());
                }
                if (CollectionUtils.isEmpty(validIdSet)) {
                    needSelectKnowledge = true;
                }
            }

            if (BooleanUtils.isNotTrue(getMismatchAllSpecialAnswerConfig())) {
                if (CollectionUtils.isNotEmpty(getMismatchSpecialAnswerConfigIdList())) {
                    validSpecialAnswerIdSet = getMismatchSpecialAnswerConfigIdList().stream().filter(resource.getSpecialAnswerIdNameMap()::containsKey).collect(Collectors.toSet());
                }
                if (CollectionUtils.isEmpty(validSpecialAnswerIdSet)) {
                    needSelectSpecialAnswer = true;
                }
            }
            if (needSelectStep && needSelectKnowledge && needSelectSpecialAnswer) {
                errorMsgList.add(String.format("%s: 未选择不关联的问答知识/流程/特殊语境", prefix));
            }

            // 如果开启了不允许打断, 但是又选择允许打断的知识是不关联的, 则校验失败
            if (BooleanUtils.isTrue(getEnableUninterrupted())
                    && (Objects.isNull(getCustomInterruptThreshold())
                    || (Objects.nonNull(getCustomInterruptThreshold()) && getCustomInterruptThreshold() > 0))) {

                Set<String> interruptKnowledgeIdSet = Collections.emptySet();
                Set<String> interruptStepIdSet = Collections.emptySet();
                Set<String> interruptSpecialAnswerIdSet = Collections.emptySet();
                if (CollectionUtils.isNotEmpty(getUninterruptedReplyStepIdList())) {
                    interruptStepIdSet = getUninterruptedReplyStepIdList().stream()
                            .filter(resource.getStepIdNameMap()::containsKey)
                            .collect(Collectors.toSet());
                }

                if (CollectionUtils.isNotEmpty(getUninterruptedReplyKnowledgeIdList())) {
                    interruptKnowledgeIdSet = getUninterruptedReplyKnowledgeIdList().stream()
                            .filter(resource.getKnowledgeIdNameMap()::containsKey)
                            .collect(Collectors.toSet());
                }

                if (CollectionUtils.isNotEmpty(getUninterruptedReplySpecialAnswerIdList())) {
                    interruptSpecialAnswerIdSet = getUninterruptedReplySpecialAnswerIdList().stream()
                            .filter(resource.getSpecialAnswerIdNameMap()::containsKey)
                            .collect(Collectors.toSet());
                }

                boolean interruptSomeStep = CollectionUtils.isNotEmpty(interruptStepIdSet);
                boolean interruptSomeKnowledge = CollectionUtils.isNotEmpty(interruptKnowledgeIdSet);
                boolean interruptSomeSpecialAnswer = CollectionUtils.isNotEmpty(interruptSpecialAnswerIdSet);

                if ((BooleanUtils.isTrue(getMismatchAllKnowledge()) && interruptSomeKnowledge)
                        || CollectionUtils.isNotEmpty(CollectionUtils.intersection(interruptKnowledgeIdSet, validIdSet))) {
                    errorMsgList.add(String.format("%s: 不关联问答知识/流程/特殊语境和允许打断问答知识/流程/特殊语境重复", prefix));
                }

                if ((BooleanUtils.isTrue(getMismatchAllStep()) && interruptSomeStep)
                        || CollectionUtils.isNotEmpty(CollectionUtils.intersection(interruptStepIdSet, validStepIdSet))) {
                    errorMsgList.add(String.format("%s: 不关联问答知识/流程/特殊语境和允许打断流程重复", prefix));
                }
                if (BooleanUtils.isTrue(getMismatchAllSpecialAnswerConfig()) && interruptSomeSpecialAnswer
                        || CollectionUtils.isNotEmpty(CollectionUtils.intersection(interruptSpecialAnswerIdSet, validSpecialAnswerIdSet))) {
                    errorMsgList.add(String.format("%s: 不关联问答知识/流程/特殊语境和允许打断特殊语境重复", prefix));
                }
            }
        }
        return errorMsgList;
    }

}
