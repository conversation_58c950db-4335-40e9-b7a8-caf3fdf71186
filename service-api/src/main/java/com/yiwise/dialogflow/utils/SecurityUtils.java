package com.yiwise.dialogflow.utils;

import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.middleware.redis.service.RedisOpsService;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

public class SecurityUtils {

    private static ThreadLocal<UserPO> threadLocal = new ThreadLocal<>();

    private static ThreadLocal<Long> doorForOpeUserIdThreadLocal = new ThreadLocal<>();

    // 用户请求ip地址
    private static ThreadLocal<String> remoteIpAddress = new ThreadLocal<>();

    public static UserPO getUserInfo() {
        return threadLocal.get();
    }

    public static final Long getTenantId() {
        return threadLocal.get().getTenantId();
    }

    public static final Long getUserId() {
        return threadLocal.get().getUserId();
    }

    public static final Long getDistributorId() {
        return threadLocal.get().getDistributorId();
    }

    /**
     * 设置分布式userId,并且返回sessionId作为token
     */
    public static void setUserInfo(String token, UserPO user, RedisOpsService redisOpsService, SystemEnum system) {
        int tokenTime = ApplicationConstant.TOKEN_TTL_TIME;
        if (SystemEnum.OPE.getCode().equals(system.getCode())) {
            tokenTime = ApplicationConstant.OPE_TOKEN_TTL_TIME;
        }
        threadLocal.set(user);
        // token作为key保存用户信息
        String userLoginInfoRedisKey = RedisKeyCenter.getUserLoginInfoRedisKey(system, token);
        redisOpsService.set(userLoginInfoRedisKey, user, tokenTime, TimeUnit.SECONDS);
        // token放在zset中
        String tokenZsetRedisKey = RedisKeyCenter.getTokenZsetRedisKey(system);
        RedisTemplate redisTemplate = redisOpsService.getRedisTemplate();
        // 使用zset便于按照score删除，score=userId
        redisTemplate.opsForZSet().add(tokenZsetRedisKey, token, user.getUserId());
        redisTemplate.expire(tokenZsetRedisKey, tokenTime, TimeUnit.SECONDS);
        // 用户登录单点的key
        String userLoginTokenRedisKey = RedisKeyCenter.getUserLoginTokenRedisKey(system, user.getUserId());
        redisOpsService.set(userLoginTokenRedisKey, token, tokenTime, TimeUnit.SECONDS);
    }

    public static void flushRemoteIpAddress(String ipAddress) {
        if (StringUtils.isNotBlank(ipAddress)) {
            remoteIpAddress.set(ipAddress);
        }
    }

    public static String getRemoteIpAddress() {
        return remoteIpAddress.get();
    }

    /**
     * 刷新线程中的用户信息数据
     *
     * @param token
     * @param redisOpsService
     */
    public static void flushUserInfo(SystemEnum system, String token, RedisOpsService redisOpsService) {
        if (SystemEnum.AICC.equals(system)) {
            system = SystemEnum.CRM;
        }
        // token作为key保存用户信息
        String userLoginInfoRedisKey = RedisKeyCenter.getUserLoginInfoRedisKey(system, token);
        UserPO userPO = redisOpsService.get(userLoginInfoRedisKey, UserPO.class);
        // token放在zset中,使用zset便于按照score删除，score=userId
        String tokenZsetRedisKey = RedisKeyCenter.getTokenZsetRedisKey(system);
        RedisTemplate redisTemplate = redisOpsService.getRedisTemplate();
        Double score = redisTemplate.opsForZSet().score(tokenZsetRedisKey, token);
        if (Objects.nonNull(score)) {
            threadLocal.set(userPO);
        }
    }

    public static void remove() {
        threadLocal.remove();
    }

    public static void flushDoorOpeUserInfo(String token, RedisOpsService redisOpsService) {
        String doorOpeKey = RedisKeyCenter.getDoorOpeUserIdRedisKey(token);
        String opeUserId = redisOpsService.get(doorOpeKey);
        if (StringUtils.isNotBlank(opeUserId)) {
            redisOpsService.set(doorOpeKey, opeUserId, ApplicationConstant.TOKEN_TTL_TIME, TimeUnit.SECONDS);
            doorForOpeUserIdThreadLocal.set(Long.valueOf(opeUserId));
        } else {
            doorForOpeUserIdThreadLocal.set(null);
        }
    }

    public static Long getDoorOpeUserId() {
        Long doorOpeUserId = doorForOpeUserIdThreadLocal.get();
        return Objects.isNull(doorOpeUserId) ? 0L : doorOpeUserId;
    }

    public static void setOpeUserInfoForDoor(String token, UserPO opeUser, RedisOpsService redisOpsService) {
        redisOpsService.set(RedisKeyCenter.getDoorOpeUserIdRedisKey(token), opeUser.getUserId(), ApplicationConstant.TOKEN_TTL_TIME, TimeUnit.SECONDS);
    }

}
