package com.yiwise.dialogflow.utils;

import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.text.TextPlaceholderElement;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.common.MdcLogIdLifterTransformer;
import com.yiwise.dialogflow.engine.share.DialogQueryNodeHttpParamInfo;
import com.yiwise.dialogflow.engine.share.QueryNodeApiTestReq;
import com.yiwise.dialogflow.engine.share.QueryNodeApiTestResult;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.engine.share.enums.QueryNodeHttpVarTypeEnum;
import io.netty.handler.timeout.ReadTimeoutException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 */
@Slf4j
public class QueryNodeApiTestUtils {

    private static final WebClient WEB_CLIENT = (WebClient) AppContextUtils.getBean("queryNodeWebClient");

    public static Mono<QueryNodeApiTestResult> test(QueryNodeApiTestReq req) {
        long start = System.currentTimeMillis();
        Map<String, String> varIdValueMap = Optional.ofNullable(req.getVarIdValueMap()).orElse(Collections.emptyMap());
        Map<String, String> varNameValueMap = Optional.ofNullable(req.getVarNameValueMap()).orElse(Collections.emptyMap());

        log.info("http req={}", JsonUtils.object2String(req));

        MultiValueMap<String, String> queryMap = buildParamInfo(req.getQueryList(), varIdValueMap);
        log.info("queryMap={}", JsonUtils.object2String(queryMap));

        MultiValueMap<String, String> headerMap = buildParamInfo(req.getHeaderList(), varIdValueMap);
        log.info("headerMap={}", JsonUtils.object2String(headerMap));

        String body = buildBody(req.getBody(), varNameValueMap);
        log.info("body={}", body);

        URI uri = UriComponentsBuilder.fromHttpUrl(req.getUrl())
                .queryParams(queryMap)
                .build()
                .encode()
                .toUri();
        log.info("uri={}", uri);

        return WEB_CLIENT.method(req.getHttpMethod())
                .uri(uri)
                .headers(headers -> headers.addAll(headerMap))
                .body(BodyInserters.fromValue(body))
                .exchange()
                .timeout(Duration.of(req.getTimeout(), ChronoUnit.SECONDS))
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .flatMap(res -> res.bodyToMono(String.class)
                        .switchIfEmpty(Mono.just(""))
                        .doOnNext(responseBody -> log.info("接口响应={}", responseBody))
                        .map(responseBody -> {
                            if (HttpStatus.OK.equals(res.statusCode())) {
                                return mapResponseBody(responseBody, req.getResMap());
                            } else {
                                return QueryNodeApiTestResult.fail(res.statusCode(), responseBody, req.getErrorVarId());
                            }
                        }))
                .onErrorResume(e -> {
                    if (e instanceof ReadTimeoutException || e instanceof TimeoutException) {
                        log.error("[LogHub_Warn]调用接口超时, url={}", uri, e);
                        return Mono.just(QueryNodeApiTestResult.timeout(req.getErrorVarId()));
                    } else {
                        log.error("[LogHub_Warn]调用接口失败, url={}", uri, e);
                    }
                    return Mono.just(QueryNodeApiTestResult.error(e.getMessage(), req.getErrorVarId()));
                })
                .map(res -> res.cost(System.currentTimeMillis() - start))
                .doOnNext(res -> log.info("http res={}", JsonUtils.object2String(res)));
    }

    private static QueryNodeApiTestResult mapResponseBody(String responseBody, Map<String, String> resMap) {
        if (MapUtils.isNotEmpty(resMap) && StringUtils.isNotBlank(responseBody)) {
            Map<String, String> varIdValueMap = new HashMap<>(resMap.size());
            try {
                DocumentContext parsed = JsonPath.parse(responseBody);
                for (Map.Entry<String, String> entry : resMap.entrySet()) {
                    String path = entry.getValue();
                    try {
                        String value = parsed.read(path, String.class);
                        log.info("path={} 提取结果={}", path, value);
                        varIdValueMap.put(entry.getKey(), value);
                    } catch (Exception e) {
                        log.error("[LogHub_Warn]提取信息失败, body={}, 表达式={}", responseBody, path, e);
                    }
                }
            } catch (Exception e) {
                log.error("[LogHub_Warn]提取信息失败, body={}, resMap={}", responseBody, resMap, e);
            }
            return QueryNodeApiTestResult.success(responseBody, varIdValueMap);
        }
        return QueryNodeApiTestResult.success(responseBody, Collections.emptyMap());
    }

    private static String buildBody(String body, Map<String, String> varNameValueMap) {
        if (StringUtils.isBlank(body)) {
            return "";
        }
        AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(body, false);
        StringBuilder sb = new StringBuilder();
        for (TextPlaceholderElement element : splitter.getTextPlaceholderList()) {
            if (TextPlaceholderTypeEnum.TEXT.equals(element.getType())) {
                sb.append(element.getValue());
            } else {
                sb.append(varNameValueMap.getOrDefault(element.getValue(), ""));
            }
        }
        return sb.toString();
    }

    private static MultiValueMap<String, String> buildParamInfo(List<DialogQueryNodeHttpParamInfo> list, Map<String, String> varIdValueMap) {
        MultiValueMap<String, String> resultMap = new LinkedMultiValueMap<>();

        if (CollectionUtils.isEmpty(list)) {
            return resultMap;
        }

        for (DialogQueryNodeHttpParamInfo paramInfo : list) {
            if (QueryNodeHttpVarTypeEnum.isVariable(paramInfo.getVariableType())) {
                resultMap.add(paramInfo.getName(), varIdValueMap.getOrDefault(paramInfo.getValue(), ""));
            }
            if (QueryNodeHttpVarTypeEnum.isConstant(paramInfo.getVariableType())) {
                resultMap.add(paramInfo.getName(), paramInfo.getValue());
            }
        }
        return resultMap;
    }
}
