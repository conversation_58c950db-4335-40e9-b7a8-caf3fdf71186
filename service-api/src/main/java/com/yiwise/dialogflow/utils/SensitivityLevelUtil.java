package com.yiwise.dialogflow.utils;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.enums.SensitivityLevelEnum;

/**
 * 灵敏度等级转换
 *
 * <AUTHOR>
 * @date 2022/11/28 10:37:33
 */
public class SensitivityLevelUtil {

    /**
     * 值转换为等级
     *
     * @param value
     * @param sensitivityLevelEnum
     * @return
     */
    public static int valueToLevel(Integer value, SensitivityLevelEnum sensitivityLevelEnum) {
        int level = 0;
        if (SensitivityLevelEnum.maxSentenceSilence.equals(sensitivityLevelEnum)) {
            if (value < 200 || value > 1000) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "反应灵敏度值非法");
            }
            level = 1 + (value - 200) / 50;
        }
        return level;
    }

    /**
     * 等级转换为值
     *
     * @param level
     * @param sensitivityLevelEnum
     * @return
     */
    public static int levelToValue(Integer level, SensitivityLevelEnum sensitivityLevelEnum) {
        int value = 0;
        if (SensitivityLevelEnum.maxSentenceSilence.equals(sensitivityLevelEnum)) {
            if (level < 1 || level > 17) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "反应灵敏度等级非法");
            }
            value = 200 + (level - 1) * 50;
        }
        return value;
    }
}