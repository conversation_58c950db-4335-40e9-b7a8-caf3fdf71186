import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.vo.BotVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MyCollectionUtilsTest {

    public static void main(String[] args) {

        List<BotPO> botList = new ArrayList<>();
        BotVO bot = new BotVO();
        bot.setBotId(1L);
        bot.setName("name");
        botList.add(bot);

        Map<Long, BotPO> botMap = MyCollectionUtils.listToMap(botList, BotPO::getBotId);

        System.out.println(botMap.size());
    }
}
