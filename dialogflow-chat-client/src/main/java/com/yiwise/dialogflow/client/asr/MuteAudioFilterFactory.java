package com.yiwise.dialogflow.client.asr;

import com.yiwise.dialogflow.engine.share.BotMetaData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Objects;

@Slf4j
public class MuteAudioFilterFactory {

    public static MuteAudioFilter createMuteAudioFilter(BotMetaData botMetaData) {
        MuteAudioFilter muteAudioFilter = null;
        try {
            muteAudioFilter = doCreateMuteAudioFilter(botMetaData);
        } catch (Exception e) {
            log.debug("[LogHub_Warn] create mute audio filter error", e);
        }
        if (muteAudioFilter == null) {
            muteAudioFilter = new NoopMuteAudioFilter();
        }
        log.debug("MuteAudioFilter:{}", muteAudioFilter.getClass().getSimpleName());
        return muteAudioFilter;
    }

    private static MuteAudioFilter doCreateMuteAudioFilter(BotMetaData botMetaData) {
        if (Objects.nonNull(botMetaData) && Objects.nonNull(botMetaData.getBotAsrConfig())) {
            if (BooleanUtils.isTrue(botMetaData.getBotAsrConfig().getEnableAdvancedAsrDelayStart())) {
                return new DefaultMuteAudioFilter(botMetaData);
            }
            if (BooleanUtils.isTrue(botMetaData.getBotAsrConfig().getEnableAsrDelayStart())) {
                return new FixedHeadAudioFilter(botMetaData);
            }
        }
        return new NoopMuteAudioFilter();
    }
}
