package com.yiwise.dialogflow.client.engine;

import com.yiwise.dialogflow.client.listener.KeyCaptureListener;
import com.yiwise.dialogflow.engine.share.model.KeyCaptureConfig;

/**
 * <AUTHOR>
 */
public interface DTMFManager {

    /**
     * 注册按键监听器
     *
     * @param listener 按键监听器
     */
    void registerListener(KeyCaptureListener listener);

    /**
     * 当前是否正在按键采集
     *
     * @return T/F
     */
    boolean isWorking();

    /**
     * 重置按键采集配置
     *
     * @param config 按键配置
     */
    void reset(KeyCaptureConfig config);

    /**
     * 接收按键
     *
     * @param c 用户按键
     */
    void receive(char c);
}
