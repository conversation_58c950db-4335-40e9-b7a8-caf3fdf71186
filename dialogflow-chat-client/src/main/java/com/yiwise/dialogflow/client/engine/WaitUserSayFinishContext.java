package com.yiwise.dialogflow.client.engine;

import com.yiwise.base.common.utils.bean.MyBeanUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 等待用户说完栈, 如果userSilenceConfig.waitUserSayFinish为true, 则需要等待用户说完
 * 在用户输入完成后, 需要把前面的输入合并发送给服务端
 */
@Slf4j
public class WaitUserSayFinishContext {

    volatile UserSayInfo preUserSayInfo;
    /**
     * 将多个用户输入合并成一个
     * @return 合并后的用户输入
     */
    UserSayInfo merge(UserSayInfo userSayInfo) {
        log.info("WaitUserSayFinishContext.merge, preUserSayInfo={}, userSayInfo={}", preUserSayInfo, userSayInfo);
        if (preUserSayInfo == null) {
            preUserSayInfo = MyBeanUtils.copy(userSayInfo, UserSayInfo.class);
            return preUserSayInfo;
        }

        if (userSayInfo == preUserSayInfo) {
            return preUserSayInfo;
        }

        // 当断句补齐和等待用户输入同时触发时
        // preUserSayInfo已经被合并到 userSayInfo 中了, 那么后面又再次将 userSayInfo 合并到 preUserSayInfo 中
        // 导致出现循环引用
        // 所以在断句补齐时, 同时也需要请求 reset() 清空 preUserSayInfo
        preUserSayInfo.text = preUserSayInfo.text + ", " +  userSayInfo.originText;
        preUserSayInfo.originText = preUserSayInfo.originText + ", " +  userSayInfo.originText;
        preUserSayInfo.endTime = userSayInfo.endTime;
        preUserSayInfo.originInfoList.add(userSayInfo);
        log.info("合并用户输入, preUserSayInfo={}", preUserSayInfo);
        return preUserSayInfo;
    }

    void reset() {
        preUserSayInfo = null;
    }
}
