package com.yiwise.dialogflow.client.config;


import com.netflix.loadbalancer.IRule;
import com.netflix.loadbalancer.WeightedResponseTimeRule;
import com.yiwise.dialogflow.client.slb.ChatServerSelectRule;
import feign.Request;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 */
@Slf4j
public class RemoteChatRibbonConfig {

    @Value("${service.feign.connectTimeout:30000}")
    private int connectTimeout;

    @Value("${service.feign.readTimeOut:90000}")
    private int readTimeout;

    @Bean
    public Request.Options options() {
        log.info("connectTimeout={}, readTimeout={}", connectTimeout, readTimeout);
        return new Request.Options(connectTimeout, readTimeout, true);
    }

    @Bean
    public IRule loadBalancer() {
        WeightedResponseTimeRule parentRule = new WeightedResponseTimeRule();
        return new ChatServerSelectRule(parentRule);
    }
}

