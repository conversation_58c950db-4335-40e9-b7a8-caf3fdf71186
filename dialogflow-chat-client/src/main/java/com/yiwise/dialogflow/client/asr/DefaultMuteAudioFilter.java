package com.yiwise.dialogflow.client.asr;

import com.yiwise.base.common.audio.vad.VadProcessor;
import com.yiwise.base.common.audio.vad.VadProcessorCallback;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.yiwise.dialogflow.client.config.DialogEngineConstant.PER_PACKAGE_DURATION_MS;

/**
 * 按 vad 过滤掉静音数据
 */
@Slf4j
public class DefaultMuteAudioFilter extends AbstractAudioFilter implements VadProcessorCallback {

    private static final int BUFFER_SIZE = 160;

    /**
     * 最大连续跳过帧数
     */
    private static final int MAX_CONTINUOUS_SKIP_FRAME_COUNT = 50 * 2;

    /**
     * 保活时, 发送的数据帧数
     */
    private static final int KEEP_ALIVE_FRAME_COUNT = 10;

    /**
     * vad 检测到用户开始说话时, 需要把之前的几帧数据一并发送
     * 这个主要是 vad 的参数决定的
     */
    private static final int INSERT_FRAME_COUNT_ON_SAYING = 10;

    /**
     * 每次 vad 检测到用户说完后, 追加的帧数, 用来 asr 断开两句话之间的间隔
     */
    private static final int APPEND_FRAME_COUNT_ON_SAY_FINISH = 1 * 40;

    private final VadProcessor vad ;

    private final List<BufferItem> ringBuffer = new ArrayList<>(BUFFER_SIZE);

    private final AtomicInteger ringBufferIndex = new AtomicInteger(0);

    private volatile Status status = Status.DETECTING;

    private volatile int statusChangeReceivedFrameCount;

    protected final AtomicInteger totalReceivedFrameCount = new AtomicInteger(0);

    private volatile int lastSendFrameIndex;

    private List<AudioVoiceInfo> audioVoiceInfoList = new ArrayList<>();

    private volatile AudioVoiceInfo currentAudioVoiceInfo = null;

    public DefaultMuteAudioFilter(BotMetaData botMetaData) {
        vad = new VadProcessor(this);
        vad.setMutePointValueGate(500);
        vad.setMuteGateFrameCount(5);
        vad.setMaxRecordTime(300 * 1000);
        vad.setMaxStallTime(200);
        vad.setVoiceCountGate(2);
        for (int i = 0; i < BUFFER_SIZE; i++) {
            ringBuffer.add(new BufferItem());
        }
    }


    @Override
    public void onVoiceStart() {
        log.debug("检测到开始说话");
        updateStatus(Status.SAYING);
    }

    @Override
    public void onVoiceEnd(long l) {
        log.debug("检测到说话结束");
        updateStatus(Status.JUST_SAY_FINISH);
    }

    void updateStatus(Status nextStatus) {
        Status preStatus = status;
        status = nextStatus;
        statusChangeReceivedFrameCount = totalReceivedFrameCount.get();
        log.debug("updateStatus, preStatus:{}, status:{}, statusChangeReceivedFrameCount:{}", preStatus, status, statusChangeReceivedFrameCount);
    }

    @Override
    public List<FilterResult> filter(byte[] bytes, int offset, int length) {
        byte[] copy = new byte[length];
        System.arraycopy(bytes, offset, copy, 0, length);
        try {
            int index = totalReceivedFrameCount.get();
            List<BufferItem> result = doFilter(copy, offset, length);
            if (CollectionUtils.isNotEmpty(result)) {
                lastSendFrameIndex = totalReceivedFrameCount.get();
            }
            // 记录跳过的数据信息
            // 没有跳过的数据
            if (CollectionUtils.isNotEmpty(result)) {
                if (currentAudioVoiceInfo == null || currentAudioVoiceInfo.isSkip()) {
                    currentAudioVoiceInfo = new AudioVoiceInfo();
                    currentAudioVoiceInfo.setSkip(false);
                    currentAudioVoiceInfo.setStartTime(result.get(0).index * PER_PACKAGE_DURATION_MS);
                    currentAudioVoiceInfo.setEndTime(result.get(result.size() - 1).index * PER_PACKAGE_DURATION_MS + PER_PACKAGE_DURATION_MS);
                    currentAudioVoiceInfo.setPreSkipTime(calculateTotalSkipDuration()) ;
                    audioVoiceInfoList.add(currentAudioVoiceInfo);
                } else {
                    // 仅更新结束时间
                    currentAudioVoiceInfo.setEndTime(result.get(result.size() - 1).index * PER_PACKAGE_DURATION_MS + PER_PACKAGE_DURATION_MS);
                }
            } else {
                // 跳过音频
                if (currentAudioVoiceInfo == null || !currentAudioVoiceInfo.isSkip()) {
                    currentAudioVoiceInfo = new AudioVoiceInfo();
                    currentAudioVoiceInfo.setSkip(true);
                    currentAudioVoiceInfo.setEndTime((index + 1) * PER_PACKAGE_DURATION_MS);
                    currentAudioVoiceInfo.setStartTime(index * PER_PACKAGE_DURATION_MS);
                } else {
                    currentAudioVoiceInfo.setEndTime((index + 1) * PER_PACKAGE_DURATION_MS);
                }
            }

            return result.stream()
                    .map(BufferItem::convertToFilterResult)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("[LogHub_Warn] asr 静音数据过滤异常", e);
        }
        FilterResult result = new FilterResult();
        result.setData(copy);
        result.setOffset(0);
        result.setLength(copy.length);
        return Collections.singletonList(result);
    }

    private Optional<AudioVoiceInfo> getPreMuteAudioInfo() {
        if (audioVoiceInfoList.isEmpty()) {
            return Optional.empty();
        }
        return Optional.of(audioVoiceInfoList.get(audioVoiceInfoList.size() - 1));
    }

    protected List<BufferItem> doFilter(byte[] bytes, int offset, int length) {
        int index = totalReceivedFrameCount.getAndIncrement();
        // 先把数据放入 buffer 中
        int addIndex = ringBufferIndex.getAndIncrement() % BUFFER_SIZE;
        BufferItem bufferItem = ringBuffer.get(addIndex);
        // 复制 bytes
        bufferItem.bytes = bytes;
        bufferItem.offset = offset;
        bufferItem.length = length;
        bufferItem.index = index;

        // 判断状态
        Status preStatus = status;

        vadUpdate(bytes, offset, length);

        int currentStatusDurationFrame = totalReceivedFrameCount.get() - statusChangeReceivedFrameCount;
        // 判断状态是否变了
        switch (status) {
            case SAYING:
                if (preStatus == Status.DETECTING) {
                    // 因为 vad 检测有延迟性, 所以检测到永恒说话时, 需要把之前的几帧数据一并发送
                    List<BufferItem> result = new ArrayList<>();

                    // 向前找 N 帧数据, 需要判断是否有这么多帧数据
                    // 1. 距离上次发送的帧数, 过了多久
                    // 2. ring buffer 中有多少数据
                    int preSkipFrameCount = totalReceivedFrameCount.get() - lastSendFrameIndex;
                    int bufferStartIndex = Math.max(0, ringBufferIndex.get() - INSERT_FRAME_COUNT_ON_SAYING);
                    int bufferContainsCount = ringBufferIndex.get() - bufferStartIndex;
                    int insertCount = Math.min(preSkipFrameCount, bufferContainsCount);
                    int startIndex = ringBufferIndex.get() - insertCount;
                    for (int i = 0; i < insertCount; i++) {
                        BufferItem item = ringBuffer.get((i + startIndex) % BUFFER_SIZE);
                        result.add(item);
                    }
                    return result;
                }
                return Collections.singletonList(new BufferItem(index, bytes, offset, length));
            case KEEP_ALIVE:
                if (currentStatusDurationFrame >= KEEP_ALIVE_FRAME_COUNT) {
                    // 切换到检测状态
                    updateStatus(Status.DETECTING);
                }
                return Collections.singletonList(new BufferItem(index, bytes, offset, length));
            case JUST_SAY_FINISH:
                if (currentStatusDurationFrame >= APPEND_FRAME_COUNT_ON_SAY_FINISH) {
                    // 切换到检测状态
                    updateStatus(Status.DETECTING);
                }
                return Collections.singletonList(new BufferItem(index, bytes, offset, length));
            case DETECTING:
            default:
                if (currentStatusDurationFrame >= MAX_CONTINUOUS_SKIP_FRAME_COUNT) {
                    // 切换到保活状态
                    updateStatus(Status.KEEP_ALIVE);
                }
                // 判断上次切换状态距离现在有多少数据发送了,
                return Collections.emptyList();
        }
    }

    protected void vadUpdate(byte[] bytes, int offset, int length) {
        ShortArrayCache shortArrayCache = null;
        try {
            shortArrayCache = ShortArrayCache.getAvailable();
            short[] shorts =  bytes2shorts(bytes, offset, length, shortArrayCache.buffer);
            vad.update(shorts);
        } finally {
            if (shortArrayCache != null) {
                shortArrayCache.release();
            }
        }
    }

    @Override
    public List<AudioVoiceInfo> getFilterAudioInfo() {
        log.debug("getFilterAudioInfo, audioVoiceInfoList:{}", audioVoiceInfoList);
        return audioVoiceInfoList;
    }

    @Override
    public int getTotalSkipDuration() {
        if (CollectionUtils.isEmpty(audioVoiceInfoList)) {
            return 0;
        }
        if (currentAudioVoiceInfo != null && !currentAudioVoiceInfo.isSkip()) {
            return currentAudioVoiceInfo.getPreSkipTime();
        }
        return calculateTotalSkipDuration();
    }

    private int calculateTotalSkipDuration() {
        Optional<AudioVoiceInfo> preInfo = getPreMuteAudioInfo();
        int preSkipTime = 0;
        int preEndTime = 0;
        if (preInfo.isPresent()) {
            preSkipTime = preInfo.get().getPreSkipTime();
            preEndTime = preInfo.get().getEndTime();
            log.debug("preAudioVoiceInfo:{}", preInfo);
        }
        int currentTime = 0;
        if (currentAudioVoiceInfo != null) {
            if (currentAudioVoiceInfo.isSkip()) {
                currentTime = currentAudioVoiceInfo.endTime;
            } else {
                currentTime = currentAudioVoiceInfo.startTime;
            }
        }
        log.debug("preSkipTime:{}, preEndTime:{}, currentTime:{}, currentAudioVoiceInfo:{}", preSkipTime, preEndTime, currentTime, currentAudioVoiceInfo);
        return preSkipTime + (currentTime - preEndTime);
    }

    protected int doConvert(int sendTime) {
        // 反向遍历
        for (int i = audioVoiceInfoList.size() - 1; i >= 0; i--) {
            AudioVoiceInfo audioVoiceInfo = audioVoiceInfoList.get(i);
            if (sendTime >= audioVoiceInfo.getSendBegin() && sendTime <= audioVoiceInfo.getSendEnd()) {
                return sendTime + audioVoiceInfo.getPreSkipTime();
            }
        }
        return sendTime;
    }

    @Data
    public static class BufferItem {
        byte[] bytes;
        int offset;
        int length;
        int index;

        public BufferItem() {

        }
        public BufferItem(int index, byte[] bytes, int offset, int length) {
            this.bytes = bytes;
            this.index = index;
            this.offset = offset;
            this.length = length;
        }

        public FilterResult convertToFilterResult() {
            FilterResult result = new FilterResult();
            result.setData(bytes);
            result.setOffset(offset);
            result.setLength(length);
            return result;
        }
    }

    enum Status {
        /**
         * 正在检测中, 这个时候把数据加入 ring buffer, 然后过滤掉
         */
        DETECTING,

        /**
         * 正在说话, 这个时候不需要过滤
         */
        SAYING,

        /**
         * 刚刚说完, 这个时候需要在后面追加一定时长的音频
         */
        JUST_SAY_FINISH,

        /**
         * 保活中, 这个是为了防止过长时间不向 asr 发送数据, 会导致连接断开
         */
        KEEP_ALIVE,
    }

    public static class ShortArrayCache {
        short[] buffer = new short[160];
        volatile AtomicBoolean isUsed = new AtomicBoolean(false);


        private static final AtomicLong index = new AtomicLong(0);
        private static final ShortArrayCache[] CACHES = new ShortArrayCache[1000];

        static {
            for (int i = 0; i < 1000; i++) {
                CACHES[i] = new ShortArrayCache();
            }
        }

        public static ShortArrayCache getAvailable() {
            for (int c = 0; c < 100; c++) {
                ShortArrayCache cache = CACHES[(int) (index.getAndIncrement() % 1000)];
                if (cache.isUsed.compareAndSet(false, true)) {
                    return cache;
                }
            }
            return new ShortArrayCache();
        }

        public void release() {
            isUsed.set(false);
        }
    }

    public static short[] bytes2shorts(byte[] bytes, int offset, int length, short[] shorts) {
        int start = offset;
        int end = offset + length;
        if (length > 320) {
            shorts = new short[(length + 1) / 2];
            log.debug("length 不符合预期, offset:{}, length:{}", offset, length);
        }
        for(int i = offset; i < end; i += 2) {
            shorts[i - start >> 1] = (short)((bytes[i + 1] & 255) << 8 | 255 & bytes[i]);
        }
        return shorts;
    }
}
