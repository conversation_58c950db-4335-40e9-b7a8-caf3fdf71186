package com.yiwise.dialogflow.client.config;

import feign.Logger;
import feign.Request;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.codec.Decoder;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

@Slf4j
public class DefaultApiWebRibbonConfig implements RequestInterceptor {

    @Value("${service.feign.connectTimeout:30000}")
    private int connectTimeout;

    @Value("${service.feign.readTimeOut:15000}")
    private int readTimeout;

    public DefaultApiWebRibbonConfig() {
        log.info("init DefaultApiWebRibbonConfig");
    }

    @Bean
    public Request.Options options() {
        log.info("connectTimeout={}, readTimeout={}", connectTimeout, readTimeout);
        return new Request.Options(connectTimeout, readTimeout, true);
    }

    @Bean
    public Decoder feignDecoder() {
        return new FeignResultDecoder();
    }

    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    @Override
    public void apply(RequestTemplate requestTemplate) {
        String traceId = MDC.get("MDC_LOG_ID");
        log.info("CustomizedConfiguration openFeign traceId={}", traceId);
        requestTemplate.header("MDC_LOG_ID", traceId);
        // MDCDecoratedServlet 种获取的是X-Request-Id, 这里先兼容下
        requestTemplate.header("X-Request-Id", traceId);
    }

}
