package com.yiwise.dialogflow.client.engine;

import com.yiwise.dialogflow.client.listener.KeyCaptureListener;
import com.yiwise.dialogflow.engine.share.enums.KeyCaptureCommitModeEnum;
import com.yiwise.dialogflow.engine.share.model.KeyCaptureConfig;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractDTMFManager implements DTMFManager {

    /**
     * 监听器列表
     */
    private final List<KeyCaptureListener> listeners;

    /**
     * 按键采集配置
     */
    @Getter
    private volatile KeyCaptureConfig config;

    /**
     * 是否暂停按键采集
     */
    private volatile Boolean stopped;

    /**
     * 已经收集到的按键列表
     */
    private final List<Character> charList;

    private final Lock lock;

    private static final char HASH_KEY = '#';
    private static final char REPLAY_KEY = '*';

    public AbstractDTMFManager() {
        this.listeners = new ArrayList<>();
        this.charList = new ArrayList<>();
        this.lock = new ReentrantLock();
        this.stopped = true;
    }

    void lock(Runnable runnable) {
        lock.lock();
        try {
            runnable.run();
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void registerListener(KeyCaptureListener listener) {
        listeners.add(listener);
    }

    @Override
    public boolean isWorking() {
        return Objects.nonNull(config);
    }

    boolean isStopped() {
        return BooleanUtils.isTrue(stopped);
    }

    private void stop() {
        log.info("暂停按键采集");
        this.stopped = true;
    }

    void doOnFailed() {
        listeners.forEach(KeyCaptureListener::onFailed);
        stop();
    }

    public void reset(KeyCaptureConfig config) {
        lock(() -> doReset(config));
    }

    protected void doReset(KeyCaptureConfig config) {
        if (Objects.nonNull(config)) {
            log.info("开启按键采集, config={}", config);
            this.stopped = false;
        } else {
            this.stopped = true;
        }
        this.config = config;
        this.charList.clear();
    }

    public void receive(char c) {
        log.info("用户按键【{}】", c);
        lock(() -> {
            if (isStopped()) {
                log.debug("当前未进行按键采集,忽略用户按键");
                return;
            }

            // 按键重播
            if (BooleanUtils.isTrue(config.getEnableReplay()) && c == REPLAY_KEY) {
                log.info("用户按了【*】号键,准备重播当前答案");
                listeners.forEach(KeyCaptureListener::onReplay);
                return;
            }

            String result;
            if (KeyCaptureCommitModeEnum.isAny(config.getCommitMode())) {
                result = String.valueOf(c);
            } else if (c == HASH_KEY) {
                // 兼容身份证
                result = charList.stream().map(String::valueOf).collect(Collectors.joining()).replace("*", "X");
            } else {
                charList.add(c);
                result = null;
            }

            if (result != null) {
                log.info("按键收集成功,result=【{}】", result);
                listeners.forEach(listener -> listener.onSuccess(result));
                stop();
            }
        });
    }
}
