package com.yiwise.dialogflow.client.engine;

import com.yiwise.dialogflow.client.listener.AudioPlayEventListener;
import com.yiwise.dialogflow.engine.share.model.KeyCaptureConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 **/
@Slf4j
public class DefaultDTMFManager extends AbstractDTMFManager implements AudioPlayEventListener {

    /**
     * AI播报完成的时间，如果当前正在播报，该字段为null
     */
    private volatile Long aiPlayEndTime;

    public DefaultDTMFManager() {
        super();
    }

    @Override
    public void onReadData() {
        lock(() -> {
            long cur;
            long cost;
            KeyCaptureConfig config = getConfig();
            if (!isStopped() && Objects.nonNull(aiPlayEndTime) && (cost = (cur = System.currentTimeMillis()) - aiPlayEndTime) > TimeUnit.SECONDS.toMillis(config.getTimeout())) {
                log.info("按键采集超时, 当前时间={}ms, ai播报完成时间={}ms, cost={}ms, 超时时间={}s", cur, aiPlayEndTime, cost, config.getTimeout());
                doOnFailed();
            }
        });
    }

    @Override
    protected void doReset(KeyCaptureConfig config) {
        super.doReset(config);
        aiPlayEndTime = null;
    }

    @Override
    public void onAiPlayBegin(int index) {
        aiPlayEndTime = null;
    }

    @Override
    public void onAiPlayEnd(int index) {
        aiPlayEndTime = System.currentTimeMillis();
    }

    @Override
    public void onPause(int index) {

    }

    @Override
    public void onResume(int index) {

    }


}
