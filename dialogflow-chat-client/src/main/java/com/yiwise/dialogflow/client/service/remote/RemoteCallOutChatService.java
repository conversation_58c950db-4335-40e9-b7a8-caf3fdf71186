package com.yiwise.dialogflow.client.service.remote;

import com.yiwise.dialogflow.client.config.RemoteChatRibbonConfig;
import com.yiwise.dialogflow.engine.share.service.RemoteChatService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/1/28
 */
@FeignClient(name = "aicc-platform-dialogflow-chat-web", configuration = RemoteChatRibbonConfig.class)
public interface RemoteCallOutChatService extends RemoteChatService {


}
