package com.yiwise.dialogflow.client.model.asr;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 * @date 2022/10/25
 */
public enum AsrSlefLearningTrainStatusEnum implements CodeDescEnum {
    NOT_TRAIN(0, "未训练"),
    SUCCESS(1, "训练成功"),
    FAILD(2, "训练失败"),
    ONLINE(3, "已上线"),
    OFFLINE(4, "已下线");

    Integer code;
    String desc;

    AsrSlefLearningTrainStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
