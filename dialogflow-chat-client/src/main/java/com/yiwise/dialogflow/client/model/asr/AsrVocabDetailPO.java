package com.yiwise.dialogflow.client.model.asr;


import com.yiwise.base.model.bean.po.BaseTimePO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import java.io.Serializable;
import java.util.List;

/**
 * asr热词组信息表
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrVocabDetailPO extends BaseTimePO implements Serializable {

    Long asrVocabId;

    /**
     * 热词组名称
     */
    String name;

    /**
     * 描述
     */
    String description;

    /**
     * 热词组内容
     */
    List<String> content;

    /**
     * 开启状态（0-停用；1-开启）
     */
    Integer status;

}
