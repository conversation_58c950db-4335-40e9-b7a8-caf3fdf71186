package com.yiwise.dialogflow.client.engine;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.client.listener.AudioPlayEventListener;
import com.yiwise.dialogflow.client.listener.CallDetailListener;
import com.yiwise.dialogflow.client.model.engine.SimpleAiSayResultInfo;
import com.yiwise.dialogflow.client.model.engine.SimpleUserSayResultInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

@Slf4j
class CallDetailCallbackAdaptor implements AudioPlayEventListener, CallDetailListener {

    final Supplier<Long> startTimestampSupplier;
    final Supplier<List<CallDetailListener>> callDetailListenerListSupplier;
    volatile List<Object> recordList = new ArrayList<>();

    private volatile long startPlayTimestamp = 0;

    /**
     * 累计读取的音频时长, 单位毫秒, 读取一次累积 20mS
     */
    private volatile long readAudioTimeMs = 0L;

    private volatile int lastIndex;

    private Lock lock = new ReentrantLock();

    CallDetailCallbackAdaptor(Supplier<Long> startTimestampSupplier, Supplier<List<CallDetailListener>> callDetailListenerListSupplier) {
        this.startTimestampSupplier = startTimestampSupplier;
        this.callDetailListenerListSupplier = callDetailListenerListSupplier;
    }

    @Override
    public void createAiSayRecord(SimpleAiSayResultInfo simpleAiSayResultInfo) {
        lock.lock();
        try {
            recordList.add(simpleAiSayResultInfo);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void createUserSayRecord(SimpleUserSayResultInfo simpleUserSayInfo) {
        lock.lock();
        try {
            recordList.add(simpleUserSayInfo);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void onReadData() {
        if (startPlayTimestamp == 0) {
            startPlayTimestamp = System.currentTimeMillis();
        }
        readAudioTimeMs += 20;
    }

    private void updateAiPlayBeginOffset(int index) {
        lock.lock();
        try {
            if (CollectionUtils.isNotEmpty(recordList)) {
                for (Object record : recordList) {
                    if (record instanceof SimpleAiSayResultInfo) {
                        SimpleAiSayResultInfo simpleAiSayResultInfo = (SimpleAiSayResultInfo) record;
                        if (index == simpleAiSayResultInfo.getSequence()) {
                            simpleAiSayResultInfo.setBeginTime(getTimeOffset());
                            log.debug("update aiBeginTime:{}", simpleAiSayResultInfo);
                        }
                    }
                }
            }
        } finally {
            lock.unlock();
        }

    }

    private void updateAiPlayEndOffset(int index) {
        lock.lock();
        try {
            if (CollectionUtils.isNotEmpty(recordList)) {
                for (Object record : recordList) {
                    if (record instanceof SimpleAiSayResultInfo) {
                        SimpleAiSayResultInfo simpleAiSayResultInfo = (SimpleAiSayResultInfo) record;
                        if (index == simpleAiSayResultInfo.getSequence()) {
                            simpleAiSayResultInfo.setEndTime(getTimeOffset());
                            log.debug("update aiBeginTime:{}", simpleAiSayResultInfo);
                        }
                    }
                }
            }
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void onAiPlayBegin(int index) {
        log.debug("onAiPlayBegin:{}", index);
        lastIndex = index;
        updateAiPlayBeginOffset(index);
        submitCallback(false);
    }

    @Override
    public void onAiPlayEnd(int index) {
        log.debug("onAiPlayEnd:{}", index);
        updateAiPlayEndOffset(index);
        submitCallback(true);
    }

    @Override
    public void onPause(int index) {
        log.debug("onPause:{}", index);
        updateAiPlayEndOffset(index);
    }

    @Override
    public void onResume(int index) {

    }

    private long getTimeOffset() {
        long start = 0 == startPlayTimestamp ? System.currentTimeMillis() : startPlayTimestamp;
        long delay = start - startTimestampSupplier.get();
        return delay + readAudioTimeMs;
    }

    public void submitCallback(boolean callbackAll) {
        lock.lock();
        try {
            if (CollectionUtils.isNotEmpty(recordList)) {
                log.debug("回调联系历史, recordList:{}", JsonUtils.object2String(recordList));
                List<Object> tmpList = new ArrayList<>();
                boolean stop = false;
                for (Object record : recordList) {
                    if (stop) {
                        tmpList.add(record);
                    } else {
                        if (record instanceof SimpleAiSayResultInfo) {
                            SimpleAiSayResultInfo simpleAiSayResultInfo = (SimpleAiSayResultInfo) record;
                            if (!callbackAll && simpleAiSayResultInfo.getSequence() >= lastIndex) {
                                stop = true;
                                tmpList.add(record);
                                continue;
                            }
                            if (CollectionUtils.isNotEmpty(callDetailListenerListSupplier.get())) {
                                for (CallDetailListener callDetailListener : callDetailListenerListSupplier.get()) {
                                    try {
                                        callDetailListener.createAiSayRecord(simpleAiSayResultInfo);
                                    } catch (Exception e) {
                                        log.warn("[LogHub_Warn] 回调联系历史处理失败", e);
                                    }
                                }
                            }
                        } else if (record instanceof SimpleUserSayResultInfo) {
                            SimpleUserSayResultInfo simpleUserSayResultInfo = (SimpleUserSayResultInfo) record;
                            for (CallDetailListener callDetailListener : callDetailListenerListSupplier.get()) {
                                try {
                                    callDetailListener.createUserSayRecord(simpleUserSayResultInfo);
                                } catch (Exception e) {
                                    log.warn("[LogHub_Warn] 回调联系历史处理失败", e);
                                }
                            }
                        }
                    }
                }
                recordList = tmpList;
            }
        } finally {
            lock.unlock();
        }
    }
}
