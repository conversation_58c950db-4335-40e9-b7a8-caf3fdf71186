package com.yiwise.dialogflow.client.engine;

import com.yiwise.dialogflow.engine.share.BotMetaData;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;

public class AudioPlayManagerFactory {

    public static AudioPlayManager createAudioPlayManager(BotMetaData botMetaData, AudioManager audioManager) {
        if (RobotSnapshotUsageTargetEnum.TEXT_TEST.equals(botMetaData.getUsageTarget())) {
            return new TextTestAudioPlayManager();
        }
        if (botMetaData.isEnableFragmentAudioPlayer()) {
            return new FragmentAudioPlayManagerV2((FragmentAudioManager) audioManager);
        }
        return new DefaultAudioPlayManager(audioManager);
    }
}
