package com.yiwise.dialogflow.client.service;

import com.yiwise.dialogflow.engine.share.BotAudioConfig;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import com.yiwise.dialogflow.engine.share.MagicActivityConfig;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.model.BotAsrConfig;

public interface BotMetaDataService {
    Integer getLastVersion(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget);

    BotMetaData getBotMetaData(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget, Integer version);

    BotAsrConfig getBotRealAsrConfig(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version);

    /**
     * 获取轻量化模板的音频配置
     */
    MagicActivityConfig getMagicActivityConfig(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version, Long callJobId);
}
