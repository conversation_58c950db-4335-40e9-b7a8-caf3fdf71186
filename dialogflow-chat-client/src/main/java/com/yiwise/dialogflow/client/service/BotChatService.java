package com.yiwise.dialogflow.client.service;

import com.yiwise.dialogflow.engine.share.*;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.request.ChatRequest;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import reactor.core.publisher.Flux;

import java.util.Map;
import java.util.Optional;

public interface BotChatService {


    Optional<SessionInfo> createSessionWithRetry(Long botId,
                                                 Object callTaskId,
                                                 Integer version,
                                                 RobotSnapshotUsageTargetEnum target,
                                                 ChatMetaData chatMetaData,
                                                 int retryTimes);

    ChatResponse sendEvent(ChatRequest request, Object callTaskId, RobotSnapshotUsageTargetEnum usageTarget);

    Flux<ChatResponse> asyncSendEvent(ChatRequest request, Object callTaskId, RobotSnapshotUsageTargetEnum usageTarget);

    void finish();

    IntentLevelAnalysisResult intentLevelAnalysis(Object callTaskId, RobotSnapshotUsageTargetEnum usageTarget, CallDataInfo callDataInfo);

    IntentLevelAnalysisResult intentLevelAnalysisInCall(Object callTaskId, RobotSnapshotUsageTargetEnum usageTarget, CallDataInfo callDataInfo);

    Map<String, String> httpRequest(QueryNodeApiTestReq req, Object callTaskId, RobotSnapshotUsageTargetEnum usageTarget);
}
