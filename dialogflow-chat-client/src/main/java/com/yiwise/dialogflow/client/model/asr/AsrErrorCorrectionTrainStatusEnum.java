package com.yiwise.dialogflow.client.model.asr;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 * @date 2022/10/14 15:57:04
 */
public enum AsrErrorCorrectionTrainStatusEnum implements CodeDescEnum {
    TRAINED(0, "已训练"),
    TRAINING(1, "训练中"),
    NOT_TRAIN(2, "未训练");
    Integer code;
    String desc;

    AsrErrorCorrectionTrainStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
