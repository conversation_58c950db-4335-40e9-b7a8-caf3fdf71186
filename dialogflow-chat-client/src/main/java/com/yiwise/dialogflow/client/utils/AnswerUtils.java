package com.yiwise.dialogflow.client.utils;

import com.yiwise.base.common.text.TextPlaceholderElement;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class AnswerUtils {

    public static String replacePlaceholder(String sentence, Map<String, String> properties) {
        AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(sentence, false);
        StringBuilder sb = new StringBuilder();
        for (TextPlaceholderElement element : splitter.getTextPlaceholderList()) {
            if (TextPlaceholderTypeEnum.TEXT.equals(element.getType())) {
                sb.append(element.getValue());
            } else {
                sb.append(properties.getOrDefault(element.getValue(), ""));
            }
        }
        return sb.toString();
    }
}