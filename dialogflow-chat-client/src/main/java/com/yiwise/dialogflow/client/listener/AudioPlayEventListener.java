package com.yiwise.dialogflow.client.listener;

public interface AudioPlayEventListener {
    /**
     * 每次读取音频数据时, 都会调用
     * 注意在 io 线程内调用, 勿执行 io 操作
     */
    void onReadData();

    /**
     * 开始播放新的答案时, 调用
     */
    void onAiPlayBegin(int index);

    /**
     * 音频播放完成时调用
     */
    void onAiPlayEnd(int index);

    /**
     * 音频暂停播放时的回调, 或者未播放完成时, 切换播放新的答案, 也会先调用 onPause, 之后调用 onAiPlayBegin
     */
    void onPause(int index);

    void onResume(int index);

    default void updatePlayAudioTime(int index) {

    }
    /**
     * 等待后续答案超时时, 调用
     * 目前是大模型对话, 等待后续生成答案的超时
     */
    default void onWaitingTimeout() {

    }
}
