package com.yiwise.dialogflow.client.engine;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalNotification;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
public class AudioFileReaderFactory {

    private static final Cache<String, FragmentAudioPlayManagerV2.AudioFileReader> cache =
            CacheBuilder.newBuilder()
                    .maximumSize(1000)
                    .expireAfterWrite(10, TimeUnit.MINUTES)
                    .removalListener(AudioFileReaderFactory::onRemoval)
                    .build();

    private static final Lock lock = new ReentrantLock();

    private static <K, V> void  onRemoval(RemovalNotification<K, V> notification) {
        log.debug("音频文件缓存移除：{} ", notification.getKey());
        if (notification.getValue() instanceof FragmentAudioPlayManagerV2.AudioFileReader) {
            try {
                FragmentAudioPlayManagerV2.AudioFileReader reader = (FragmentAudioPlayManagerV2.AudioFileReader) notification.getValue();
                reader.close();
                log.debug("音频文件关闭成功：{} {} {}", reader.hitCount, reader.createTime, reader.filePath);
            } catch (Exception e) {
                log.error("音频文件关闭失败：{}", notification.getKey(), e);
            }
        }
    }

    public static FragmentAudioPlayManagerV2.AudioFileReader getReader(String filePath, int start, int end) throws IOException {
        FragmentAudioPlayManagerV2.AudioFileReader reader = cache.getIfPresent(filePath);
        if (reader == null || reader.closed) {
            lock.lock();
            try {
                reader = cache.getIfPresent(filePath);
                if (reader == null || reader.closed) {
                    reader = createReader(filePath, start, end);
                    cache.put(filePath, reader);
                }
            } finally {
                lock.unlock();
            }
        }
        return reader;
    }

    private static FragmentAudioPlayManagerV2.AudioFileReader createReader(String filePath, int start, int end) throws IOException {
        return new FragmentAudioPlayManagerV2.AudioFileReader(filePath, start, end);
    }

    public static void main(String[] args) throws IOException {
        FragmentAudioPlayManagerV2.AudioFileReader reader = getReader("/Users/<USER>/Downloads/1121.wav", 44, 10000);
        byte[] buffer1 = new byte[640];
        byte[] buffer2 = new byte[640];
        byte[] buffer3 = new byte[640];
        int readSize = reader.read(buffer1, 10, 320);
        readSize = reader.read(buffer2, 10, 320);
        readSize = reader.read(buffer3, 10, 320);
        System.out.println(readSize);
    }
}
