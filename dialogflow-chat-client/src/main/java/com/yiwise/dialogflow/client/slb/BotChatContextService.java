package com.yiwise.dialogflow.client.slb;

public interface BotChatContextService {
    ThreadLocal<BotChatServerSelectContext> chatServerSelectContext = new ThreadLocal<>();

    static void setChatServerSelectContext(BotChatServerSelectContext context) {
        chatServerSelectContext.set(context);
    }

    static BotChatServerSelectContext getChatServerSelectContext() {
        return chatServerSelectContext.get();
    }

    static void clearChatServerSelectContext() {
        chatServerSelectContext.remove();
    }
}
