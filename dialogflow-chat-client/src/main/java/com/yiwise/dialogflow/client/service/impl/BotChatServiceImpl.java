package com.yiwise.dialogflow.client.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yiwise.base.common.helper.ServerInfoConstants;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.client.service.BotChatService;
import com.yiwise.dialogflow.client.service.remote.RemoteCallOutChatService;
import com.yiwise.dialogflow.client.service.remote.RemoteCallOutLLMChatWebClient;
import com.yiwise.dialogflow.client.service.remote.RemoteTrainChatService;
import com.yiwise.dialogflow.client.service.remote.RemoteTrainLLMChatWebClient;
import com.yiwise.dialogflow.client.slb.BotChatContextService;
import com.yiwise.dialogflow.client.slb.BotChatServerSelectContext;
import com.yiwise.dialogflow.engine.share.CallDataInfo;
import com.yiwise.dialogflow.engine.share.ChatMetaData;
import com.yiwise.dialogflow.engine.share.IntentLevelAnalysisResult;
import com.yiwise.dialogflow.engine.share.QueryNodeApiTestReq;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.request.ChatRequest;
import com.yiwise.dialogflow.engine.share.request.CreateSessionRequest;
import com.yiwise.dialogflow.engine.share.request.HttpRequest;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import com.yiwise.dialogflow.engine.share.service.RemoteChatService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.util.context.Context;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;

@Slf4j
public class BotChatServiceImpl implements BotChatService {

    private final RemoteCallOutChatService remoteCallOutChatService;

    private final RemoteTrainChatService remoteTrainChatService;

    private final RemoteTrainLLMChatWebClient remoteTrainLLMChatWebClient;

    private final RemoteCallOutLLMChatWebClient remoteCallOutLLMChatWebClient;

    public BotChatServiceImpl(RemoteCallOutChatService remoteCallOutChatService, RemoteTrainChatService remoteTrainChatService) {
        this(remoteCallOutChatService, remoteTrainChatService, null, null);
    }

    public BotChatServiceImpl(RemoteCallOutChatService remoteCallOutChatService,
                              RemoteTrainChatService remoteTrainChatService,
                              RemoteTrainLLMChatWebClient remoteTrainLLMChatWebClient,
                              RemoteCallOutLLMChatWebClient remoteCallOutLLMChatWebClient) {
        this.remoteCallOutChatService = remoteCallOutChatService;
        this.remoteTrainChatService = remoteTrainChatService;
        this.remoteTrainLLMChatWebClient = remoteTrainLLMChatWebClient;
        this.remoteCallOutLLMChatWebClient = remoteCallOutLLMChatWebClient;
    }

    @Override
    public Optional<SessionInfo> createSessionWithRetry(Long botId,
                                                        Object callTaskId,
                                                        Integer version,
                                                        RobotSnapshotUsageTargetEnum target,
                                                        ChatMetaData chatMetaData,
                                                        int maxTryTimes) {
        if (Objects.nonNull(chatMetaData)) {
            chatMetaData.setLogId(MDC.get("MDC_LOG_ID"));
            chatMetaData.setRequestHostIp(ServerInfoConstants.SERVER_IP_ADDRESS);
            chatMetaData.setRequestHostName(ServerInfoConstants.SERVER_HOSTNAME);
        }
        return invokeWithRetry("创建会话", maxTryTimes, false, (tryTimes) -> {
            CreateSessionRequest createSessionRequest = new CreateSessionRequest();
            createSessionRequest.setChatMetaData(chatMetaData);
            createSessionRequest.setBotId(botId);
            createSessionRequest.setVersion(version);
            createSessionRequest.setUsageTarget(target);
            BotChatServerSelectContext context = new BotChatServerSelectContext(target, String.valueOf(callTaskId), tryTimes > 0);
            BotChatContextService.setChatServerSelectContext(context);
            try {
                SessionInfo sessionInfo = selectChatService(context)
                        .createSession(createSessionRequest)
                        .getData();
                if (Objects.nonNull(sessionInfo)) {
                    sessionInfo.setCallTaskId(callTaskId);
                }
                return sessionInfo;
            } finally {
                BotChatContextService.clearChatServerSelectContext();
            }
        });
    }

    @Override
    public ChatResponse sendEvent(ChatRequest request, Object callTaskId, RobotSnapshotUsageTargetEnum usageTarget) {
        return sendEventWithRetry(request, callTaskId, usageTarget, 1);
    }

    @Override
    public Flux<ChatResponse> asyncSendEvent(ChatRequest request, Object callTaskId, RobotSnapshotUsageTargetEnum usageTarget) {
        if (Objects.nonNull(request)) {
            request.setLogId(MDC.get("MDC_LOG_ID"));
            request.setRequestHostIp(ServerInfoConstants.SERVER_IP_ADDRESS);
            request.setRequestHostName(ServerInfoConstants.SERVER_HOSTNAME);
        }
        BotChatServerSelectContext context = new BotChatServerSelectContext(usageTarget, String.valueOf(callTaskId), false);
        String body = JsonUtils.object2String(request);
        return selectClient(context)
                .post()
                .uri("/apiBot/v3/chatService/processRequest")
                .attribute(BotChatServerSelectContext.ATTRIBUTE_KEY, context)
                // ai-call-back webflux版本较低
                .header("Content-Type", "application/json;charset=UTF-8")
                .body(BodyInserters.fromObject(body))
                .retrieve()
                .bodyToFlux(String.class)
                .flatMap(responseBody -> {
                    log.debug("responseBody:{}", responseBody);
                    try {
                        // 移除前后的[]
                        responseBody = responseBody.trim();
                        if (responseBody.startsWith("[") && responseBody.endsWith("]")) {
                            responseBody = responseBody.substring(1, responseBody.length() - 1);
                        }
                        // 原来的单对象解析方式
                        ResultObject<ChatResponse> result = JsonUtils.string2Object(responseBody, new TypeReference<ResultObject<ChatResponse>>(){});
                        return Flux.just(result);
                    } catch (Exception e) {
                        log.error("解析响应失败: {}", responseBody, e);
                        return Flux.empty();
                    }
                })
                .filter(ResultObject::isSuccess)
                .map(ResultObject::getData);
    }

    private ChatResponse sendEventWithRetry(ChatRequest request, Object callTaskId, RobotSnapshotUsageTargetEnum usageTarget, int maxTryTimes) {
        if (Objects.nonNull(request)) {
            request.setLogId(MDC.get("MDC_LOG_ID"));
            request.setRequestHostIp(ServerInfoConstants.SERVER_IP_ADDRESS);
            request.setRequestHostName(ServerInfoConstants.SERVER_HOSTNAME);
        }
        return invokeWithRetry("发送对话事件", maxTryTimes, false, (tryTimes) -> {
            BotChatServerSelectContext context = new BotChatServerSelectContext(usageTarget, String.valueOf(callTaskId), tryTimes > 0);
            BotChatContextService.setChatServerSelectContext(context);
            try {
                long start = System.currentTimeMillis();
                ResultObject<ChatResponse> result = selectChatService(context).processRequest(request);
                long end = System.currentTimeMillis();
                long duration = end - start;
                log.debug("请求对话接口, duration:{}, host:{}, requestId:{}", duration, result.getHost(), result.getRequestId());
                return result.getData();
            } finally {
                BotChatContextService.clearChatServerSelectContext();
            }
        }).orElse(null);
    }

    @Override
    public Map<String, String> httpRequest(QueryNodeApiTestReq req, Object callTaskId, RobotSnapshotUsageTargetEnum usageTarget) {
        return httpRequestWithRetry(req, callTaskId, usageTarget, 1);
    }

    private Map<String, String> httpRequestWithRetry(QueryNodeApiTestReq req, Object callTaskId, RobotSnapshotUsageTargetEnum usageTarget, int maxTryTimes) {
        HttpRequest request = new HttpRequest();
        request.setLogId(MDC.get("MDC_LOG_ID"));
        request.setRequestHostIp(ServerInfoConstants.SERVER_IP_ADDRESS);
        request.setRequestHostName(ServerInfoConstants.SERVER_HOSTNAME);
        request.setHttpRequestInfo(req);

        return invokeWithRetry("外呼中异步调用HTTP接口", maxTryTimes, false, (tryTimes) -> {
            BotChatServerSelectContext context = new BotChatServerSelectContext(usageTarget, String.valueOf(callTaskId), tryTimes > 0);
            BotChatContextService.setChatServerSelectContext(context);
            try {
                return selectChatService(context).httpRequest(request).getData();
            } finally {
                BotChatContextService.clearChatServerSelectContext();
            }
        }).orElse(null);
    }

    @Override
    public void finish() {

    }

    @Override
    public IntentLevelAnalysisResult intentLevelAnalysis(Object callTaskId, RobotSnapshotUsageTargetEnum usageTarget, CallDataInfo callDataInfo) {
        return intentLevelAnalysisWithRetry(String.valueOf(callTaskId), usageTarget, callDataInfo, 1, RemoteChatService::analysis);
    }

    @Override
    public IntentLevelAnalysisResult intentLevelAnalysisInCall(Object callTaskId, RobotSnapshotUsageTargetEnum usageTarget, CallDataInfo callDataInfo) {
        return intentLevelAnalysisWithRetry(String.valueOf(callTaskId), usageTarget, callDataInfo, 1, RemoteChatService::analysisInCall);
    }

    private IntentLevelAnalysisResult intentLevelAnalysisWithRetry(String callTaskId,
                                                                   RobotSnapshotUsageTargetEnum usageTarget,
                                                                   CallDataInfo callDataInfo,
                                                                   int maxTryTimes,
                                                                   BiFunction<RemoteChatService, CallDataInfo, ResultObject<IntentLevelAnalysisResult>> intentAnalyzeFunc) {
        if (Objects.nonNull(callDataInfo)) {
            callDataInfo.setLogId(MDC.get("MDC_LOG_ID"));
            callDataInfo.setRequestHostIp(ServerInfoConstants.SERVER_IP_ADDRESS);
            callDataInfo.setRequestHostName(ServerInfoConstants.SERVER_HOSTNAME);
        }

        log.info("调用意向等级分析, callData={}", JsonUtils.object2StringNotNull(callDataInfo));

        Optional<IntentLevelAnalysisResult> result = invokeWithRetry("意向等级分析", maxTryTimes, true, (tryTimes) -> {
            BotChatServerSelectContext context = new BotChatServerSelectContext(usageTarget, callTaskId, tryTimes > 0);
            BotChatContextService.setChatServerSelectContext(context);
            try {
                return intentAnalyzeFunc.apply(selectChatService(context), callDataInfo).getData();
            } finally {
                BotChatContextService.clearChatServerSelectContext();
            }
        });

        result.ifPresent(r -> {
            log.info("调用意向等级分析, result={}", JsonUtils.object2StringNotNull(r));
        });

        return result.orElse(null);
    }

    private RemoteChatService selectChatService(BotChatServerSelectContext context) {
        if (Objects.nonNull(context) && RobotSnapshotUsageTargetEnum.CALL_OUT.equals(context.getUsageTarget())) {
            return remoteCallOutChatService;
        }
        return remoteTrainChatService;
    }


    private WebClient selectClient(BotChatServerSelectContext context) {
        if (Objects.nonNull(context) && RobotSnapshotUsageTargetEnum.CALL_OUT.equals(context.getUsageTarget())) {
            return remoteCallOutLLMChatWebClient.getWebClient();
        }
        return remoteTrainLLMChatWebClient.getWebClient();
    }

    private  <T> Optional<T> invokeWithRetry(String invokeJobName,
                                           int maxTryTimes,
                                           boolean delayOnRetry,
                                           Function<Integer, T> invoker) {
        if (maxTryTimes < 1) {
            maxTryTimes = 1;
        }
        if (maxTryTimes > 10) {
            maxTryTimes = 10;
        }
        Random random = new Random();
        Exception exception = null;
        for (int i = 0; i < maxTryTimes; i++) {
            try {
                if (i > 0) {
                    log.info("{}, 第{}次调用", invokeJobName, i + 1);
                }
                T result = invoker.apply(i);
                if (Objects.nonNull(result)) {
                    return Optional.of(result);
                }
            } catch (Exception e) {
                log.warn("{}, 进行重试, tryCount={}, maxTryTimes={}",invokeJobName, i + 1, maxTryTimes, e);
                exception = e;
            }
            if (delayOnRetry) {
                try {
                    Thread.sleep(i * 1000 + random.nextInt(500));
                } catch (InterruptedException e) {
                    log.warn("{}, 进行重试", invokeJobName, e);
                }
            }
        }

        if (Objects.nonNull(exception)) {
            throw new RuntimeException(exception);
        }
        return Optional.empty();
    }
}
