package com.yiwise.dialogflow.client.engine.asr;

import com.yiwise.dialogflow.client.model.asr.AsrMetaData;
import com.yiwise.dialogflow.engine.share.enums.AsrProviderEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/8
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BaseAsrContext {
    /**
     * ASR供应商
     */
    AsrProviderEnum asrProvider;

    /**
     * ASR appkey
     */
    String asrAppkey;

    /**
     * ASR供应商的热词ID
     */
    String asrVocabId;

    /**
     * 话术重构-ASR热词表ID
     */
    Long asrVocabDetailId;

    /**
     * ASR供应商的自学习模型ID
     */
    String asrSelfLearningId;

    /**
     * 话术重构-ASR自学习模型表ID
     */
    Long asrSelfLearningDetailId;

    /**
     * 话术重构-ASR纠错模型ID
     */
    String asrErrorCorrectionDetailId;

    /**
     * ASR参数
     */
    Map<String, String> asrParam;

    /**
     * ASR纠错白名单
     */
    List<String> asrCorrectionWhiteList;

    /**
     * ASR纠错阈值
     */
    BigDecimal asrCorrectionThreshold;

    /**
     * 反应灵敏度
     */
    Integer maxSentenceSilence;

    List<String> asrVocabDetailContentList;

    Boolean enableAsrOptimization;
}
