package com.yiwise.dialogflow.client.asr;

import com.yiwise.dialogflow.client.config.DialogEngineConstant;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

import static com.yiwise.dialogflow.client.config.DialogEngineConstant.PACKET_BUFFER_SIZE;

/**
 * 过滤掉音频头部的数据
 */
@Slf4j
public class FixedHeadAudioFilter extends DefaultMuteAudioFilter {

    private static final byte[] EMPTY_BUFFER = new byte[320];

    final int skipSize;

    AtomicInteger skipDataCount = new AtomicInteger(0);

    boolean beginVad = false;

    public FixedHeadAudioFilter(BotMetaData botMetaData) {
        super(botMetaData);
        if (Objects.nonNull(botMetaData.getBotAsrConfig())
                && Objects.nonNull(botMetaData.getBotAsrConfig().getAsrDelayStartSeconds())) {
            int size = (int) (botMetaData.getBotAsrConfig().getAsrDelayStartSeconds() * DialogEngineConstant.MONO_FILE_LENGTH_PER_SECOND);
            skipSize = size - (size % PACKET_BUFFER_SIZE);
        } else {
            skipSize = 0;
        }
        log.debug("skipSize={}", skipSize);
    }

    @Override
    public List<FilterResult> filter(byte[] bytes, int offset, int length) {
        if (skipDataCount.get() < skipSize) {
            skipDataCount.getAndAdd(length);
            bytes = EMPTY_BUFFER;
            offset = 0;
            length = 320;
        }
        return super.filter(bytes, offset, length);
    }

    @Override
    protected void vadUpdate(byte[] bytes, int offset, int length) {
        if (skipDataCount.get() < skipSize) {
            return;
        }
        if (!beginVad) {
            beginVad = true;
            log.debug("开始vad识别");
        }
        super.vadUpdate(bytes, offset, length);
    }

    @Override
    public void waitingAsrInitSkipAudioData(int length) {
        super.waitingAsrInitSkipAudioData(length);
        skipDataCount.getAndAdd(length);
    }

    @Override
    public void onVoiceEnd(long l) {
        log.debug("检测到开始说话");
        updateStatus(Status.SAYING);
    }

}
