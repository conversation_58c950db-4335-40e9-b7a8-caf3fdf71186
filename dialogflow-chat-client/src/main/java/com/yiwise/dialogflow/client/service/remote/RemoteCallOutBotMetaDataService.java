package com.yiwise.dialogflow.client.service.remote;

import com.yiwise.dialogflow.client.config.RemoteChatRibbonConfig;
import com.yiwise.dialogflow.engine.share.service.RemoteBotMetaDataService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "aicc-platform-dialogflow-chat-web",
        path = "/apiBot/v3/botMetaData",
        configuration = RemoteChatRibbonConfig.class)
public interface RemoteCallOutBotMetaDataService extends RemoteBotMetaDataService {

}
