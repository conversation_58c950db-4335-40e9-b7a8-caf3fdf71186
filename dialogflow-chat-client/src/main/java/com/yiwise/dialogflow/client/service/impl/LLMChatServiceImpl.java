package com.yiwise.dialogflow.client.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yiwise.base.common.helper.ServerInfoConstants;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.client.service.LLMChatClient;
import com.yiwise.dialogflow.client.service.remote.RemoteCallOutLLMChatWebClient;
import com.yiwise.dialogflow.client.service.remote.RemoteTrainLLMChatWebClient;
import com.yiwise.dialogflow.client.slb.BotChatServerSelectContext;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.request.ChatRequest;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.util.Objects;

@Slf4j
public class LLMChatServiceImpl implements LLMChatClient {

    private final RemoteTrainLLMChatWebClient remoteTrainLLMChatWebClient;

    private final RemoteCallOutLLMChatWebClient remoteCallOutLLMChatWebClient;

    public LLMChatServiceImpl(RemoteTrainLLMChatWebClient remoteTrainLLMChatWebClient,
                              RemoteCallOutLLMChatWebClient remoteCallOutLLMChatWebClient) {
        this.remoteTrainLLMChatWebClient = remoteTrainLLMChatWebClient;
        this.remoteCallOutLLMChatWebClient = remoteCallOutLLMChatWebClient;
    }

    @Override
    public Flux<ChatResponse> sendEvent(ChatRequest request, Object callTaskId, RobotSnapshotUsageTargetEnum usageTarget) {
        if (Objects.nonNull(request)) {
            request.setLogId(MDC.get("MDC_LOG_ID"));
            request.setRequestHostIp(ServerInfoConstants.SERVER_IP_ADDRESS);
            request.setRequestHostName(ServerInfoConstants.SERVER_HOSTNAME);
        }
        BotChatServerSelectContext context = new BotChatServerSelectContext(usageTarget, String.valueOf(callTaskId), false);
        String body = JsonUtils.object2String(request);
        return selectService(context)
                .post()
                .uri("/apiBot/v3/chatService/llmProcessRequest")
                // ai-call-back webflux版本较低
                .header("Content-Type", "application/json;charset=UTF-8")
                .body(BodyInserters.fromObject(body))
                .retrieve()
                .bodyToFlux(String.class)
                .map(responseBody -> JsonUtils.string2Object(responseBody, new TypeReference<ResultObject<ChatResponse>>(){}))
                .filter(ResultObject::isSuccess)
                .map(ResultObject::getData);
    }

    private WebClient selectService(BotChatServerSelectContext context) {
        if (Objects.nonNull(context) && RobotSnapshotUsageTargetEnum.CALL_OUT.equals(context.getUsageTarget())) {
            return remoteCallOutLLMChatWebClient.getWebClient();
        }
        return remoteTrainLLMChatWebClient.getWebClient();
    }

}
