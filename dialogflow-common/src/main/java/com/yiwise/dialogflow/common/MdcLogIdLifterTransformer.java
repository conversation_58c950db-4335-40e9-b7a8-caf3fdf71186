package com.yiwise.dialogflow.common;

import org.reactivestreams.Publisher;
import org.reactivestreams.Subscription;
import org.slf4j.MDC;
import reactor.core.CoreSubscriber;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.context.Context;

import java.util.function.Function;

/**
 * 在reactor中, 因为线程切换, 导致MDC中logId的数据丢失, 通过此类可以将MDC中logId的数据传递到下游
 * @param <T>
 */
public class MdcLogIdLifterTransformer<T> implements Function<Publisher<T>, Publisher<T>> {
    private MdcLogIdLifterTransformer() {}

    @Override
    public Publisher<T> apply(Publisher<T> publisher) {
        if (publisher instanceof Mono) {
            return new MonoMdcContextLifter<>((Mono<T>) publisher);
        } else if (publisher instanceof Flux) {
            return new FluxMdcContextLifter<>((Flux<T>) publisher);
        } else {
            throw new IllegalArgumentException("Unsupported publisher type: " + publisher.getClass().getName());
        }
    }

    public static <T> MdcLogIdLifterTransformer<T> lift() {
        return new MdcLogIdLifterTransformer<>();
    }

    private static class MonoMdcContextLifter<T> extends Mono<T> {
        private final Mono<T> source;

        public MonoMdcContextLifter(Mono<T> source) {
            this.source = source;
        }

        @Override
        public void subscribe(CoreSubscriber<? super T> actual) {
            source.subscribe(new MdcLogIdLifter<>(actual));
        }
    }

    private static class FluxMdcContextLifter<T> extends Flux<T> {

        private final Flux<T> source;

        public FluxMdcContextLifter(Flux<T> source) {
            this.source = source;
        }

        @Override
        public void subscribe(CoreSubscriber<? super T> actual) {
            source.subscribe(new MdcLogIdLifter<>(actual));
        }
    }

    public static class MdcLogIdLifter<T> implements CoreSubscriber<T> {
        private final CoreSubscriber<T> actual;

        public MdcLogIdLifter(CoreSubscriber<T> actual) {
            this.actual = actual;
        }

        @Override
        public void onNext(T t) {
            copyToMdc(actual.currentContext());
            actual.onNext(t);
        }

        @Override
        public void onSubscribe(Subscription subscription) {
            copyToMdc(actual.currentContext());
            actual.onSubscribe(subscription);
        }

        @Override
        public void onComplete() {
            copyToMdc(actual.currentContext());
            actual.onComplete();
        }

        @Override
        public void onError(Throwable throwable) {
            copyToMdc(actual.currentContext());
            actual.onError(throwable);
        }

        @Override
        public Context currentContext() {
            return actual.currentContext();
        }

        private static void copyToMdc(Context context) {
            if (context == null) {
                return;
            }
            if (!context.isEmpty()) {
                context.getOrEmpty(ApplicationConstant.MDC_LOG_ID)
                        .ifPresent(logId -> MDC.put(ApplicationConstant.MDC_LOG_ID, logId.toString()));
            }
        }
    }
}

