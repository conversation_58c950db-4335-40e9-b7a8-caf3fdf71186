package com.yiwise.dialogflow.common;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
public class OssKeyCenter {

    public static String getBotUploadAudioPath(Long botId, String fileName) {
        return ApplicationConstant.OSS_ROOT_DIR + "UploadAudio/" + botId + "/" + fileName;
    }

    public static String getBotTtsAudioPath(Long botId, String fileName) {
        return ApplicationConstant.OSS_ROOT_DIR + "UploadAudio/ttsCompose/" + botId + "/" + fileName;
    }

    public static String getDownloadAudioZipOssKey(Long botId) {
        return ApplicationConstant.OSS_ROOT_DIR + "DownloadAudio/" + botId + "/" + String.format("%s_%s.zip", botId, "音频导出");
    }

    public static String getDownloadAudioOssKey(Long botId, String label) {
        return ApplicationConstant.OSS_ROOT_DIR + "DownloadAudio/" + botId + "/" + String.format("%s.wav", label);
    }

    public static String getBotBatchUploadAudioResult(Long botId) {
        return ApplicationConstant.OSS_ROOT_DIR + "UploadAudioResult/" + System.currentTimeMillis() + "/" + botId + "/";
    }

    public static String getBotStatsExportFileKey(Long botId, String fileName) {
        return ApplicationConstant.OSS_ROOT_DIR + "StatsExport/" + botId + "/" + System.currentTimeMillis() + "/" + fileName;

    }
    public static String getBotExportFileKey(Long botId, String fileName) {
        return ApplicationConstant.OSS_ROOT_DIR + "BotExport/" + botId + "/" + fileName;
    }

    public static String getBotOperationLogExportPath(Long botId, String fileName) {
        return ApplicationConstant.OSS_ROOT_DIR + "OperationLogExport/" + botId + "/" + fileName;
    }

    public static String getUploadIssFileKey(Long tenantId, Long userId, String fileName) {
        return ApplicationConstant.OSS_UPLOAD_ROOT
                + "/" + tenantId
                + "/" + userId
                + "/" + trimPostfix(fileName)
                + "_" + System.currentTimeMillis()
                + getPostfix(fileName);
    }

    private static String trimPostfix(String file) {
        if (file == null || !file.contains(".")) {
            return "";
        } else {
            return file.substring(0, file.lastIndexOf("."));
        }
    }

    private static String getPostfix(String file) {
        if (file == null || !file.contains(".")) {
            return "";
        } else {
            return file.substring(file.lastIndexOf("."));
        }
    }

    public static String getSilenceAudioOssKey(Long pauseMs) {
        return ApplicationConstant.OSS_ROOT_DIR + "SilenceAudio/" + pauseMs + ".wav";
    }

    public static String getBotGenerateConfigKey(String fileSuffix) {
        return ApplicationConstant.OSS_ROOT_DIR + "BotGenerate/" + System.currentTimeMillis() + "." + fileSuffix;
    }

    public static String getAllCorpusExportKey() {
        return ApplicationConstant.OSS_ROOT_DIR + "AllCorpusExport/" + System.currentTimeMillis() + ".csv";
    }

    public static String getBotExportSnapshotJsonPath(Long botId) {
        return ApplicationConstant.OSS_ROOT_DIR + "Snapshot/" + System.currentTimeMillis() + "/" + botId + ".json";
    }

    public static String getBatchBotQRCodePosterKey() {
        return ApplicationConstant.OSS_ROOT_DIR + "QRCodePoster/" + System.currentTimeMillis() + "/体验海报.zip";
    }

    public static String getAnalyzeTaskResultKey(Long taskId) {
        return ApplicationConstant.OSS_ROOT_DIR + "AnalyzeTask/" + taskId + ".xlsx";
    }

    public static String getBotExportFullResourceZipDirPath(Long botId) {
        return ApplicationConstant.OSS_ROOT_DIR + "BotZip/" + System.currentTimeMillis() + "/" + botId + ".zip";
    }

    public static String getBotImportZipOssKey(String fileName) {
        return ApplicationConstant.OSS_ROOT_DIR + "BotImportZip/" + System.currentTimeMillis() + "/" + fileName;
    }

    public static String getVarAuditionDir() {
        return ApplicationConstant.OSS_ROOT_DIR + "Audition/" + System.currentTimeMillis() + "/";
    }
    public static String getVarValueReplaceExport() {
        String fileName = "变量读音值替换导出-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx";
        return ApplicationConstant.OSS_ROOT_DIR + "VarValueTTSReplaceExport/" + fileName + ".xlsx";
    }

    public static String getBotRagDocUploadOssKey(Long botId, String fileName) {
        return ApplicationConstant.OSS_ROOT_DIR + "BotRagDoc/" + botId + "/" + System.currentTimeMillis() + "/" + fileName;
    }
}
