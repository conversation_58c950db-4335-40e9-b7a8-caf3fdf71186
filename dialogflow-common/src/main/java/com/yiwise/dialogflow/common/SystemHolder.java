package com.yiwise.dialogflow.common;

import com.yiwise.base.model.enums.SystemEnum;

/**
 * <AUTHOR>
 * @date 2023/8/25
 */
public class SystemHolder {

    private static final ThreadLocal<SystemEnum> THREAD_LOCAL = new ThreadLocal<>();

    public static SystemEnum getSystem() {
        return THREAD_LOCAL.get();
    }

    public static void setSystem(SystemEnum system) {
        THREAD_LOCAL.set(system);
    }

    public static void remove() {
        THREAD_LOCAL.remove();
    }
}
