package com.yiwise.dialogflow.common;

import java.util.Collections;
import java.util.List;

import com.yiwise.base.common.context.EnvEnum;
import com.yiwise.base.common.utils.PropertyLoaderUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.enums.CodeDescEnum;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ApplicationConstant {

    public static final String CURRENT_APPLICATION_NAME = PropertyLoaderUtils.getProperty("spring.application.name");


    /**
     * 短信魔板配置为随外呼任务配置的模板占位ID
     */
    public static final Long SMS_TEMPLATE_PLACEHOLDER_ID = -1L;

    public static final String DIALOG_FLOW_WEB_APP_NAME = "aicc-platform-dialogflow-web";

    public static final String MDC_LOG_ID = "MDC_LOG_ID";
    /**
     * 本地临时文件存储路径
     */
    public static final String LOCAL_TMP_DIR = "/tmp/";

    public static final String TXT_SUFFIX = ".txt";

    public static final String OSS_ROOT_DIR = "dialog/";

    // 将WAV文件转换成PCM忽略文件头的大小
    public static final int WAV_TO_PCM_HEAD_LEN = 44 + 16 * 5;

    // 特殊的意图id(兜底意图, 用户无应答, 采集成功)
    public static final String DEFAULT_INTENT_ID = "default";
    public static final String USER_SILENCE_INTENT_ID = "userSilence";
    public static final String COLLECT_SUCCESS_INTENT_ID = "collectSuccess";
    public static final String COLLECT_FAILED_INTENT_ID = "collectFailed";

    public static final String DEFAULT_INTENT_NAME = "兜底意图";
    public static final String USER_SILENCE_INTENT_NAME = "客户无应答";
    public static final String COLLECT_SUCCESS_INTENT_NAME = "采集成功";
    public static final String COLLECT_FAILED_INTENT_NAME = "采集失败";

    public static final double AUDIO_PLAY_FINISH_PROGRESS = 100d;

    public static final String ALGORITHM_REGISTER_ENV = PropertyLoaderUtils.getProperty("algorithm.register.env");

    public static final String PRIVATE_DOMAIN_INTENT_ALGORITHM_URL = PropertyLoaderUtils.getProperty("ai.private.domain.intent.algorithm.url");

    public static final String ACTIVITY_INTENT_ALGORITHM_URL = PropertyLoaderUtils.getProperty("ai.activity.intent.algorithm.url");

    public static final String EDUCATION_ACTIVITY_INTENT_ALGORITHM_URL = PropertyLoaderUtils.getProperty("ai.education.activity.intent.algorithm.url");

    public static final String OSS_UPLOAD_ROOT = PropertyLoaderUtils.getProperty("oss.upload.root");


    /**
     * 普通token过期时间
     */
    public static final Integer TOKEN_TTL_TIME = 7 * 24 * 60 * 60;

    /**
     * ope token过期时间
     */
    public static final Integer OPE_TOKEN_TTL_TIME = 6 * 60 * 60;

    /**
     * 简单表对象缓存时间
     */
    public static final Integer POJO_CACHE_TIMEOUT_OF_SECOND = 30 * 24 * 60 * 60;


    public static final double AUDIO_AVERAGE_VOLUME_RATE = 0.8;

    /**
     * Websocket连接地址
     */
    public static final String WEBSOCKET_TRAIN_CONNECT_URL = "/apiBot/v3/webSocket/train";

    /**
     * tts后台合成任务 Websocket连接地址
     */
    public static final String WEBSOCKET_TTS_JOB_CONNECT_URL = "/apiBot/v3/webSocket/tts";

    /**
     * bot生成, 推送合成完成通知
     */
    public static final String WEBSOCKET_BOT_GENERATE_CONNECT_URL = "/apiBot/v3/webSocket/botGenerate";

    /**
     * bot生成改写模型, 改写完成通知
     */
    public static final String WEBSOCKET_BOT_REWRITE_CONNECT_URL = "/apiBot/v3/webSocket/botRewrite";

    /**
     * bot资源锁 Websocket连接地址
     */
    public static final String WEBSOCKET_BOT_RESOURCE_LOCK_CONNECT_URL = "/apiBot/v3/webSocket/botResourceLock";

    /**
     * Websocket订阅地址
     */
    public static final String WEBSOCKET_TRAIN_SUBSCRIPT_URL = "/queue/trainResult";

    /**
     * tts job progress Websocket订阅地址
     */
    public static final String WEBSOCKET_TTS_JOB_SUBSCRIPT_URL = "/queue/tts";

    public static final String WEBSOCKET_BOT_GENERATE_SUBSCRIPT_URL = "/queue/botGenerate";

    public static final String WEBSOCKET_BOT_REWRITE_SUBSCRIPT_URL = "/queue/botRewrite";

    public static final String WEBSOCKET_BOT_RESOURCE_LOCK_SUBSCRIPT_URL = "/queue/botResourceLock";

    /**
     * 背景音配置
     */
    public static final String BOT_BACKGROUND_SOUND_URL = PropertyLoaderUtils.getProperty("bot.backgroundSound.url");

    /**
     * 分组路径分隔符
     */
    public static final String GROUP_PATH_DELIMITER = "/";

    /**
     * 全部知识
     */
    public static final String ALL_KNOWLEDGE = "全部知识";

    /**
     * 全部意图
     */
    public static final String ALL_INTENT = "全部意图";

    /**
     * 默认分组
     */
    public static final String DEFAULT_GROUP = "默认分组";

    /**
     * 启动环境
     */
    public static final EnvEnum CURR_ENV = CodeDescEnum.getFromDescOrThrow(EnvEnum.class, PropertyLoaderUtils.getProperty("spring.profiles.active"));


    /**
     * 排序方向: 倒序
     */
    public static final String SORT_DIRECTION_DESC = "descending";

    /**
     * 排序方向: 正序
     */
    public static final String SORT_DIRECTION_ASC = "ascending";
    /**
     * ASR纠错模型
     */
    public static final String ASR_ERROR_CORRECT_URL = PropertyLoaderUtils.getProperty("algorithm.asr.correction.url");

    public static final Long OPE_TENANT_ID = 0L;

    public static final Long OPEN_API_USER_ID = 1L;
    public static final String BOT_EXPORT_AUDIO_MAPPING_FILE_NAME = "audioMapping.json";
    public static final String BOT_EXPORT_SNAPSHOT_FILE_NAME = "snapshot.json";
    public static final String BOT_EXPORT_RAG_DOC_MAPPING_FILE_NAME = "ragDocMapping.json";

    // todo 通过apollo 配置
    public static boolean enableDebug = false;

    public static String BOT_QRCODE_POSTER_FONT_NAME = "微软雅黑";
    public static String BOT_QRCODE_YIWISE_TEMPLATE_IMAGE_PATH = "";
    public static String BOT_QRCODE_COMMON_TEMPLATE_IMAGE_PATH = "";
    public static String BOT_QRCODE_H5_URL = "";

    /**
     * 时间实体收集url
     */
    public static String ALGORITHM_DATETIME_ENTITY_URL = PropertyLoaderUtils.getProperty("algorithm.dateTimeEntity.url");

    /**
     * 地址/人名/组织名实体收集url
     */
    public static String ALGORITHM_ADDRESS_ENTITY_URL = PropertyLoaderUtils.getProperty("algorithm.addressEntity.url");

    /**
     * 年龄、年级实体收集url
     */
    public static String ALGORITHM_AGE_GRADE_ENTITY_URL = PropertyLoaderUtils.getProperty("algorithm.ageGradeEntity.url");

    public static boolean ENABLE_CACHE = true;

    public static String ENABLE_REGEX_MATCH_OPTIMIZATION_BOT_ID = "0";

    public static String SESSION_CONTEXT_SERIALIZE_TYPE = "json";
    /**
     * bot生成命名空间:test, pre, prod
     */
    public static String BOT_GENERATE_NAMESPACE = PropertyLoaderUtils.getProperty("bot.generate.namespace");

    /**
     * but生成校验url
     */
    public static String BOT_GENERATE_VALIDATE_URL = PropertyLoaderUtils.getProperty("bot.generate.validate.url");
    /**
     * but生成url
     */
    public static String BOT_GENERATE_CREATE_URL = PropertyLoaderUtils.getProperty("bot.generate.create.url");

    /**
     * but生成并发数
     */
    public static Integer BOT_GENERATE_CONCURRENCY = 5;

    public static Integer BOT_GENERATE_CREATE_TIMEOUT_SECOND = 60 * 30;

    public static String MA_BOT_STATS_IDS = "";

    public static String BOT_REWRITE_URL = PropertyLoaderUtils.getProperty("bot.rewrite.url");

    public static String BOT_REWRITE_ANALYZE_CONTEXT_URL = PropertyLoaderUtils.getProperty("bot.rewrite.analyzeContext.url");
    public static String BOT_REWRITE_ANALYZE_PROMPT_URL = PropertyLoaderUtils.getProperty("bot.rewrite.analyzePrompt.url");

    public static String SMS_GENERATE_URL = PropertyLoaderUtils.getProperty("sms.generate.url");

    public static String TIANRUN_TTS_VOICE_LIST =  PropertyLoaderUtils.getProperty("tianrun.tts.voice.list");
    /**
     * 敏感词飞书预警webhook
     */
    public static String SENSITIVE_WORDS_WARNING_WEBHOOK_URL = PropertyLoaderUtils.getProperty("sensitive.words.warning.url");

    /**
     * 敏感词预警处理人
     */
    public static String SENSITIVE_WORDS_WARING_AT_USERS = PropertyLoaderUtils.getProperty("sensitive.words.warning.at.users");

    /**
     * 阿里云录音文件识别应用appKey
     */
    public static String ALIYUN_NLS_FLASH_RECOGNIZER_APP_KEY = PropertyLoaderUtils.getProperty("aliyun.nls.flash.recognizer.appKey");

    /**
     * 阿里云敏感词服务名称
     */
    public static String ALIYUN_SENSITIVE_WORDS_SERVICE_NAME = PropertyLoaderUtils.getProperty("aliyun.sensitive.words.service.name");

    /**
     * 阿里云敏感词服务endpoint
     */
    public static String ALIYUN_SENSITIVE_WORDS_ENDPOINT = PropertyLoaderUtils.getProperty("aliyun.sensitive.words.endpoint");

    /**
     * 配置在 oss 上的敏感词文件, 格式为 txt 文件, 一个敏感词一行
     */
    public static String SENSITIVE_CONFIG_OSS_KEY = PropertyLoaderUtils.getProperty("sensitive.config.oss.key");

    /**
     * 分析任务最大并发
     */
    public static Integer ANALYZE_TASK_MAX_CONCURRENCY;

    /**
     * 会话分析回调地址
     */
    public static String ANALYZE_TASK_CALLBACK_URL;

    /**
     * 会话分析请求地址
     */
    public static String ANALYZE_TASK_REQUEST_URL;

    /**
     * 任务过期时间
     */
    public static Integer ANALYZE_TASK_EXPIRED_MINUTES;

    /**
     * 会话分析任务最大语料数量,单位:万
     */
    public static Long ANALYZE_TASK_MAX_CORPUS_SIZE_IN_WAN;

    /**
     * 会话分析单个条件最大语料数量,单位:万
     */
    public static Long ANALYZE_TASK_MAX_CORPUS_SIZE_PER_CONDITION_IN_WAN;

    /**
     * 会话分析结果中单个意图最多保留的语料数
     */
    public static Long ANALYZE_TASK_RESULT_MAX_CORPUS_SIZE_PER_INTENT;

    /**
     * 分析任务平台
     */
    public static String ANALYZE_TASK_PLATFORM;

    /**
     * 会话分析大模型调用次数限制
     */
    public static Integer ANALYZE_TASK_LLM_MAX_TIMES_PER_MONTH;

    /**
     * 会话分析大模型请求地址
     */
    public static String ANALYZE_TASK_LLM_REQUEST_URL;

    /**
     * 大模型实体提取请求地址
     */
    public static String LARGE_MODEL_ENTITY_COLLECT_URL;

    /**
     * 大模型服务名称
     */
    public static String LARGE_MODEL_ENTITY_COLLECT_SERVICE_NAME;

    /**
     * 大模型版本
     */
    public static String LARGE_MODEL_ENTITY_COLLECT_VERSION;

    public static String ALGORITHM_RAG_UPLOAD_CALLBACK_DOMAIN = "";
    public static String ALGORITHM_RAG_UPLOAD_URL = "";
    public static String ALGORITHM_RAG_UPLOAD_ENV = "";
    public static String ALGORITHM_LLM_CHAT_URL = "";
    public static String ALGORITHM_LLMSTEP_CHAT_URL = "";
    public static String ALGORITHM_LLM_ANALYZE_URL = "";
    public static String ALGORITHM_LLM_CACHE_URL = "";
    public static String LLM_ANSWER_HANGUP_REGEX_JSON_LIST = "";
    public static String LLM_MODEL_NAME_LIST_URL = "";

    public static String ALGORITHM_INTERRUPT_URL = "";

    public static String ENABLE_ADVANCED_ASR_DELAY_START_BOT_IDS = "";
    /**
     * 问答对齐请求地址
     */
    public static String QA_ALIGN_URL;

    /**
     * 大模型生成同义词请求地址
     */
    public static String LARGE_MODEL_GENERATE_SYNONYM_URL;

    /**
     * 大模型生成相似成员请求地址
     */
    public static String LARGE_MODEL_GENERATE_MEMBER_URL;

    /**
     * 大模型生成同义词或者相似成员服务名称
     */
    public static String LARGE_MODEL_GENERATE_SERVICE_NAME;

    /**
     * 大模型生成同义词或者相似成员版本
     */
    public static String LARGE_MODEL_GENERATE_VERSION;

    /**
     * 算法节点标签请求地址
     */
    public static String ALGORITHM_NODE_LABEL_URL;

    /**
     * 算法领域标签列表接口
     */
    public static String ALGORITHM_DOMAIN_LABEL_LIST_URL;

    /**
     * 算法领域信息
     */
    public static String ALGORITHM_DOMAIN_INFO;

    /**
     * 高级版地址信息
     */
    public static String ALGORITHM_ADVANCED_ADDRESS_ENTITY_URL = PropertyLoaderUtils.getProperty("algorithm.advancedAddressEntity.url");
    public static String ALGORITHM_ADVANCED_ADDRESS_ENTITY_PROVIDER = PropertyLoaderUtils.getProperty("algorithm.advancedAddressEntity.provider");

    /**
     * 文本解析服务地址
     */
    public static String ALGORITHM_TEXT_BANK_PARSING_URL;

    /**
     * 文本检索地址
     */
    public static String ALGORITHM_TEXT_BANK_SEARCH_URL;

    /**
     * 文本重写地址
     */
    public static String ALGORITHM_TEXT_BANK_REPHRASE_URL;

    /**
     * 算法服务领域参数
     */
    public static String ALGORITHM_SERVICE_OPE_DOMAIN;

    /**
     * 算法服务环境参数
     */
    public static String ALGORITHM_SERVICE_NAMESPACE;

    /**
     * 文档知识上传地址
     */
    public static String ALGORITHM_RAG_DOC_UPLOAD_URL;

    /**
     * 文档知识回调地址
     */
    public static String ALGORITHM_RAG_DOC_CALLBACK_URL;

    /**
     * 文档知识删除地址
     */
    public static String ALGORITHM_RAG_DOC_DELETE_URL;

    /**
     * 文档知识片段删除地址
     */
    public static String ALGORITHM_RAG_DOC_SEGMENT_DELETE_URL;

    /**
     * 文档知识片段添加地址
     */
    public static String ALGORITHM_RAG_DOC_SEGMENT_ADD_URL;

    /**
     * 文档片段批量新增地址
     */
    public static String ALGORITHM_RAG_DOC_SEGMENT_BATCH_ADD_URL;

    /**
     * 文档检索地址
     */
    public static String ALGORITHM_RAG_DOC_SEARCH_URL;

    /**
     * 文档环境参数
     */
    public static String ALGORITHM_RAG_DOC_ID_SUFFIX;

    /**
     * 大模型流程提示词优化
     */
    public static String ALGORITHM_LLM_PROMPT_OPTIMIZE_URL;


    public static String PAYPAL_PROXY_URL = "";
    public static String PAYPAL_SIGN_URL = "";
    public static String PAYPAL_TENANT_KEY_JSON = "";


    /**
     * 算法nlu实体识别
     */
    public static String ALGORITHM_NLU_ENTITY_PREDICT_URL;

    /**
     * 算法nlu意图预测
     */
    public static String ALGORITHM_NLU_INTENT_PREDICT_URL;

    /**
     * 算法nlu启用的botId列表
     */
    public static String ALGORITHM_NLU_INTENT_PREDICT_ENABLE_BOT_ID_LIST;
    public static String ASR_OPTIMIZATION_BOT_IDS = "";
    public static String ASR_OPTIMIZATION_CONFIG_JSON = "";

    public static int ENABLE_FRAGMENT_AUDIO_PLAYER_PERCENT = 0;
    public static String ENABLE_FRAGMENT_AUDIO_PLAYER_BOT_ID_LIST;

    /**
     * 抖音回调相关配置
     */
    public static String DOUYIN_CALLBACK_URL = "";
    public static String DOUYIN_CALLBACK_MAINBRAND_IDS = "";
    public static String DOUYIN_CALLBACK_PROVIDER = "";
    public static String DOUYIN_CALLBACK_SCRIPT_PREFIX = "ys_";
    public static String DOUYIN_CAllBACK_SK = "";

    /**
     * 大模型意图规则分析
     */
    public static String ALGORITHM_LLM_INTENT_RULE_ANALYZE_URL;

    /**
     * 大模型意图规则分析启用的botId列表
     */
    public static String ALGORITHM_LLM_INTENT_RULE_ENABLE_BOT_ID_LIST;

    /**
     * 大模型生成结果的后处理
     */
    public static String LLM_GENERATE_ANSWER_CONVERT_JSON = "";

    public static String MOBILE_MD5_DECRYPT_URL = "";

    /**
     * 运营商在通话前 N 秒增加的提示音正则匹配列表
     */
    public static List<String> OPERATOR_PROMPT_AUDIO_REGEX_LIST = Collections.emptyList();
    public static List<String> OPERATOR_PROMPT_AUDIO_PRE_REGEX_LIST = Collections.emptyList();
    public static List<String> V2_OPERATOR_PROMPT_AUDIO_REGEX_LIST = Collections.emptyList();
    public static List<String> OPERATOR_PROMPT_EXCLUDE_REGEX_LIST = Collections.emptyList();

    /**
     * 仅在前 N 秒才检测信息, 如果是 < 1 则不检测
     */
    public static Integer OPERATOR_PROMPT_AUDIO_BEFORE_SECONDS = 0;
    public static List<Long> OPERATOR_PROMPT_AUDIO_DISABLE_TENANT_IDS = Collections.emptyList();
    public static List<Long> OPERATOR_PROMPT_AUDIO_USE_V2_TENANT_IDS = Collections.emptyList();
    public static boolean defaultUseV2PromptAudioCheck;
    public static Integer OPERATOR_PROMPT_AUDIO_RESET_TIMEOUT_MS = 1000;
    public static Integer OPERATOR_PROMPT_AUDIO_MERGE_INPUT_INTERVAL_MS = 1000;

    public static void reload() {
        if (PropertyLoaderUtils.containsProperty("enable.regexMatch.optimization.botId")) {
            ENABLE_REGEX_MATCH_OPTIMIZATION_BOT_ID = PropertyLoaderUtils.getProperty("enable.regexMatch.optimization.botId");
        } else {
            ENABLE_REGEX_MATCH_OPTIMIZATION_BOT_ID = "0";
        }
        if (PropertyLoaderUtils.containsProperty("session.context.serialize.type")) {
            SESSION_CONTEXT_SERIALIZE_TYPE = PropertyLoaderUtils.getProperty("session.context.serialize.type");
        } else {
            SESSION_CONTEXT_SERIALIZE_TYPE = "json";
        }
        if (PropertyLoaderUtils.containsProperty("bot.generate.concurrency")) {
            BOT_GENERATE_CONCURRENCY = PropertyLoaderUtils.getIntProperty("bot.generate.concurrency");
        } else {
            BOT_GENERATE_CONCURRENCY = 5;
        }
        if (PropertyLoaderUtils.containsProperty("bot.generate.validate.url")) {
            BOT_GENERATE_VALIDATE_URL = PropertyLoaderUtils.getProperty("bot.generate.validate.url");
        }
        if (PropertyLoaderUtils.containsProperty("bot.generate.create.url")) {
            BOT_GENERATE_CREATE_URL = PropertyLoaderUtils.getProperty("bot.generate.create.url");
        }
        if (PropertyLoaderUtils.containsProperty("bot.generate.create.timeout.second")) {
            BOT_GENERATE_CREATE_TIMEOUT_SECOND = PropertyLoaderUtils.getIntProperty("bot.generate.create.timeout.second");
        } else {
            BOT_GENERATE_CREATE_TIMEOUT_SECOND = 60 * 30;
        }

        if (PropertyLoaderUtils.containsProperty("maBotStats.botIds")) {
            MA_BOT_STATS_IDS = PropertyLoaderUtils.getProperty("maBotStats.botIds");
        } else {
            MA_BOT_STATS_IDS = "";
        }
        if (PropertyLoaderUtils.containsProperty("enableCache")) {
            ENABLE_CACHE = PropertyLoaderUtils.getBooleanProperty("enableCache");
        } else {
            ENABLE_CACHE = true;
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.dateTimeEntity.url")) {
            ALGORITHM_DATETIME_ENTITY_URL = PropertyLoaderUtils.getProperty("algorithm.dateTimeEntity.url");
        } else {
            ALGORITHM_DATETIME_ENTITY_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.addressEntity.url")) {
            ALGORITHM_ADDRESS_ENTITY_URL = PropertyLoaderUtils.getProperty("algorithm.addressEntity.url");
        } else {
            ALGORITHM_ADDRESS_ENTITY_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.ageGradeEntity.url")) {
            ALGORITHM_AGE_GRADE_ENTITY_URL = PropertyLoaderUtils.getProperty("algorithm.ageGradeEntity.url");
        } else {
            ALGORITHM_AGE_GRADE_ENTITY_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("bot.rewrite.url")) {
            BOT_REWRITE_URL = PropertyLoaderUtils.getProperty("bot.rewrite.url");
        } else {
            BOT_REWRITE_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("bot.rewrite.analyzeContext.url")) {
            BOT_REWRITE_ANALYZE_CONTEXT_URL = PropertyLoaderUtils.getProperty("bot.rewrite.analyzeContext.url");
        } else {
            BOT_REWRITE_ANALYZE_CONTEXT_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("bot.rewrite.analyzePrompt.url")) {
             BOT_REWRITE_ANALYZE_PROMPT_URL = PropertyLoaderUtils.getProperty("bot.rewrite.analyzePrompt.url");
        } else {
            BOT_REWRITE_ANALYZE_PROMPT_URL = "";
        }

        if (PropertyLoaderUtils.containsProperty("sms.generate.url")) {
            SMS_GENERATE_URL = PropertyLoaderUtils.getProperty("sms.generate.url");
        }
        if (PropertyLoaderUtils.containsProperty("tianrun.tts.voice.list")) {
            TIANRUN_TTS_VOICE_LIST = PropertyLoaderUtils.getProperty("tianrun.tts.voice.list");
        } else {
            TIANRUN_TTS_VOICE_LIST = "";
        }
        if (PropertyLoaderUtils.containsProperty("sensitive.words.warning.url")) {
            SENSITIVE_WORDS_WARNING_WEBHOOK_URL = PropertyLoaderUtils.getProperty("sensitive.words.warning.url");
        }
        if (PropertyLoaderUtils.containsProperty("sensitive.words.warning.at.users")) {
            SENSITIVE_WORDS_WARING_AT_USERS = PropertyLoaderUtils.getProperty("sensitive.words.warning.at.users");
        }
        if (PropertyLoaderUtils.containsProperty("aliyun.nls.flash.recognizer.appKey")) {
            ALIYUN_NLS_FLASH_RECOGNIZER_APP_KEY = PropertyLoaderUtils.getProperty("aliyun.nls.flash.recognizer.appKey");
        }
        if (PropertyLoaderUtils.containsProperty("aliyun.sensitive.words.service.name")) {
            ALIYUN_SENSITIVE_WORDS_SERVICE_NAME = PropertyLoaderUtils.getProperty("aliyun.sensitive.words.service.name");
        }
        if (PropertyLoaderUtils.containsProperty("aliyun.sensitive.words.endpoint")) {
            ALIYUN_SENSITIVE_WORDS_ENDPOINT = PropertyLoaderUtils.getProperty("aliyun.sensitive.words.endpoint");
        }

        if (PropertyLoaderUtils.containsProperty("qrCode.yiwiseTemplate.path")) {
            BOT_QRCODE_YIWISE_TEMPLATE_IMAGE_PATH = PropertyLoaderUtils.getProperty("qrCode.yiwiseTemplate.path");
        }
        if (PropertyLoaderUtils.containsProperty("qrCode.commonTemplate.path")) {
            BOT_QRCODE_COMMON_TEMPLATE_IMAGE_PATH = PropertyLoaderUtils.getProperty("qrCode.commonTemplate.path");
        }
        if (PropertyLoaderUtils.containsProperty("qrCode.poster.font")) {
            BOT_QRCODE_POSTER_FONT_NAME = PropertyLoaderUtils.getProperty("qrCode.poster.font");
        }
        if (PropertyLoaderUtils.containsProperty("qrCode.poster.h5.url")) {
            BOT_QRCODE_H5_URL = PropertyLoaderUtils.getProperty("qrCode.poster.h5.url");
        }
        if (PropertyLoaderUtils.containsProperty("sensitive.config.oss.key")) {
            SENSITIVE_CONFIG_OSS_KEY = PropertyLoaderUtils.getProperty("sensitive.config.oss.key");
        }
        if (PropertyLoaderUtils.containsProperty("analyze.task.max.concurrency")) {
            ANALYZE_TASK_MAX_CONCURRENCY = PropertyLoaderUtils.getIntProperty("analyze.task.max.concurrency");
        } else {
            ANALYZE_TASK_MAX_CONCURRENCY = 1;
        }
        if (PropertyLoaderUtils.containsProperty("analyze.task.callback.url")) {
            ANALYZE_TASK_CALLBACK_URL = PropertyLoaderUtils.getProperty("analyze.task.callback.url");
        } else {
            ANALYZE_TASK_CALLBACK_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("analyze.task.request.url")) {
            ANALYZE_TASK_REQUEST_URL = PropertyLoaderUtils.getProperty("analyze.task.request.url");
        } else {
            ANALYZE_TASK_REQUEST_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("analyze.task.expired.minutes")) {
            ANALYZE_TASK_EXPIRED_MINUTES = PropertyLoaderUtils.getIntProperty("analyze.task.expired.minutes");
        } else {
            ANALYZE_TASK_EXPIRED_MINUTES = 2 * 60;
        }
        if (PropertyLoaderUtils.containsProperty("analyze.task.max.corpus.size.in.wan")) {
            ANALYZE_TASK_MAX_CORPUS_SIZE_IN_WAN = PropertyLoaderUtils.getLongProperty("analyze.task.max.corpus.size.in.wan");
        } else {
            ANALYZE_TASK_MAX_CORPUS_SIZE_IN_WAN = 2000L;
        }
        if (PropertyLoaderUtils.containsProperty("analyze.task.result.max.corpus.size.per.intent")) {
            ANALYZE_TASK_RESULT_MAX_CORPUS_SIZE_PER_INTENT = PropertyLoaderUtils.getLongProperty("analyze.task.result.max.corpus.size.per.intent");
        } else {
            ANALYZE_TASK_RESULT_MAX_CORPUS_SIZE_PER_INTENT = 500L;
        }
        if (PropertyLoaderUtils.containsProperty("analyze.task.max.corpus.size.per.condition.in.wan")) {
            ANALYZE_TASK_MAX_CORPUS_SIZE_PER_CONDITION_IN_WAN = PropertyLoaderUtils.getLongProperty("analyze.task.max.corpus.size.per.condition.in.wan");
        } else {
            ANALYZE_TASK_MAX_CORPUS_SIZE_PER_CONDITION_IN_WAN = 400L;
        }
        if (PropertyLoaderUtils.containsProperty("analyze.task.platform")) {
            ANALYZE_TASK_PLATFORM = PropertyLoaderUtils.getProperty("analyze.task.platform");
        } else {
            ANALYZE_TASK_PLATFORM = "";
        }
        if (PropertyLoaderUtils.containsProperty("analyze.task.llm.max.times.per.month")) {
            ANALYZE_TASK_LLM_MAX_TIMES_PER_MONTH = PropertyLoaderUtils.getIntProperty("analyze.task.llm.max.times.per.month");
        } else {
            ANALYZE_TASK_LLM_MAX_TIMES_PER_MONTH = 200;
        }
        if (PropertyLoaderUtils.containsProperty("analyze.task.llm.request.url")) {
            ANALYZE_TASK_LLM_REQUEST_URL = PropertyLoaderUtils.getProperty("analyze.task.llm.request.url");
        } else {
            ANALYZE_TASK_LLM_REQUEST_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("large.model.entity.collect.url")) {
            LARGE_MODEL_ENTITY_COLLECT_URL = PropertyLoaderUtils.getProperty("large.model.entity.collect.url");
        } else {
            LARGE_MODEL_ENTITY_COLLECT_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("large.model.entity.collect.service.name")) {
            LARGE_MODEL_ENTITY_COLLECT_SERVICE_NAME = PropertyLoaderUtils.getProperty("large.model.entity.collect.service.name");
        } else {
            LARGE_MODEL_ENTITY_COLLECT_SERVICE_NAME = "";
        }
        if (PropertyLoaderUtils.containsProperty("large.model.entity.collect.version")) {
            LARGE_MODEL_ENTITY_COLLECT_VERSION = PropertyLoaderUtils.getProperty("large.model.entity.collect.version");
        } else {
            LARGE_MODEL_ENTITY_COLLECT_VERSION = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.ragUpload.callbackDomain")) {
            ALGORITHM_RAG_UPLOAD_CALLBACK_DOMAIN = PropertyLoaderUtils.getProperty("algorithm.ragUpload.callbackDomain");
        } else {
            ALGORITHM_RAG_UPLOAD_CALLBACK_DOMAIN = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.ragUpload.url")) {
            ALGORITHM_RAG_UPLOAD_URL = PropertyLoaderUtils.getProperty("algorithm.ragUpload.url");
        } else {
            ALGORITHM_RAG_UPLOAD_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.ragUpload.env")) {
            ALGORITHM_RAG_UPLOAD_ENV = PropertyLoaderUtils.getProperty("algorithm.ragUpload.env");
        } else {
            ALGORITHM_RAG_UPLOAD_ENV = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.llmChat.url")) {
            ALGORITHM_LLM_CHAT_URL = PropertyLoaderUtils.getProperty("algorithm.llmChat.url");
        } else {
            ALGORITHM_LLM_CHAT_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.llmStepChat.url")) {
            ALGORITHM_LLMSTEP_CHAT_URL = PropertyLoaderUtils.getProperty("algorithm.llmStepChat.url");
        } else {
            ALGORITHM_LLMSTEP_CHAT_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.llmAnalyze.url")) {
            ALGORITHM_LLM_ANALYZE_URL = PropertyLoaderUtils.getProperty("algorithm.llmAnalyze.url");
        } else {
            ALGORITHM_LLM_ANALYZE_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.llm.cache.url")) {
            ALGORITHM_LLM_CACHE_URL = PropertyLoaderUtils.getProperty("algorithm.llm.cache.url");
        } else {
            ALGORITHM_LLM_CACHE_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.llmModel.nameList.url")) {
            LLM_MODEL_NAME_LIST_URL = PropertyLoaderUtils.getProperty("algorithm.llmModel.nameList.url");
        } else {
            LLM_MODEL_NAME_LIST_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("llmAnswer.hangupRegex.jsonList")) {
            LLM_ANSWER_HANGUP_REGEX_JSON_LIST = PropertyLoaderUtils.getProperty("llmAnswer.hangupRegex.jsonList");
        } else {
            LLM_ANSWER_HANGUP_REGEX_JSON_LIST = "";
        }
        if (PropertyLoaderUtils.containsProperty("enable.advancedAsrDelayStart.botIds")) {
            ENABLE_ADVANCED_ASR_DELAY_START_BOT_IDS = PropertyLoaderUtils.getProperty("enable.advancedAsrDelayStart.botIds");
        } else {
            ENABLE_ADVANCED_ASR_DELAY_START_BOT_IDS = "";
        }
        if (PropertyLoaderUtils.containsProperty("qa.align.url")) {
            QA_ALIGN_URL = PropertyLoaderUtils.getProperty("qa.align.url");
        } else {
            QA_ALIGN_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("large.model.generate.synonym.url")) {
            LARGE_MODEL_GENERATE_SYNONYM_URL = PropertyLoaderUtils.getProperty("large.model.generate.synonym.url");
        } else {
            LARGE_MODEL_GENERATE_SYNONYM_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("large.model.generate.member.url")) {
            LARGE_MODEL_GENERATE_MEMBER_URL = PropertyLoaderUtils.getProperty("large.model.generate.member.url");
        } else {
            LARGE_MODEL_GENERATE_MEMBER_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("large.model.generate.service.name")) {
            LARGE_MODEL_GENERATE_SERVICE_NAME = PropertyLoaderUtils.getProperty("large.model.generate.service.name");
        } else {
            LARGE_MODEL_GENERATE_SERVICE_NAME = "";
        }
        if (PropertyLoaderUtils.containsProperty("large.model.generate.version")) {
            LARGE_MODEL_GENERATE_VERSION = PropertyLoaderUtils.getProperty("large.model.generate.version");
        } else {
            LARGE_MODEL_GENERATE_VERSION = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.node.label.url")) {
            ALGORITHM_NODE_LABEL_URL = PropertyLoaderUtils.getProperty("algorithm.node.label.url");
        } else {
            ALGORITHM_NODE_LABEL_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.domain.label.list.url")) {
            ALGORITHM_DOMAIN_LABEL_LIST_URL = PropertyLoaderUtils.getProperty("algorithm.domain.label.list.url");
        } else {
            ALGORITHM_DOMAIN_LABEL_LIST_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.domain.info")) {
            ALGORITHM_DOMAIN_INFO = PropertyLoaderUtils.getProperty("algorithm.domain.info");
        } else {
            ALGORITHM_DOMAIN_INFO = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.advancedAddressEntity.url")) {
            ALGORITHM_ADVANCED_ADDRESS_ENTITY_URL = PropertyLoaderUtils.getProperty("algorithm.advancedAddressEntity.url");
        } else {
            ALGORITHM_ADVANCED_ADDRESS_ENTITY_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.advancedAddressEntity.provider")) {
            ALGORITHM_ADVANCED_ADDRESS_ENTITY_PROVIDER = PropertyLoaderUtils.getProperty("algorithm.advancedAddressEntity.provider");
        } else {
            ALGORITHM_ADVANCED_ADDRESS_ENTITY_PROVIDER = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.rag.doc.upload.url")) {
            ALGORITHM_RAG_DOC_UPLOAD_URL = PropertyLoaderUtils.getProperty("algorithm.rag.doc.upload.url");
        } else {
            ALGORITHM_RAG_DOC_UPLOAD_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.rag.doc.callback.url")) {
            ALGORITHM_RAG_DOC_CALLBACK_URL = PropertyLoaderUtils.getProperty("algorithm.rag.doc.callback.url");
        } else {
            ALGORITHM_RAG_DOC_CALLBACK_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.rag.doc.delete.url")) {
            ALGORITHM_RAG_DOC_DELETE_URL = PropertyLoaderUtils.getProperty("algorithm.rag.doc.delete.url");
        } else {
            ALGORITHM_RAG_DOC_DELETE_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.rag.doc.segment.delete.url")) {
            ALGORITHM_RAG_DOC_SEGMENT_DELETE_URL = PropertyLoaderUtils.getProperty("algorithm.rag.doc.segment.delete.url");
        } else {
            ALGORITHM_RAG_DOC_SEGMENT_DELETE_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.rag.doc.segment.add.url")) {
            ALGORITHM_RAG_DOC_SEGMENT_ADD_URL = PropertyLoaderUtils.getProperty("algorithm.rag.doc.segment.add.url");
        } else {
            ALGORITHM_RAG_DOC_SEGMENT_ADD_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.rag.doc.search.url")) {
            ALGORITHM_RAG_DOC_SEARCH_URL = PropertyLoaderUtils.getProperty("algorithm.rag.doc.search.url");
        } else {
            ALGORITHM_RAG_DOC_SEARCH_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.rag.doc.id.suffix")) {
            ALGORITHM_RAG_DOC_ID_SUFFIX = PropertyLoaderUtils.getProperty("algorithm.rag.doc.id.suffix");
        } else {
            ALGORITHM_RAG_DOC_ID_SUFFIX = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.rag.doc.segment.batch.add.url")) {
            ALGORITHM_RAG_DOC_SEGMENT_BATCH_ADD_URL = PropertyLoaderUtils.getProperty("algorithm.rag.doc.segment.batch.add.url");
        } else {
            ALGORITHM_RAG_DOC_SEGMENT_BATCH_ADD_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.text.bank.parsing.url")) {
            ALGORITHM_TEXT_BANK_PARSING_URL = PropertyLoaderUtils.getProperty("algorithm.text.bank.parsing.url");
        } else {
            ALGORITHM_TEXT_BANK_PARSING_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.text.bank.search.url")) {
            ALGORITHM_TEXT_BANK_SEARCH_URL = PropertyLoaderUtils.getProperty("algorithm.text.bank.search.url");
        } else {
            ALGORITHM_TEXT_BANK_SEARCH_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.text.bank.rephrase.url")) {
            ALGORITHM_TEXT_BANK_REPHRASE_URL = PropertyLoaderUtils.getProperty("algorithm.text.bank.rephrase.url");
        } else {
            ALGORITHM_TEXT_BANK_REPHRASE_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.service.ope.domain")) {
            ALGORITHM_SERVICE_OPE_DOMAIN = PropertyLoaderUtils.getProperty("algorithm.service.ope.domain");
        } else {
            ALGORITHM_SERVICE_OPE_DOMAIN = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.service.namespace")) {
            ALGORITHM_SERVICE_NAMESPACE = PropertyLoaderUtils.getProperty("algorithm.service.namespace");
        } else {
            ALGORITHM_SERVICE_NAMESPACE = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.llm.prompt.optimize.url")) {
            ALGORITHM_LLM_PROMPT_OPTIMIZE_URL = PropertyLoaderUtils.getProperty("algorithm.llm.prompt.optimize.url");
        } else {
            ALGORITHM_LLM_PROMPT_OPTIMIZE_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("paypal.proxy.url")) {
            PAYPAL_PROXY_URL = PropertyLoaderUtils.getProperty("paypal.proxy.url");
        } else {
            PAYPAL_PROXY_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("paypal.sign.url")) {
            PAYPAL_SIGN_URL = PropertyLoaderUtils.getProperty("paypal.sign.url");
        } else {
            PAYPAL_SIGN_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("paypal.tenantKey.json")) {
            PAYPAL_TENANT_KEY_JSON = PropertyLoaderUtils.getProperty("paypal.tenantKey.json");
        } else {
            PAYPAL_TENANT_KEY_JSON = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.nlu.entity.predict.url")) {
            ALGORITHM_NLU_ENTITY_PREDICT_URL = PropertyLoaderUtils.getProperty("algorithm.nlu.entity.predict.url");
        } else {
            ALGORITHM_NLU_ENTITY_PREDICT_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.nlu.intent.predict.url")) {
            ALGORITHM_NLU_INTENT_PREDICT_URL = PropertyLoaderUtils.getProperty("algorithm.nlu.intent.predict.url");
        } else {
            ALGORITHM_NLU_INTENT_PREDICT_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.nlu.intent.predict.enable.bot.id.list")) {
            ALGORITHM_NLU_INTENT_PREDICT_ENABLE_BOT_ID_LIST = PropertyLoaderUtils.getProperty("algorithm.nlu.intent.predict.enable.bot.id.list");
        } else {
            ALGORITHM_NLU_INTENT_PREDICT_ENABLE_BOT_ID_LIST = "";
        }
        if (PropertyLoaderUtils.containsProperty("asr.optimization.bot.ids")) {
            ASR_OPTIMIZATION_BOT_IDS = PropertyLoaderUtils.getProperty("asr.optimization.bot.ids");
        } else {
            ASR_OPTIMIZATION_BOT_IDS = "";
        }
        if (PropertyLoaderUtils.containsProperty("asr.optimization.config.json")) {
            ASR_OPTIMIZATION_CONFIG_JSON = PropertyLoaderUtils.getProperty("asr.optimization.config.json");
        } else {
            ASR_OPTIMIZATION_CONFIG_JSON = "";
        }
        if (PropertyLoaderUtils.containsProperty("botClient.fragmentAudioPlayer.percent")) {
            ENABLE_FRAGMENT_AUDIO_PLAYER_PERCENT = PropertyLoaderUtils.getIntProperty("botClient.fragmentAudioPlayer.percent");
        } else {
            ENABLE_FRAGMENT_AUDIO_PLAYER_PERCENT = 0;
        }
        if (PropertyLoaderUtils.containsProperty("botClient.fragmentAudioPlayer.botIdList")) {
            ENABLE_FRAGMENT_AUDIO_PLAYER_BOT_ID_LIST = PropertyLoaderUtils.getProperty("botClient.fragmentAudioPlayer.botIdList");
        } else {
            ENABLE_FRAGMENT_AUDIO_PLAYER_BOT_ID_LIST = "";
        }
        if (PropertyLoaderUtils.containsProperty("douyin.callback.url")) {
            DOUYIN_CALLBACK_URL = PropertyLoaderUtils.getProperty("douyin.callback.url");
        } else {
            DOUYIN_CALLBACK_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("douyin.callback.mainBrandIds")) {
            DOUYIN_CALLBACK_MAINBRAND_IDS = PropertyLoaderUtils.getProperty("douyin.callback.mainBrandIds");
        } else {
            DOUYIN_CALLBACK_MAINBRAND_IDS = "";
        }
        if (PropertyLoaderUtils.containsProperty("douyin.callback.provider")) {
            DOUYIN_CALLBACK_PROVIDER = PropertyLoaderUtils.getProperty("douyin.callback.provider");
        } else {
            DOUYIN_CALLBACK_PROVIDER = "";
        }
        if (PropertyLoaderUtils.containsProperty("douyin.callback.scriptPrefix")) {
            DOUYIN_CALLBACK_SCRIPT_PREFIX = PropertyLoaderUtils.getProperty("douyin.callback.scriptPrefix");
        } else {
            DOUYIN_CALLBACK_SCRIPT_PREFIX = "";
        }
        if (PropertyLoaderUtils.containsProperty("douyin.callback.sk")) {
            DOUYIN_CAllBACK_SK = PropertyLoaderUtils.getProperty("douyin.callback.sk");
        } else {
            DOUYIN_CAllBACK_SK = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.llm.intent.rule.analyze.url")) {
            ALGORITHM_LLM_INTENT_RULE_ANALYZE_URL = PropertyLoaderUtils.getProperty("algorithm.llm.intent.rule.analyze.url");
        } else {
            ALGORITHM_LLM_INTENT_RULE_ANALYZE_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.llm.intent.rule.enable.bot.id.list")) {
            ALGORITHM_LLM_INTENT_RULE_ENABLE_BOT_ID_LIST = PropertyLoaderUtils.getProperty("algorithm.llm.intent.rule.enable.bot.id.list");
        } else {
            ALGORITHM_LLM_INTENT_RULE_ENABLE_BOT_ID_LIST = "";
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.llm.generateAnswer.convert.json")) {
            LLM_GENERATE_ANSWER_CONVERT_JSON = PropertyLoaderUtils.getProperty("algorithm.llm.generateAnswer.convert.json");
        } else {
            LLM_GENERATE_ANSWER_CONVERT_JSON = "";
        }
        if (PropertyLoaderUtils.containsProperty("mobileDecrypt.md5.url")) {
            MOBILE_MD5_DECRYPT_URL = PropertyLoaderUtils.getProperty("mobileDecrypt.md5.url");
        } else {
            MOBILE_MD5_DECRYPT_URL = "";
        }
        if (PropertyLoaderUtils.containsProperty("operator.prompt.audio.regex.list")) {
            String regexList = PropertyLoaderUtils.getProperty("operator.prompt.audio.regex.list");
            // 解析为 json 列表
            try {
                OPERATOR_PROMPT_AUDIO_REGEX_LIST = JsonUtils.string2ListObject(regexList, String.class);
            } catch (Exception e) {
                log.warn("[LogHub_Warn]解析 operator.prompt.audio.regex.list 失败, 使用默认值", e);
                OPERATOR_PROMPT_AUDIO_REGEX_LIST = Collections.emptyList();
            }
        } else {
            OPERATOR_PROMPT_AUDIO_REGEX_LIST = Collections.emptyList();
        }
        if (PropertyLoaderUtils.containsProperty("operator.prompt.audio.regex.list.v2")) {
            String regexList = PropertyLoaderUtils.getProperty("operator.prompt.audio.regex.list.v2");
            // 解析为 json 列表
            try {
                V2_OPERATOR_PROMPT_AUDIO_REGEX_LIST = JsonUtils.string2ListObject(regexList, String.class);
            } catch (Exception e) {
                log.warn("[LogHub_Warn]解析 operator.prompt.audio.regex.list.v2 失败, 使用默认值", e);
                V2_OPERATOR_PROMPT_AUDIO_REGEX_LIST = Collections.emptyList();
            }
        } else {
            V2_OPERATOR_PROMPT_AUDIO_REGEX_LIST = Collections.emptyList();
        }
        if (PropertyLoaderUtils.containsProperty("operator.prompt.audio.excludeRegexList")) {
            String regexList = PropertyLoaderUtils.getProperty("operator.prompt.audio.excludeRegexList");
            // 解析为 json 列表
            try {
                OPERATOR_PROMPT_EXCLUDE_REGEX_LIST = JsonUtils.string2ListObject(regexList, String.class);
            } catch (Exception e) {
                log.warn("[LogHub_Warn]解析 operator.prompt.audio.excludeRegexList 失败, 使用默认值", e);
                OPERATOR_PROMPT_EXCLUDE_REGEX_LIST = Collections.emptyList();
            }
        } else {
            OPERATOR_PROMPT_EXCLUDE_REGEX_LIST = Collections.emptyList();
        }
        if (PropertyLoaderUtils.containsProperty("operator.prompt.audio.preRegex.list")) {
            String regexList = PropertyLoaderUtils.getProperty("operator.prompt.audio.preRegex.list");
            // 解析为 json 列表
            try {
                OPERATOR_PROMPT_AUDIO_PRE_REGEX_LIST = JsonUtils.string2ListObject(regexList, String.class);
            } catch (Exception e) {
                log.warn("[LogHub_Warn]解析 operator.prompt.audio.preRegex.list 失败, 使用默认值", e);
                OPERATOR_PROMPT_AUDIO_PRE_REGEX_LIST = Collections.emptyList();
            }
        } else {
            OPERATOR_PROMPT_AUDIO_PRE_REGEX_LIST = Collections.emptyList();
        }
        if (PropertyLoaderUtils.containsProperty("operator.prompt.audio.before.seconds")) {
            OPERATOR_PROMPT_AUDIO_BEFORE_SECONDS = PropertyLoaderUtils.getIntProperty("operator.prompt.audio.before.seconds");
        } else {
            OPERATOR_PROMPT_AUDIO_BEFORE_SECONDS = 0;
        }
        if (PropertyLoaderUtils.containsProperty("operator.prompt.audio.disableTenantIds")) {
            String json = PropertyLoaderUtils.getProperty("operator.prompt.audio.disableTenantIds");
            // 解析为 json 列表
            try {
                OPERATOR_PROMPT_AUDIO_DISABLE_TENANT_IDS = JsonUtils.string2ListObject(json, Long.class);
            } catch (Exception e) {
                log.warn("[LogHub_Warn]解析 operator.prompt.audio.disableTenantIds 失败, 使用默认值", e);
                OPERATOR_PROMPT_AUDIO_DISABLE_TENANT_IDS = Collections.emptyList();
            }
        } else {
            OPERATOR_PROMPT_AUDIO_DISABLE_TENANT_IDS = Collections.emptyList();
        }
        if (PropertyLoaderUtils.containsProperty("operator.prompt.audio.v2TenantIds")) {
            String json = PropertyLoaderUtils.getProperty("operator.prompt.audio.v2TenantIds");
            // 解析为 json 列表
            try {
                OPERATOR_PROMPT_AUDIO_USE_V2_TENANT_IDS = JsonUtils.string2ListObject(json, Long.class);
            } catch (Exception e) {
                log.warn("[LogHub_Warn]解析 operator.prompt.audio.v2TenantIds 失败, 使用默认值", e);
                OPERATOR_PROMPT_AUDIO_USE_V2_TENANT_IDS = Collections.emptyList();
            }
        } else {
            OPERATOR_PROMPT_AUDIO_USE_V2_TENANT_IDS = Collections.emptyList();
        }
        if (PropertyLoaderUtils.containsProperty("operator.prompt.audio.defaultUseV2")) {
            defaultUseV2PromptAudioCheck = PropertyLoaderUtils.getBooleanProperty("operator.prompt.audio.defaultUseV2");
        } else {
            defaultUseV2PromptAudioCheck = false;
        }
        if (PropertyLoaderUtils.containsProperty("operator.prompt.audio.resetTimeoutMs")) {
            OPERATOR_PROMPT_AUDIO_RESET_TIMEOUT_MS = PropertyLoaderUtils.getIntProperty("operator.prompt.audio.resetTimeoutMs");
        } else {
            OPERATOR_PROMPT_AUDIO_RESET_TIMEOUT_MS = 1000;
        }
        if (PropertyLoaderUtils.containsProperty("operator.prompt.audio.mergeInputIntervalMs")) {
            OPERATOR_PROMPT_AUDIO_MERGE_INPUT_INTERVAL_MS = PropertyLoaderUtils.getIntProperty("operator.prompt.audio.mergeInputIntervalMs");
        } else {
            OPERATOR_PROMPT_AUDIO_MERGE_INPUT_INTERVAL_MS = 1000;
        }
        if (PropertyLoaderUtils.containsProperty("algorithm.interrupt.url")) {
            ALGORITHM_INTERRUPT_URL = PropertyLoaderUtils.getProperty("algorithm.interrupt.url");
        } else {
            ALGORITHM_INTERRUPT_URL = "";
        }
    }

    static {
        PropertyLoaderUtils.addPropertyChangeListener(ApplicationConstant::reload);
        reload();
    }
}
