package com.yiwise.dialogflow.engine.chatprocessor;

import com.yiwise.dialogflow.engine.ReceptionInfo;
import com.yiwise.dialogflow.engine.analysis.EventLog;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.share.action.WaitUserSayFinishAction;
import com.yiwise.dialogflow.engine.share.enums.ActiveTypeEnum;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.request.UserSayFinishEvent;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.utils.DebugLogUtils;
import com.yiwise.dialogflow.engine.utils.EventLogSerializeUtils;
import com.yiwise.dialogflow.entity.bo.algorithm.IntentResultDetailBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.Objects;
import java.util.Optional;

/**
 * 收集对话中所有的数据, 理论上通过eventContext和response就可以了, 不需要在处理逻辑中进行数据的收集
 * 收集的数据用于意向等级判断
 * 需要收集的是意图匹配情况, 各种命中情况等
 * <AUTHOR>
 */
@Slf4j
public class ChatDataCollectPostProcessor extends AbstractPostProcessor {

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "对话数据收集";
    }

    @Override
    public Optional<ChatResponse> doProcess(SessionContext sessionContext, EventContext eventContext, ChatResponse chatResponse) {
        // 目前收集的数据比较简单, 就是收集EventLog数据

        EventLog eventLog = new EventLog();
        eventLog.setSequence(eventContext.getSeq());
        boolean isWaitUserSayFinish = false;
        if (Objects.nonNull(chatResponse)) {
            eventLog.recordAnswerResultInfo(chatResponse.getAnswer());
            eventLog.setAnswerAudioPlayConfig(chatResponse.getAnswerAudioPlayConfig());
            eventLog.setActionList(chatResponse.getActionList());
            if (Objects.nonNull(chatResponse.getAnswer())) {
                eventLog.setUninterrupted(chatResponse.getAnswer().getUninterrupted());
                eventLog.setCustomInterruptThreshold(chatResponse.getAnswer().getCustomInterruptThreshold());
                generateReceptionInfo(eventContext, eventLog);
            } else if (Objects.nonNull(eventContext.getActiveManagerInfo())) {
                // 在延迟挂机命中直接挂机时, 会走到这个分支
                generateReceptionInfo(eventContext, eventLog);
            }
            if (CollectionUtils.isNotEmpty(chatResponse.getActionList())) {
                isWaitUserSayFinish = chatResponse.getActionList().stream()
                        .anyMatch(action -> action instanceof WaitUserSayFinishAction);
            }
        }
        IntentResultDetailBO intentResultDetailBO = eventContext.getAlgorithmPredictIntent();
        if (Objects.nonNull(intentResultDetailBO)) {
            eventLog.setDomain(intentResultDetailBO.getDomain());
            eventLog.setAttribute(intentResultDetailBO.getAttribute());
        }
        eventLog.recordEventContextInfo(eventContext);
        if (Objects.nonNull(eventLog.getSequence())) {
            // 判断是否是有意义的输入
            if (!isWaitUserSayFinish) {
                sessionContext.getValidSequenceSet().add(eventLog.getSequence());
            } else {
                log.info("当前输入是等待用户说完, 不记录到有效输入中, seq:{}", eventLog.getSequence());
            }
        }
        chatResponse.setEventLogContent(EventLogSerializeUtils.serialize(eventLog));

        // 当前所有可播放动态变量的值
        if (MapUtils.isNotEmpty(eventContext.getPlayableVariableValueMap())) {
            chatResponse.setPlayableVariableValueMap(eventContext.getPlayableVariableValueMap());
        }

        try {
            if (eventContext.getOriginEventParam() instanceof UserSayFinishEvent) {
                UserSayFinishEvent userSayFinishEvent = (UserSayFinishEvent) eventContext.getOriginEventParam();
                Double progress = userSayFinishEvent.getPlayProgress();
                DebugLogUtils.commonDebugLog(eventContext, "播放进度:" + convertProgressStr(progress) + "%");
            }
        } catch (Exception e) {
            log.warn("处理异常", e);
        }
        return Optional.of(chatResponse);
    }


    private String convertProgressStr(Double progress) {
        if (Objects.isNull(progress)) {
            return "unknown";
        }
        // 保留两位小数
        return String.format("%.2f", progress);
    }

    private static void generateReceptionInfo(EventContext eventContext, EventLog log) {
        ReceptionInfo receptionInfo = new ReceptionInfo();
        ActiveTypeEnum activeType = eventContext.getActiveManagerInfo().getActiveType();
        receptionInfo.setTargetId(eventContext.getActiveManagerInfo().getOriginId());
        receptionInfo.setTargetName(eventContext.getActiveManagerInfo().getOriginName());
        receptionInfo.setTargetLabel(eventContext.getActiveManagerInfo().getOriginLabel());
        switch (activeType) {
            case KNOWLEDGE: receptionInfo.setType(AnswerSourceEnum.KNOWLEDGE); break;
            case STEP: receptionInfo.setType(AnswerSourceEnum.STEP); break;
            case SPECIAL_ANSWER: receptionInfo.setType(AnswerSourceEnum.SPECIAL_ANSWER); break;
            default:
                break;
        }
        if (Objects.nonNull(receptionInfo.getType())) {
            log.setReceptionInfo(receptionInfo);
        }
        eventContext.setReceptionInfo(receptionInfo);
    }
}
