package com.yiwise.dialogflow.engine.chatfilter;

import com.google.common.collect.Sets;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.chatprocessor.IntentPredictPreProcessor;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.action.IgnoreInputAction;
import com.yiwise.dialogflow.engine.share.request.EventParam;
import com.yiwise.dialogflow.engine.share.request.UserSayEvent;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.utils.ChatResponseGenerateUtils;
import com.yiwise.dialogflow.engine.utils.DebugLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Mono;

import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * 噪音过滤
 */
@Slf4j
public class NoiseChatFilter extends AbstractChatFilter {

    public static final String HAN_NUMBER = "一二三四五六七八九零0123456789";

    public static final Set<String> NOISE_SET = Sets.newHashSet("喂", "位", "未", "为", "微", "对", "嗯", "哦", "啊", "在", "说");

    public static final Set<String> EXCLUDE_SET = Sets.newHashSet("好", "行", "性", "型", "幸", "对", "在", "有", "会", "到", "是", "能", "滚", "忙", "没", "喂", "位", "未", "为", "微");

    private final RobotRuntimeResource resource;

    public NoiseChatFilter(RobotRuntimeResource resource) {
        this.resource = resource;
    }

    @Override
    public Optional<ChatResponse> doFilter(SessionContext sessionContext, EventContext eventContext) {

        String userInput = eventContext.getUserInput();
        Double aiProgress = null;
        EventParam originParam = eventContext.getOriginEventParam();
        if (originParam instanceof UserSayEvent) {
            UserSayEvent userSayEvent = (UserSayEvent) originParam;
            aiProgress = userSayEvent.getPlayProgress();
        }

        if (Objects.isNull(aiProgress)) {
            // 未获取到进度，直接过滤
            return Optional.empty();
        }

        // 非单字
        if (StringUtils.trimToEmpty(userInput).length() > 1) {
            return Optional.empty();
        }

        IntentPredictPreProcessor predictPreProcessor = new IntentPredictPreProcessor(resource);
        predictPreProcessor.process(sessionContext, eventContext);
        if (CollectionUtils.isNotEmpty(eventContext.getCandidateIntentIdList())) {
            log.info("用户输入[{}]匹配到意图[{}]，不过滤", userInput, eventContext.getCandidateIntentIdList());
            return Optional.empty();
        }

        // 在AI播报过程中
        if (aiProgress >= ApplicationConstant.AUDIO_PLAY_FINISH_PROGRESS && NOISE_SET.contains(userInput)) {
            log.info("用户输入[{}]命中AI播报结束关键词，不过滤", userInput);
            return Optional.empty();
        }

        // 单字过滤
        boolean filtered = !EXCLUDE_SET.contains(userInput) && !HAN_NUMBER.contains(userInput);
        if (filtered) {
            // 响应不可打断结果
            String debugLog = "噪音过滤, 继续播报";
            if (aiProgress >= ApplicationConstant.AUDIO_PLAY_FINISH_PROGRESS) {
                debugLog = "噪音过滤，重新输入";
            }
            DebugLogUtils.commonDebugLog(eventContext, debugLog);
            ChatResponse chatResponse = ChatResponseGenerateUtils.generateUninterruptedResponse();
            chatResponse.getActionList().add(IgnoreInputAction.of());
            return Optional.of(chatResponse);
        }
        log.info("用户输入[{}]命中内置关键词，不过滤", userInput);
        return Optional.empty();
    }

    @Override
    public Mono<ChatResponse> doFilterAsync(SessionContext sessionContext, EventContext eventContext) {
        String tmpUserInput = eventContext.getUserInput();
        if (BooleanUtils.isTrue(eventContext.isMergeRequest()) && StringUtils.isNotBlank(eventContext.getOriginUserInput())) {
            tmpUserInput = eventContext.getOriginUserInput();
            log.info("断句补齐, 用户输入使用原始数据[{}]", tmpUserInput);
        }
        String userInput = tmpUserInput;
        Double aiProgress = null;
        EventParam originParam = eventContext.getOriginEventParam();
        if (originParam instanceof UserSayEvent) {
            UserSayEvent userSayEvent = (UserSayEvent) originParam;
            aiProgress = userSayEvent.getPlayProgress();
        }

        if (Objects.isNull(aiProgress)) {
            // 未获取到进度，直接过滤
            return Mono.empty();
        }

        // 非单字
        if (StringUtils.trimToEmpty(userInput).length() > 1) {
            return Mono.empty();
        }

        IntentPredictPreProcessor predictPreProcessor = new IntentPredictPreProcessor(resource);

        final double finalAiProgress = aiProgress;
        return predictPreProcessor.processAsync(sessionContext, eventContext)
                        .then(Mono.defer(() -> {
                            if (CollectionUtils.isNotEmpty(eventContext.getCandidateIntentIdList())) {
                                log.info("用户输入[{}]匹配到意图[{}]，不过滤", userInput, eventContext.getCandidateIntentIdList());
                                return Mono.empty();
                            }
                            // 在AI播报过程中
                            if (finalAiProgress >= ApplicationConstant.AUDIO_PLAY_FINISH_PROGRESS && NOISE_SET.contains(userInput)) {
                                log.info("用户输入[{}]命中AI播报结束关键词，不过滤", userInput);
                                return Mono.empty();
                            }
                            // 单字过滤
                            boolean filtered = !EXCLUDE_SET.contains(userInput) && !HAN_NUMBER.contains(userInput);
                            if (filtered) {
                                // 响应不可打断结果
                                String debugLog = "噪音过滤, 继续播报";
                                if (finalAiProgress >= ApplicationConstant.AUDIO_PLAY_FINISH_PROGRESS) {
                                    debugLog = "噪音过滤，重新输入";
                                }
                                DebugLogUtils.commonDebugLog(eventContext, debugLog);
                                ChatResponse chatResponse = ChatResponseGenerateUtils.generateUninterruptedResponse();
                                chatResponse.getActionList().add(IgnoreInputAction.of());
                                return Mono.just(chatResponse);
                            }
                            log.info("用户输入[{}]命中内置关键词，不过滤", userInput);
                            return Mono.empty();
                        }));
    }

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "噪音过滤";
    }
}
