package com.yiwise.dialogflow.engine.chatprocessor;

import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
public abstract class AbstractChatPreProcessor implements ChatPreProcessor {

    @Override
    public final void process(SessionContext sessionContext, EventContext eventContext) {
            doProcess(sessionContext, eventContext);
    }

    @Override
    public final Mono<Void> processAsync(SessionContext sessionContext, EventContext eventContext) {
        return doProcessAsync(sessionContext, eventContext);
    }

    protected abstract void doProcess(SessionContext sessionContext, EventContext eventContext);

    protected Mono<Void> doProcessAsync(SessionContext sessionContext, EventContext eventContext) {
        return Mono.fromRunnable(() -> doProcess(sessionContext, eventContext));
    }
}
