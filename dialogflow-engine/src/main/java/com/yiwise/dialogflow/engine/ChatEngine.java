package com.yiwise.dialogflow.engine;

import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.ChatMetaData;
import com.yiwise.dialogflow.engine.share.request.ChatRequest;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ChatEngine {
    SessionContext generateAndInitSessionContext(SessionInfo sessionInfo, ChatMetaData chatMetaData);

    SessionContext generateAndInitSessionContext(SessionInfo sessionInfo, ChatMetaData chatMetaData, Map<String, String> magicTemplateVarValueMap);

    Flux<ChatResponse> processAsync(ChatRequest request);

    /**
     * 大模型处理
     * 客户端在接收到需要由大模型生成答案时, 需要请求该接口进行处理
     * 和之前的 processAsync 隔离开, 为了避免 processAsync 里面的逻辑过于复杂和 bug
     */
    default Flux<ChatResponse> llmProcess(ChatRequest request) {
        return Flux.empty();
    }

    RobotRuntimeResource getResource();
}
