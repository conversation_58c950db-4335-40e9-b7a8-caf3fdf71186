package com.yiwise.dialogflow.engine.utils;

import com.google.common.cache.*;
import com.google.common.collect.ImmutableMap;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.PropertyLoaderUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.MdcLogIdLifterTransformer;
import com.yiwise.dialogflow.engine.resource.IntentPredictRequiredResource;
import com.yiwise.dialogflow.entity.bo.algorithm.IntentBO;
import com.yiwise.dialogflow.entity.bo.algorithm.IntentResultBO;
import com.yiwise.dialogflow.entity.bo.algorithm.IntentResultDetailBO;
import com.yiwise.dialogflow.entity.po.intent.IntentConfigPO;
import com.yiwise.dialogflow.service.feishu.FeishuAlertService;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.reactor.circuitbreaker.operator.CircuitBreakerOperator;
import io.netty.handler.timeout.ReadTimeoutException;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by 昌夜 on 19/5/7.
 */
public class IntentPredictUtils {
    private static final Logger logger = LoggerFactory.getLogger(IntentPredictUtils.class);

    private static final String FILTER_PREDICT_PATTERN = "^[0-9a-zA-Z]*$";

    private static final RestTemplate LOW_LATENCY_REST_TEMPLATE = (RestTemplate) AppContextUtils.getBean("lowLatencyRestTemplate");

    private static final WebClient webClient = AppContextUtils.getBean(WebClient.class);

    private static final FeishuAlertService feishuAlertService = AppContextUtils.getBean(FeishuAlertService.class);

    private static final CircuitBreaker circuitBreaker = feishuAlertService.getCircuitBreakerByTopic("NLP").orElse(null);

    private static final String ALGORITHM_PREDICT_URL = PropertyLoaderUtils.getProperty("algorithm.predict.url");
    private static final String ALGORITHM_VERBAL_PREDICT_URL = PropertyLoaderUtils.getProperty("algorithm.verbal.training.url");

    private static final Cache<CustomerModelPredictCacheKey, IntentResultBO> CUSTOMER_MODEL_PREDICT_RESULT_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    public static Mono<IntentResultBO> predictAsync(Long tenantId, IntentPredictRequiredResource resource, String userInput, String lastAiSay, boolean isVerbalTraining, Map<String, String> varNameValueMap) {
        IntentConfigPO intentConfig = resource.getIntentConfig();
        Integer inputMinLength = intentConfig.getAlgorithmInterventionCount();
        Long botId = resource.getIntentModelTemplateBotId();

        IntentResultBO askingBO = new IntentResultBO();
        if (Objects.nonNull(inputMinLength)) {
            if (!canPredict(userInput, inputMinLength)) {
                logger.info("用户输入时纯字母和数字组合，不进行算法预测, 用户输入={}, inputMinLength={}", userInput, inputMinLength);
                return Mono.just(askingBO);
            }
        }

        // 对话中走缓存逻辑
        CustomerModelPredictCacheKey key = new CustomerModelPredictCacheKey(botId, userInput, isVerbalTraining);
        if (ApplicationConstant.ENABLE_CACHE) {
            IntentResultBO result = CUSTOMER_MODEL_PREDICT_RESULT_CACHE.getIfPresent(key);
            if (Objects.nonNull(result)) {
                return Mono.just(result);
            }
        }
        if (Objects.isNull(varNameValueMap)) {
            varNameValueMap = Collections.emptyMap();
        }
        return doPredictAsync(tenantId, botId, userInput, isVerbalTraining, lastAiSay, resource, varNameValueMap)
                .doOnSuccess(result ->
                        CUSTOMER_MODEL_PREDICT_RESULT_CACHE.put(new CustomerModelPredictCacheKey(botId, userInput, isVerbalTraining), result)
                )
                .onErrorReturn(askingBO)
                .defaultIfEmpty(askingBO);
    }

    private static Mono<IntentResultBO> doPredictAsync(Long tenantId, Long botId, String userInput, boolean isVerbalTraining,
                                                       String lastAiSay, IntentPredictRequiredResource resource, Map<String, String> varNameValueMap) {
        return Mono.zip(
                        sendCustomerModelIntentPredictAsync(tenantId, botId, userInput, isVerbalTraining).switchIfEmpty(Mono.just(new IntentResultBO())),
                        nluIntentPredictAsync(tenantId, botId, userInput, lastAiSay, resource, varNameValueMap).switchIfEmpty(Mono.just(Collections.emptyList()))
                ).transformDeferred(MdcLogIdLifterTransformer.lift())
                .flatMap(tuple -> {
                    IntentResultBO intentResultBO = tuple.getT1();
                    List<IntentBO> intentBOList = tuple.getT2();
                    if (Objects.isNull(intentResultBO.getIntent())) {
                        intentResultBO.setIntent(new IntentResultDetailBO());
                    }
                    IntentResultDetailBO intentResultBOIntent = intentResultBO.getIntent();
                    if (CollectionUtils.isEmpty(intentResultBOIntent.getIntent_ranking())) {
                        intentResultBOIntent.setIntent_ranking(new ArrayList<>());
                    }
                    if (CollectionUtils.isNotEmpty(intentBOList)) {
                        intentResultBOIntent.getIntent_ranking().addAll(intentBOList);
                    }
                    return Mono.justOrEmpty(intentResultBO);
                });
    }

    private static Boolean isEnableNluIntentPredict(Long botId, V3BotTypeEnum botType) {
        if (!V3BotTypeEnum.isCommon(botType)) {
            return true;
        }
        String s = ApplicationConstant.ALGORITHM_NLU_INTENT_PREDICT_ENABLE_BOT_ID_LIST;
        if (StringUtils.isBlank(s)) {
            return false;
        }
        return StringUtils.equals(s, "*") || Arrays.asList(s.split(",")).contains(String.valueOf(botId));
    }

    private static Mono<List<IntentBO>> nluIntentPredictAsync(Long tenantId, Long robotId, String userInput, String lastAiSay, IntentPredictRequiredResource resource, Map<String, String> varNameValueMap) {
        if (!isEnableNluIntentPredict(robotId, resource.getBotType())) {
            return Mono.empty();
        }
        ImmutableMap<String, List<String>> intentNameDescListMap = resource.getIntentNameDescListMap();
        if (MapUtils.isEmpty(intentNameDescListMap)) {
            return Mono.empty();
        }

        List<Map<String, Object>> choiceList = new ArrayList<>();
        intentNameDescListMap.forEach((intentName, descList) -> {
            List<String> descriptions = descList.stream().map(desc -> AnswerRenderUtils.replacePlaceholder(desc, varNameValueMap)).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(descriptions)) {
                Map<String, Object> map = new HashMap<>();
                map.put("value", intentName);
                map.put("descriptions", descriptions);
                choiceList.add(map);
            }
        });
        if (CollectionUtils.isEmpty(choiceList)) {
            return Mono.empty();
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("env", ApplicationConstant.ALGORITHM_REGISTER_ENV);
        paramMap.put("bot_id", robotId);
        paramMap.put("user_content", userInput);
        paramMap.put("service_content", lastAiSay);
        paramMap.put("choices", choiceList);

        String param = JsonUtils.object2String(paramMap);
        long start = System.currentTimeMillis();

        String url = ApplicationConstant.ALGORITHM_NLU_INTENT_PREDICT_URL;
        return webClient.method(HttpMethod.POST)
                .uri(url)
                .body(BodyInserters.fromValue(param))
                .retrieve()
                .bodyToMono(String.class)
                .doOnSubscribe(s -> {
                    logger.info("远程调用NLU算法预测接口, url={}, param={}", url, param);
                })
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .transformDeferred(CircuitBreakerOperator.of(circuitBreaker))
                .doOnSuccess(s -> {
                    long end = System.currentTimeMillis();
                    logger.info("调用NLU算法预测接口结果, cost={}, ms,url:{}, param={}, responseBody={}", end - start, url, param, s);
                })
                .flatMap(s -> {
                    NluRes nluRes = JsonUtils.string2Object(s, NluRes.class);
                    if (nluRes.isSuccess() && CollectionUtils.isNotEmpty(nluRes.getIntents())) {
                        List<IntentBO> resultList = new ArrayList<>();
                        for (NluIntentRes intent : nluRes.getIntents()) {
                            String intentName = intent.getValue();
                            if (StringUtils.isNotBlank(intentName) && StringUtils.isNotBlank(intent.getDescription())) {
                                String intentId = resource.getIntentName2IdMap().get(intentName);
                                if (StringUtils.isNotBlank(intentId)) {
                                    IntentBO intentBO = new IntentBO();
                                    intentBO.setId(intentId);
                                    intentBO.setName(intentName);
                                    if (StringUtils.isBlank(intent.getScore())) {
                                        intentBO.setConfidence("0.0");
                                    } else {
                                        intentBO.setConfidence(intent.getScore());
                                    }
                                    intentBO.setSim_sentence(intent.getDescription());
                                    intentBO.setIsNlu(true);
                                    resultList.add(intentBO);
                                }
                            }
                        }
                        return Mono.just(resultList);
                    }
                    if (!nluRes.isSuccess()) {
                        logger.warn("[LogHub_Warn]调用NLU算法预测接口响应异常, url:{}, param:{}, tenantId={}, robotId={}, responseBody={}", url, param, tenantId, robotId, s);
                    }
                    return Mono.empty();
                })
                .onErrorResume(e -> {
                    if (e instanceof ReadTimeoutException) {
                        logger.warn("[LogHub_Warn]调用NLU算法预测接口超时, url:{}, param:{} tenantId={}, robotId={}, 用户输入={}", url, param, tenantId, robotId, userInput, e);
                    } else {
                        logger.warn("[LogHub_Warn]调用NLU算法预测接口失败, url:{}, param:{}, tenantId={}, robotId={}, 用户输入={}, 失败原因={}", url, param, tenantId, robotId, userInput, e.getMessage(), e);
                    }
                    return Mono.empty();
                });
    }


    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    static class NluRes implements Serializable {

        Integer code;

        List<NluIntentRes> intents;

        public boolean isSuccess() {
            return Objects.nonNull(code) && code == 0;
        }
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    static class NluIntentRes implements Serializable {

        String value;

        String description;

        String score;
    }

    private static Mono<IntentResultBO> sendCustomerModelIntentPredictAsync(Long tenantId, Long robotId, String userInput, boolean isVerbalTraining) {
        Map<String, Object> postMap = prepareParam(tenantId, robotId, userInput);

        String param = JsonUtils.object2String(postMap);
        long start = System.currentTimeMillis();

        String url = isVerbalTraining ? ALGORITHM_VERBAL_PREDICT_URL : ALGORITHM_PREDICT_URL;
        return webClient.method(HttpMethod.POST)
                .uri(url)
                .body(BodyInserters.fromValue(param))
                .retrieve()
                .bodyToMono(String.class)
                .doOnSubscribe(s -> {
//                    logger.info("远程调用算法预测接口, url={}, param={}", url, param);
                })
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .transformDeferred(CircuitBreakerOperator.of(circuitBreaker))
                .doOnSuccess(s -> {
                    long end = System.currentTimeMillis();
                    logger.info("调用算法预测接口结果, cost={}, ms,url:{}, param={}, responseBody={}", end - start, url, param, s);
                })
                .flatMap(s -> Mono.just(JsonUtils.string2Object(s, IntentResultBO.class)))
                .onErrorResume(e -> {
                    if (e instanceof ReadTimeoutException) {
                        logger.warn("[LogHub_Warn]调用算法预测接口超时, url:{}, param:{} tenantId={}, robotId={}, 用户输入={}", url, param, tenantId, robotId, userInput, e);
                    } else {
                        logger.warn("[LogHub_Warn]调用算法预测接口失败, url:{}, param:{}, tenantId={}, robotId={}, 用户输入={}, 失败原因={}", url, param, tenantId, robotId, userInput, e.getMessage(), e);
                    }
                    return Mono.empty();
                });
    }

    private static Map<String, Object> prepareParam(Long tenantId, Long robotId, String userInput) {
        Map<String, Object> postMap = new HashMap<>();
        postMap.put("tenantId", tenantId);
        postMap.put("robotId", robotId);
        postMap.put("text", userInput);
        postMap.put("trainType", "INTENT");
        postMap.put("snapshotType", "PUBLISHED");
        postMap.put("environment", ApplicationConstant.ALGORITHM_REGISTER_ENV);
        return postMap;
    }

    // 是否可以预测，如果是纯英文和数字的组合，则不进行预测
    private static boolean canPredict(String userInput, int inputMinLength) {
        if (StringUtils.length(userInput) < inputMinLength) {
            return false;
        }
        return !userInput.matches(FILTER_PREDICT_PATTERN);
    }

    @Data
    @EqualsAndHashCode
    static class CustomerModelPredictCacheKey {
        Long botId;
        private String userInput;
        Boolean isVerbalTraining;

        public CustomerModelPredictCacheKey(Long botId, String userInput, Boolean isVerbalTraining) {
            this.botId = botId;
            this.userInput = userInput;
            this.isVerbalTraining = isVerbalTraining;
        }
    }

}
