package com.yiwise.dialogflow.engine.chatmanager;

import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.utils.PrintLogUtils;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import com.yiwise.dialogflow.entity.enums.IntentRefTypeEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import reactor.core.publisher.Mono;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;

/**
 * 对话管理器触发条件, 之前是通过if else 的方式
 * 现在尝试抽象出来条件表达式, 一个对话管理器可能会有多个条件, 每个条件有优先级
 * 再调度时, 根据优先级排序后, 选择第一个满足的
 * 这个条件可能是每轮都会动态生成的
 * 每个Condition里面的字段条件都是且的关系
 *
 * <AUTHOR>
 */
@Data
public class ChatManagerTriggerCondition {

    private ChatManagerPriorityEnum type;
    private int priority;

    private Set<String> intentIdSet;

    private Set<ChatEventTypeEnum> chatEventSet;

    private ChatManagerPredicate dynamicPredicate;

    private ChatManagerAsyncPredicate asyncDynamicPredicate;

    private BiConsumer<SessionContext, EventContext> onSelected;

    private String chatManagerName;

    private IntentRefTypeEnum intentRefType;

    private Set<SpecialChatModeEnum> mustMatchModes = new HashSet<>();

    private Set<SpecialChatModeEnum> mustNotMatchModes = new HashSet<>();

    public ChatManagerTriggerCondition(ChatManagerPriorityEnum priority) {
        this.priority = priority.getCode();
        this.type = priority;
    }

    public ConditionMatchResult test(SessionContext sessionContext, EventContext eventContext, RobotRuntimeResource resource) {
        if (CollectionUtils.isNotEmpty(this.getChatEventSet())) {
            if (!this.getChatEventSet().contains(eventContext.getEvent())) {
                return ConditionMatchResult.FALSE(this);
            }
        }
        if (Objects.nonNull(this.getDynamicPredicate()) && !getDynamicPredicate().test(sessionContext, eventContext)) {
            return ConditionMatchResult.FALSE(this);
        }
        if (CollectionUtils.isNotEmpty(mustMatchModes)) {
            Set<SpecialChatModeEnum> allSpecialChatModes = new HashSet<>();
            if (CollectionUtils.isNotEmpty(sessionContext.getSpecialChatModes())) {
                allSpecialChatModes.addAll(sessionContext.getSpecialChatModes());
            }
            if (CollectionUtils.isNotEmpty(eventContext.getSpecialChatModes())) {
                allSpecialChatModes.addAll(eventContext.getSpecialChatModes());
            }
            if (CollectionUtils.isEmpty(allSpecialChatModes) || !allSpecialChatModes.containsAll(mustMatchModes)) {
                return ConditionMatchResult.FALSE(this);
            }
        }
        if (CollectionUtils.isNotEmpty(mustNotMatchModes)) {
            Set<SpecialChatModeEnum> allSpecialChatModes = new HashSet<>();
            if (CollectionUtils.isNotEmpty(sessionContext.getSpecialChatModes())) {
                allSpecialChatModes.addAll(sessionContext.getSpecialChatModes());
            }
            if (CollectionUtils.isNotEmpty(eventContext.getSpecialChatModes())) {
                allSpecialChatModes.addAll(eventContext.getSpecialChatModes());
            }
            if (CollectionUtils.isNotEmpty(allSpecialChatModes)
                    && allSpecialChatModes.stream().anyMatch(mode -> mustNotMatchModes.contains(mode))) {
                return ConditionMatchResult.FALSE(this);
            }
        }
        if (getIntentIdSet() != null) {
            List<PredictResult> candidatePredictResultList = eventContext.getCandidatePredictResultList();
            if (CollectionUtils.isEmpty(candidatePredictResultList)) {
                return ConditionMatchResult.FALSE(this);
            }
            return eventContext.getCandidatePredictResultList().stream()
                    .filter(pr -> getIntentIdSet().contains(pr.getIntentId()))
                    .map(pr -> ConditionMatchResult.of(pr, intentRefType, this))
                    .min(new ConditionPriorityComparator(eventContext,resource,sessionContext)).orElse(ConditionMatchResult.FALSE(this));
        }
        return ConditionMatchResult.TRUE(this);
    }


    //
    public Mono<ConditionMatchResult> asyncTest(SessionContext sessionContext, EventContext eventContext, RobotRuntimeResource resource) {
        ConditionMatchResult result = test(sessionContext, eventContext, resource);
        if (!result.isMatch() || Objects.isNull(asyncDynamicPredicate)) {
            return Mono.just(result);
        }

        // 如果条件匹配, 且 asyncDynamicPredicate 不为空, 则执行异步条件判断
        return asyncDynamicPredicate.test(sessionContext, eventContext)
                .map(isMatch -> isMatch ? result : ConditionMatchResult.FALSE(this));
    }


    public String toPrintLogString() {
        StringBuilder sb = new StringBuilder();
        sb.append("{\"type\": \"").append(type);
        sb.append("\", \"priority\": ").append(priority);
        sb.append(", \"intentIdSet\": "); PrintLogUtils.stringCollectionToPrintLog(sb, intentIdSet);
        sb.append(", \"chatEventSet\": ").append(chatEventSet);
        sb.append(", \"chatManagerName\": \"").append(chatManagerName);
        sb.append("\", \"intentRefType\": \"").append(intentRefType);
        sb.append("\", \"mustMatchModes\": ").append(mustMatchModes);
        sb.append(", \"mustNotMatchModes\": ").append(mustNotMatchModes);
        sb.append("}");

        return sb.toString();
    }

}

