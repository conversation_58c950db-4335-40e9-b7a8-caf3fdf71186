package com.yiwise.dialogflow.engine.service;

import com.yiwise.dialogflow.engine.analysis.OriginChatData;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.CallDataInfo;
import com.yiwise.dialogflow.engine.share.IntentLevelAnalysisResult;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;

public interface BotStatsAnalysisService {

    BotStatsAnalysisResult analysis(RobotRuntimeResource resource,
                                    SessionInfo sessionInfo,
                                    CallDataInfo callDataInfo,
                                    OriginChatData originChatData,
                                    IntentLevelAnalysisResult intentLevelAnalysisResult);
}
