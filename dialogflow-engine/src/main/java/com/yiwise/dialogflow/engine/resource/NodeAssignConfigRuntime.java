package com.yiwise.dialogflow.engine.resource;

import com.yiwise.dialogflow.entity.po.ConstantAssignConfigPO;
import com.yiwise.dialogflow.entity.po.EntityAssignConfigPO;
import com.yiwise.dialogflow.entity.po.OriginInputAssignConfigPO;
import lombok.Data;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

@Data
public class NodeAssignConfigRuntime {

    /**
     * 常量赋值
     */
    private ConstantAssignConfigPO constantAssign;

    /**
     * 实体赋值
     */
    private EntityAssignConfigPO entityAssign;

    /**
     * 原话赋值
     */
    private OriginInputAssignConfigPO originInputAssign;

    private Integer lastActiveSeq;
    /**
     * 当前采集是否开启了采集成功分支意图?
     * 如果启用了, 则采集成功后, 直接命中该意图, 不会再进行意图匹配了
     */
    private Boolean enableCollectSuccessIntent;

    public Set<String> getDependEntityIdSet() {
        Set<String> result = new HashSet<>();

        if (Objects.nonNull(constantAssign)) {
            result.addAll(constantAssign.getDependentEntityIdList());
        }
        if (Objects.nonNull(entityAssign)) {
            result.addAll(entityAssign.getDependentEntityIdSet());
        }
        if (Objects.nonNull(originInputAssign)) {
            result.addAll(originInputAssign.getDependentEntityIdSet());
        }
        return result;
    }
}
