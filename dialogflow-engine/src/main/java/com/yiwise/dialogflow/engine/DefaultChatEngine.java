package com.yiwise.dialogflow.engine;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.engine.chatfilter.*;
import com.yiwise.dialogflow.engine.chatprocessor.*;
import com.yiwise.dialogflow.engine.dispatcher.*;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public class DefaultChatEngine extends AbstractChatEngine {

    private ChatDispatcher defaultDispatcher;

    private RecordLastAnswerPostProcessor recordLastAnswerPostProcessor;

    private ChatDataCollectPostProcessor chatDataCollectPostProcessor;

    private HangupDelayModeCheckPostProcessor hangupDelayModeCheckPostProcessor;

    private InaudibleModeExitPostProcessor inaudibleModeExitPostProcessor;

    private IntentPersistencePostProcessor intentPersistencePostProcessor;

    private ImmediateActionPostProcessor immediateActionPostProcessor;

    private KeyCaptureModeCheckPostProcessor keyCaptureModeCheckPostProcessor;

    public DefaultChatEngine(Long botId, RobotSnapshotUsageTargetEnum usageTarget, RobotRuntimeResource resource) {
        super(botId, usageTarget, resource);
    }

    @Override
    protected void initChatComponent() {
        defaultDispatcher = new DynamicDispatcher(resource);
    }

    @Override
    protected List<ChatFilter> initChatFilter(ChatEventTypeEnum eventType) {
        List<ChatFilter> result = new ArrayList<>();
        switch (eventType) {
            case ENTER:
            case USER_SILENCE:
            case AI_SAY_FINISH:
            case FAST_HANGUP:
                // 这三个事件不涉及到用户输入, 没有过滤逻辑要执行
                break;
            case USER_SAY:
                result.add(new ToneWordChatFilter(resource));
                result.add(new InterruptChatFilter(resource));
                result.add(new NoiseChatFilter(resource));
                result.add(new PauseAudioPlayFilter());
                break;
            case USER_SAY_FINISH:
                result.add(new AsrCorrectionChatFilter(resource));
                result.add(new ToneWordChatFilter(resource));
                result.add(new InterruptChatFilter(resource));
                result.add(new NoiseChatFilter(resource));
                break;
            case DELIVER_HTTP_RESPONSE:
            case KEY_CAPTURE_FAILED:
            case KEY_CAPTURE_SUCCESS:
                break;
            case LLM_REQUEST:
                break;
            default:
                throw new ComException(ComErrorCode.UNKNOWN_ERROR, "未处理的新事件");
        }

        return result;
    }


    @Override
    protected List<ChatPreProcessor> initPreProcessorList(ChatEventTypeEnum eventType) {
        List<ChatPreProcessor> result = new ArrayList<>();
        switch (eventType) {
            case ENTER:
                break;
            case AI_SAY_FINISH:
                AudioPlayProgressProcessor playProgressProcessor = new AudioPlayProgressProcessor(resource);
                result.add(playProgressProcessor);
                break;
            case USER_SAY:
                break;
            case USER_SAY_FINISH:
                AudioPlayProgressProcessor audioPlayProgressProcessor = new AudioPlayProgressProcessor(resource);
                result.add(audioPlayProgressProcessor);

                result.add(new EntityCollectPreProcessor(resource));

                IntentPredictPreProcessor intentPredictPreProcessor = new IntentPredictPreProcessor(resource);
                result.add(intentPredictPreProcessor);

                CheckOnlyReplySpecialPreProcessor checkOnlyReplySpecialPreProcessor = new CheckOnlyReplySpecialPreProcessor(resource);
                result.add(checkOnlyReplySpecialPreProcessor);
                break;
            case USER_SILENCE:
                // 用户无应答了,正常来说录音应该是一定播放完成了
                audioPlayProgressProcessor = new AudioPlayProgressProcessor(resource);
                result.add(audioPlayProgressProcessor);

                UserSilenceIntentPreProcessor userSilenceIntentPreProcessor = new UserSilenceIntentPreProcessor();
                result.add(userSilenceIntentPreProcessor);
            default:
                break;
        }
        return result;
    }

    @Override
    protected ChatDispatcher initChatDispatcher(ChatEventTypeEnum eventType) {
        if (ChatEventTypeEnum.USER_SAY.equals(eventType)) {
            return new UserSayDispatcher(resource);
        }
        if (ChatEventTypeEnum.DELIVER_HTTP_RESPONSE.equals(eventType)) {
            return new HttpResponseDispatcher(resource);
        }
//        if (ChatEventTypeEnum.LLM_REQUEST.equals(eventType)) {
//            return new LLMDispatcher(resource);
//        }
        return defaultDispatcher;
    }

    @Override
    protected List<ChatPostProcessor> initPostProcessorList(ChatEventTypeEnum eventType) {
        List<ChatPostProcessor> result = new ArrayList<>();

        // userSay 等暂不处理
        if (ChatEventTypeEnum.USER_SAY.equals(eventType)
                || ChatEventTypeEnum.DELIVER_HTTP_RESPONSE.equals(eventType)) {
            return result;
        }

        result.add(initRecordLastAnswer());

        switch (eventType) {
            case ENTER:
            case AI_SAY_FINISH:
            case USER_SAY_FINISH:
                result.add(getHangupDelayProcessor());
                result.add(getInaudibleModeExitProcessor());
                result.add(initIntentPersistence());
                break;
            case USER_SILENCE:
            default:
                break;
        }
        result.add(getImmediateActionProcessor());
        result.add(initChatDataCollect());
        result.add(getKeyCaptureModeCheckPostProcessor());
        return result;
    }

    private ChatPostProcessor getImmediateActionProcessor() {
        if (immediateActionPostProcessor == null) {
            immediateActionPostProcessor = new ImmediateActionPostProcessor(resource);
        }
        return immediateActionPostProcessor;
    }

    private ChatPostProcessor getInaudibleModeExitProcessor() {
        if (inaudibleModeExitPostProcessor == null) {
            inaudibleModeExitPostProcessor = new InaudibleModeExitPostProcessor(resource);
        }
        return inaudibleModeExitPostProcessor;
    }

    private ChatPostProcessor getHangupDelayProcessor() {
        if (Objects.isNull(hangupDelayModeCheckPostProcessor)) {
            hangupDelayModeCheckPostProcessor = new HangupDelayModeCheckPostProcessor(resource);
        }
        return hangupDelayModeCheckPostProcessor;
    }

    private ChatPostProcessor getKeyCaptureModeCheckPostProcessor() {
        if (Objects.isNull(keyCaptureModeCheckPostProcessor)) {
            keyCaptureModeCheckPostProcessor = new KeyCaptureModeCheckPostProcessor();
        }
        return keyCaptureModeCheckPostProcessor;
    }

    private ChatDataCollectPostProcessor initChatDataCollect() {
        if (Objects.isNull(chatDataCollectPostProcessor)) {
            chatDataCollectPostProcessor = new ChatDataCollectPostProcessor();
        }
        return chatDataCollectPostProcessor;
    }

    private RecordLastAnswerPostProcessor initRecordLastAnswer() {
        if (Objects.isNull(recordLastAnswerPostProcessor)) {
            recordLastAnswerPostProcessor = new RecordLastAnswerPostProcessor();
        }
        return recordLastAnswerPostProcessor;
    }

    private IntentPersistencePostProcessor initIntentPersistence() {
        if (Objects.isNull(intentPersistencePostProcessor)) {
            intentPersistencePostProcessor = new IntentPersistencePostProcessor(resource);
        }
        return intentPersistencePostProcessor;
    }

}
