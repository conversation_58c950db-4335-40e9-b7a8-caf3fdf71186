package com.yiwise.dialogflow.engine.chatprocessor;

import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;

import java.util.Optional;

/**
 * <AUTHOR>
 */
public class GenerateDebugLogProcessor extends AbstractPostProcessor {

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "生成debugLog";
    }

    @Override
    public Optional<ChatResponse> doProcess(SessionContext sessionContext, EventContext eventContext, ChatResponse chatResponse) {
        // 要生成哪些内容
        return Optional.empty();
    }
}
