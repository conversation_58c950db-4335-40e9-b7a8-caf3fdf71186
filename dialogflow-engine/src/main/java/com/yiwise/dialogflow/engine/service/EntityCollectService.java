package com.yiwise.dialogflow.engine.service;

import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.bo.entity.RuntimeEntityBO;
import com.yiwise.dialogflow.engine.domain.EntityCollectContext;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 实体提取 service
 */
public interface EntityCollectService {

    Mono<List<EntityValueBO>> collectAsync(String inputText, List<RuntimeEntityBO> entityList);

    Mono<List<EntityValueBO>> collectAsync(String inputText, List<RuntimeEntityBO> entityList, EntityCollectContext entityCollectContext);

    List<EntityValueBO> test(Long botId, String userInput);
}