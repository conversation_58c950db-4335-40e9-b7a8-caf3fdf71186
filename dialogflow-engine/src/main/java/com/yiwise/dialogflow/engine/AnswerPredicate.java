package com.yiwise.dialogflow.engine;

import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.entity.po.BaseAnswerContent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;

import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @date 2022/5/30
 */
@Slf4j
public class AnswerPredicate extends AbstractConditionPredicate<BaseAnswerContent> implements Predicate<BaseAnswerContent> {

    public AnswerPredicate(RobotRuntimeResource robotRuntimeResource, SessionContext sessionContext) {
        super(robotRuntimeResource, sessionContext, null);
    }

    @Override
    public boolean test(BaseAnswerContent baseAnswerContent) {
        if (baseAnswerContent == null) {
            return false;
        }
        Boolean enableVarCondition = baseAnswerContent.getEnableVarCondition();
        if (BooleanUtils.isNotTrue(enableVarCondition)) {
            return true;
        }
        return super.doTest(baseAnswerContent);
    }

}
