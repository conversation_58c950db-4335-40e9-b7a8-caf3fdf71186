package com.yiwise.dialogflow.engine.resource;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.yiwise.dialogflow.entity.po.intent.IntentRulePO;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class IntentLevelRuleResource {

    /**
     * 节点id对应的意向等级code
     */
    ImmutableMap<String, Integer> nodeId2IntentLevelCodeMap;

    /**
     * 问答知识id对应设置的意向等级code
     */
    ImmutableMap<String, Integer> knowledgeId2IntentLevelCodeMap;

    /**
     * 特殊语境管理的意向等级
     */
    ImmutableMap<String, Integer> specialAnswerId2IntentLevelCodeMap;

    /**
     * 意向规则列表
     */
    ImmutableList<IntentRulePO> intentRuleList;

    /**
     * 意向标签code对应的名称
     */
    ImmutableMap<Integer, String> intentLevelCode2NameMap;

    boolean hasActivityCondition;

    boolean hasPrivateDomainCondition;

    /**
     * 意向规则中是否配置了被动加微
     */
    boolean hasPassivePrivateDomainCondition;

    /**
     * 意向规则中是否配置了教育活动通知
     */
    boolean hasEducationActivityCondition;
}
