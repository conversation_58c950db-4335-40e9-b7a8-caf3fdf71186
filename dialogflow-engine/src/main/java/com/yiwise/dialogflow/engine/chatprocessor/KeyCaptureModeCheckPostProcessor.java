package com.yiwise.dialogflow.engine.chatprocessor;

import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
public class KeyCaptureModeCheckPostProcessor extends AbstractPostProcessor {
    @Override
    public void initContext(SessionContext sessionContext) {
    }

    @Override
    public String getName() {
        return "按键采集状态检测";
    }

    @Override
    protected Optional<ChatResponse> doProcess(SessionContext sessionContext, EventContext eventContext, ChatResponse chatResponse) {
        AnswerResult answer = chatResponse.getAnswer();
        if (Objects.isNull(answer)) {
            return Optional.of(chatResponse);
        }

        if (Objects.nonNull(answer.getKeyCaptureConfig())) {
            log.info("检测到按键采集指令, 进入到按键采集模式, 指令来源={}", chatResponse.getAnswerLocate());
            sessionContext.getSpecialChatModes().add(SpecialChatModeEnum.KEY_CAPTURE);
        } else {
            sessionContext.getSpecialChatModes().remove(SpecialChatModeEnum.KEY_CAPTURE);
        }
        return Optional.of(chatResponse);
    }
}
