package com.yiwise.dialogflow.engine.chatprocessor;

import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.request.UserSayFinishEvent;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public class AudioPlayProgressProcessor extends AbstractChatPreProcessor {

    private final RobotRuntimeResource resource;

    public AudioPlayProgressProcessor(RobotRuntimeResource resource) {
        this.resource = resource;
    }

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "记录录音播放进度";
    }

    @Override
    public void doProcess(SessionContext sessionContext, EventContext eventContext) {
//        if (eventContext.isMergeRequest() && StringUtils.isBlank(eventContext.getLastAnswerId())) {
//            log.info("断句补齐请求, 因状态已经回退, 所以此时答案已对应不上, 忽略进度更新");
//            return;
//        }
        String clientPlayLastAnswerId = eventContext.getClientLastPlayAnswerId();
        String lastAnswerId = sessionContext.getLastAnswerId();
        // 如果服务端答案和客户端答案不一致, 说明断句补齐时, 客户端播放了一部分新的答案, 但是该答案目前需要回退了
        if (eventContext.isMergeRequest() &&
                StringUtils.isNotBlank(clientPlayLastAnswerId)
                && !clientPlayLastAnswerId.equals(lastAnswerId)) {
            log.info("断句补齐时, 状态回退且答案被切换, 此时无法更新当前答案播放进度, 客户端侧答案Id:{}, 服务端答案Id:{}",
                    clientPlayLastAnswerId, lastAnswerId);
            return;
        }
        double progress = 0;
        switch (eventContext.getEvent()) {
            case USER_SAY_FINISH:
                UserSayFinishEvent request = (UserSayFinishEvent) eventContext.getOriginEventParam();
                if (StringUtils.isNotBlank(lastAnswerId)) {
                    progress = request.getPlayProgress();
                }
                break;
            case AI_SAY_FINISH:
            case USER_SILENCE:
                if (StringUtils.isNotBlank(lastAnswerId)) {
                    progress = ApplicationConstant.AUDIO_PLAY_FINISH_PROGRESS;
                }
                break;
            default:
                return;
        }
        if (StringUtils.isNotBlank(lastAnswerId)) {
            Double oldProgress = sessionContext.getAnswerProgressMap().get(lastAnswerId);
            if (Objects.isNull(oldProgress) || oldProgress != progress) {
                // 更新
                sessionContext.getAnswerProgressMap().put(lastAnswerId, progress);
                if (Objects.isNull(oldProgress) || progress > oldProgress) {
                    AnswerLocateBO answerLocateBO = resource.getAnswerId2LocateMap().get(lastAnswerId);
                    if ( Objects.nonNull(answerLocateBO)
                            && !(Objects.isNull(answerLocateBO.getKnowledgeId())
                            && Objects.isNull(answerLocateBO.getNodeId()))) {
                        if (Objects.nonNull(answerLocateBO.getNodeId())) {
                            if (ApplicationConstant.enableDebug) {
                                log.debug("更新节点答案录音最大播放进度,nodeId={},progress={}", answerLocateBO.getNodeId(), progress);
                            }
                            sessionContext.getNodeAnswerMaxProgressMap().put(answerLocateBO.getNodeId(), progress);
                        } else {
                            if (ApplicationConstant.enableDebug) {
                                log.debug("更新知识答案录音最大播放进度,knowledgeId={},progress={}", answerLocateBO.getKnowledgeId(), progress);
                            }
                            sessionContext.getKnowledgeAnswerMaxProgressMap().put(answerLocateBO.getKnowledgeId(), progress);
                        }
                    }
                }
                if (ApplicationConstant.enableDebug) {
                    log.debug("更新录音播放进度, answerId={}, progress={}, oldProgress={}", lastAnswerId, progress, oldProgress);
                }
            }
        } else {
            log.info("更新录音播放进度失败, 不存在最近播放的答案");
        }

    }
}
