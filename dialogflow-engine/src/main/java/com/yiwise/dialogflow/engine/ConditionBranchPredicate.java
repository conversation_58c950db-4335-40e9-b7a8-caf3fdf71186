package com.yiwise.dialogflow.engine;

import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.entity.po.NodeConditionBranchPO;

import java.util.function.Predicate;

public class ConditionBranchPredicate extends AbstractConditionPredicate<NodeConditionBranchPO> implements Predicate<NodeConditionBranchPO> {

    public ConditionBranchPredicate(RobotRuntimeResource robotRuntimeResource, SessionContext sessionContext, EventContext eventContext) {
        super(robotRuntimeResource, sessionContext, eventContext);
    }

    @Override
    public boolean test(NodeConditionBranchPO nodeConditionBranchPO) {
        return super.doTest(nodeConditionBranchPO);
    }
}
