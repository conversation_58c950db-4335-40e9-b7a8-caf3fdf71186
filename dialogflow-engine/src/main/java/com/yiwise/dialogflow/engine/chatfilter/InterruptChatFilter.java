package com.yiwise.dialogflow.engine.chatfilter;

import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.MdcLogIdLifterTransformer;
import com.yiwise.dialogflow.engine.chatmanager.PredictResultComparator;
import com.yiwise.dialogflow.engine.chatprocessor.IntentPredictPreProcessor;
import com.yiwise.dialogflow.engine.context.AnswerInterruptConfig;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.action.ChatAction;
import com.yiwise.dialogflow.engine.share.enums.ActionTypeEnum;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.request.EventParam;
import com.yiwise.dialogflow.engine.share.request.UserSayEvent;
import com.yiwise.dialogflow.engine.share.response.ActiveManagerInfo;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.utils.ChatResponseGenerateUtils;
import com.yiwise.dialogflow.engine.utils.DebugLogUtils;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.*;

/**
 * 打断过滤器
 */
@Slf4j
public class InterruptChatFilter extends AbstractChatFilter {

    private static final WebClient webClient = (WebClient) AppContextUtils.getBean("interruptWebClient");

    private final RobotRuntimeResource resource;

    public InterruptChatFilter(RobotRuntimeResource resource) {
        this.resource = resource;
    }
    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "打断过滤";
    }

    @Override
    public Optional<ChatResponse> doFilter(SessionContext sessionContext, EventContext eventContext) {
        Double aiProgress = null;
        EventParam originParam = eventContext.getOriginEventParam();
        if (originParam instanceof UserSayEvent) {
            UserSayEvent userSayEvent = (UserSayEvent) originParam;
            aiProgress = userSayEvent.getPlayProgress();
        }

        if (Objects.isNull(aiProgress)) {
            // 未获取到进度，直接放行
            log.debug("未获取到进度，直接放行");
            return Optional.empty();
        }

        // 放音完成可以打断
        if (aiProgress >= ApplicationConstant.AUDIO_PLAY_FINISH_PROGRESS) {
            return Optional.empty();
        }

        // 获取当前答案配置的打断选项,
        AnswerInterruptConfig interruptConfig = sessionContext.getLastAnswerInterruptConfig();

        if (Objects.isNull(interruptConfig)) {
            log.warn("interruptConfig为空, 直接放行");
            return Optional.empty();
        }

        AnswerLocateBO locate = resource.getAnswerId2LocateMap().get(sessionContext.getLastAnswerId());
        String typeDesc = "流程";
        if (Objects.nonNull(locate) && Objects.nonNull(locate.getAnswerSource())) {
            typeDesc = locate.getAnswerSource().getDesc();
        }

        Boolean uninterrupted = interruptConfig.getUninterrupted();
        Integer threshold = interruptConfig.getCustomInterruptThreshold();

        if (BooleanUtils.isTrue(uninterrupted)) {
            // 不可打断
            // 判断放音进度是否达到可打断的阈值
            if (threshold == null) {
                threshold = 100;
            }
            log.info(typeDesc + "设置了打断阈值={}, 当前播放进度{}", threshold, aiProgress);
            if (aiProgress < threshold) {
                if (BooleanUtils.isTrue(interruptConfig.getNeedTryReplyOnUninterrupted())
                        && ChatEventTypeEnum.USER_SAY_FINISH.equals(eventContext.getEvent())) {
                    log.info("不可打断，但是需要尝试回复, 进行放行处理");
                    return Optional.empty();
                }
                String debugLog = "当前进度" + String.format("%.2f", aiProgress) + "%, 不支持打断";
                DebugLogUtils.commonDebugLog(eventContext, debugLog);
                return Optional.of(ChatResponseGenerateUtils.generateUninterruptedResponse());
            }
        }

        return Optional.empty();
    }

    /**
     * 请求算法接口判断是否可以打断
     */
    private Mono<ChatResponse> checkCanInterruptByAlgorithm(SessionContext sessionContext, EventContext eventContext, ChatResponse response) {
        if (response == null) {
            ActiveManagerInfo activeManagerInfo = sessionContext.getActiveManagerInfo();
            if (eventContext.getOriginEventParam() instanceof UserSayEvent
                    && Objects.nonNull(activeManagerInfo) && activeManagerInfo.isLLM()) {
                UserSayEvent userSayEvent = (UserSayEvent) eventContext.getOriginEventParam();
                if (Objects.nonNull(userSayEvent.getPlayProgress()) && userSayEvent.getPlayProgress() < ApplicationConstant.AUDIO_PLAY_FINISH_PROGRESS) {
                    return requestCanInterrupt(resource.getBotId(), resource.getName(), eventContext)
                            .flatMap(canInterrupt -> {
                                if (BooleanUtils.isTrue(canInterrupt)) {
                                    return Mono.empty();
                                }
                                String debugLog = "算法判断当前不可打断";
                                DebugLogUtils.commonDebugLog(eventContext, debugLog);
                                return Mono.just(ChatResponseGenerateUtils.generateUninterruptedResponse());
                            });
                }

            }
        }
        return Mono.justOrEmpty(response);
    }

    private Mono<Boolean> requestCanInterrupt(Long botId, String botName, EventContext eventContext) {
        String url = ApplicationConstant.ALGORITHM_INTERRUPT_URL;
        UserSayEvent userSayEvent = (UserSayEvent) eventContext.getOriginEventParam();

        Map<String, Object> param = new HashMap<>();

        param.put("request_id", eventContext.getLogId());
        param.put("bot_id", botId);
        param.put("bot_name", botName);
        param.put("user_content", userSayEvent.getInputText() == null ? "" : userSayEvent.getInputText());
        param.put("robot_content", userSayEvent.getLastAnswerPlayedContent() == null ? "" : userSayEvent.getLastAnswerPlayedContent());

        log.debug("请求算法打断判断接口, url:{}, request:{}", url, JsonUtils.object2String(param));
        long start = System.currentTimeMillis();
        return webClient.post()
                .uri(url)
                .body(BodyInserters.fromValue(JsonUtils.object2String(param)))
                .retrieve()
                .bodyToMono(String.class)
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .map(body -> {
                    long end = System.currentTimeMillis();
                    log.debug("请求算法打断判断接口, 耗时:{}, url:{}, request:{}, result:{}", (end - start), url, JsonUtils.object2String(param), body);
                    Map<String, Object> result = JsonUtils.string2MapObject(body, String.class, Object.class);
                    if (Integer.valueOf(0).equals(result.get("code"))
                            && Integer.valueOf(0).equals(result.get("interrupt_label"))) {
                        return false;
                    }
                    return true;
                })
                .switchIfEmpty(Mono.defer(() -> Mono.just(true)))
                .onErrorResume((throwable) -> {
                    log.warn("[LogHub_Warn]请求算法打断判断接口异常, url:{}", url, throwable);
                    return Mono.just(true);
                });
    }

    @Override
    protected Mono<ChatResponse> doFilterAsync(SessionContext sessionContext, EventContext eventContext) {
        // 这里如果 response 为空, 则再判断最后播放的答案是否是大模型生成的, 如果是, 则请求算法接口来判断是否允许打断
        Mono<ChatResponse> finalResponse = checkCanInterruptByAlgorithm(sessionContext, eventContext, doFilter(sessionContext, eventContext).orElse(null));

        return finalResponse.flatMap(chatResponse -> {
            if (chatResponse != null && BooleanUtils.isTrue(resource.getContainsIntentHitRule())) {
                List<ChatAction> actionList = chatResponse.getActionList();
                if (CollectionUtils.isNotEmpty(actionList)
                        && ChatEventTypeEnum.USER_SAY_FINISH.equals(eventContext.getEvent())
                        && actionList.stream().anyMatch(action -> action.getType().equals(ActionTypeEnum.UNINTERRUPTED))) {
                    return new IntentPredictPreProcessor(resource).processAsync(sessionContext, eventContext)
                            .then(Mono.defer(
                                            () -> {
                                                List<PredictResult> candidatePredictResultList = eventContext.getCandidatePredictResultList();
                                                if (CollectionUtils.isNotEmpty(candidatePredictResultList)) {
                                                    candidatePredictResultList.stream()
                                                            .filter(pr -> !Objects.equals(pr.getIntentId(), ApplicationConstant.COLLECT_SUCCESS_INTENT_ID))
                                                            .min(new PredictResultComparator())
                                                            .ifPresent(predictResult -> {
                                                                eventContext.setUninterruptedPredictResult(predictResult);
                                                                DebugLogUtils.uninterruptedPredictDetail(eventContext, predictResult);
                                                            });
                                                }
                                                return Mono.empty();
                                            }
                                    )
                            ).then(Mono.just(chatResponse));
                }
            }
            return Mono.justOrEmpty(chatResponse);
        }).switchIfEmpty(Mono.empty());
    }
}
