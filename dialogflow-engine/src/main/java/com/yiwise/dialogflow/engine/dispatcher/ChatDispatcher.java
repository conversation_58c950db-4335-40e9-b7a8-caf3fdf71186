package com.yiwise.dialogflow.engine.dispatcher;

import com.yiwise.dialogflow.engine.ChatComponent;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import reactor.core.publisher.Flux;

/**
 * 根据sessionContent和eventContext中的结果进行chatManager的分发处理
 * 比如命中了问答知识, 则调用KnowledgeManager, 如果命中了用户无应答, 则使用UserSilenceManager进行处理等等以此类推
 * 同时可以只处理某个事件, 或者处理多个事件, 只需要在DefaultChatEngine时构造Pipeline的时候选择好对应的就可以了
 * <AUTHOR>
 */
public interface ChatDispatcher extends ChatComponent {

    /*
    * 目前的四种事件来说, userSilence事件, 是可以作为意图触发来实现的,
    * aiSayFinish往往涉及到chatManager之间的跳转, 一般交由原来的chatManager先进行处理, 如果需要跳转, 再调用跳转后的chatManager进行处理之后, 返回后者的response
    * userSayFinish是最复杂的用户输入, 预测之后选择对应的ChatManager进行处理
    *
    * 对于跳转的逻辑, 一般只涉及到跳转回流程来处理
    */
    Flux<ChatResponse> dispatchAsync(SessionContext sessionContext, EventContext eventContext);

    Flux<ChatResponse> dispatchLLMEventAsync(SessionContext sessionContext, EventContext eventContext);
}
