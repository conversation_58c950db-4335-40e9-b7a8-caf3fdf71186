package com.yiwise.dialogflow.engine.chatmanager;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 */
public enum ChatManagerPriorityEnum implements CodeDescEnum {
    NOISE_IGNORE(110, "噪音过滤"),
    IMMEDIATELY_HANGUP(110, "延迟挂断状态下, 命中排除的意图时, 立即挂断"),
    MORE_THAN_DELAY_COUNT(108, "延迟挂机状态下, 超过允许的延迟次数"),
    USER_SILENCE_ON_DELAY_HANGUP(107, "延迟挂机时, 等待用户应答期间触发用户无应答"),
    KEY_CAPTURE(106, "按键采集"),

    NODE_MATCH_COLLECT_SUCCESS(105, "节点采集成功"),
    LLM_SPECIAL_ANSWER(101, "大模型特殊语境"),
    INTENT_TRIGGER_KNOWLEDGE(100, "通过意图触发知识"),
    INTENT_TRIGGER_SPECIAL_ANSWER(100, "意图触发特殊语境"),
    INTENT_TRIGGER_STEP(100, "意图触发流程"),
    STEP_MATCH_NEXT_NODE(100, "意图命中分支"),

    KEY_CAPTURE_NO_MATCH_INTENT(97, "按键采集模式下，未触发流程和知识"),
    DELAY_HANGUP(96, "延时挂断且未通过意图触发问答知识/流程"),
    FAST_HANGUP(95, "快速挂断"),
    INAUDIBLE_REPEAT(94, "听不清模式下重复"),

    COLLECT_NODE_CURRENT_ENTITY_SUCCESS(93, "信息采集节点当前实体采集成功"),
    COLLECT_NODE_SKIP_CONDITION(93, "信息采集节点命中跳过意图"),

    INTERRUPT_LLM_SPECIAL_ANSWER(90, "中断大模型特殊语境"),
    INTERRUPT_LLM_STEP_FLOW(90, "中断大模型流程"),

    AI_UNKNOWN_AND_INTERRUPT(85, "机器人未知说话且中断"),
    LLM_STEP_FLOW(82, "大模型流程"),
    DEFAULT_LLM_SPECIAL_ANSWER(81, "大模型特殊语境"),
    STEP_NODE_DEFAULT_INTENT(80, "流程节点默认"),
    STEP_NODE_USER_SILENCE_INTENT(80, "流程节点用户无应答"),
    LLM_STEP_USER_SILENCE_INTENT(80, "大模型用户无应答"),

    USER_SILENCE_TRIGGER_SPECIAL_ANSWER(70, "用户无应答"),
    DEFAULT(0, "兜底")
    ;

    ChatManagerPriorityEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    private int code;
    private String desc;

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
