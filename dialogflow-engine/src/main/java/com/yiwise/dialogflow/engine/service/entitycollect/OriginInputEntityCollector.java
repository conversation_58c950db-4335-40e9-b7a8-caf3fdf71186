package com.yiwise.dialogflow.engine.service.entitycollect;

import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.enums.SystemEntityCategoryEnum;
import reactor.core.publisher.Flux;

import java.util.Map;

public class OriginInputEntityCollector implements SystemEntityCollector {
    @Override
    public void register(Map<SystemEntityCategoryEnum, SystemEntityCollector> registry) {
        registry.put(SystemEntityCategoryEnum.ORIGIN_INPUT, this);
    }

    @Override
    public Flux<EntityValueBO> asyncCollect(SystemEntityCategoryEnum category, String userInput, String userInputPinyin) {
        EntityValueBO value = new EntityValueBO();
        value.setValue(userInput);
        value.setOriginValue(userInput);
        value.setStartOffset(0);
        value.setEndOffset(userInput.length() - 1);
        value.setSystemEntityCategory(category);
        return Flux.just(value);
    }
}
