package com.yiwise.dialogflow.engine.analysis;

import lombok.Data;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/9/26
 * @class <code>RoleTalkContent</code>
 * @see
 * @since JDK1.8
 */
@Data
public class RoleTalkContent {
    /**
     * 说话角色
     */
    private String role;

    /**
     * 说话内容
     */
    private String text;

    /**
     * 算法侧判断意图
     */
    private String algorithm_intent;

    /**
     * 算法侧意图属性
     */
    private String algorithm_intent_attribute;

    /**
     * 最终匹配的意图
     */
    private String use_intent;

    /**
     * 客服播报内容完整度
     */
    private Double complete_situation;

    /**
     * 客服播报内容是否允许打断
     */
    private Boolean allow_break;

    /**
     * 话术允许打断比例
     */
    private Integer allow_break_rate;
}
