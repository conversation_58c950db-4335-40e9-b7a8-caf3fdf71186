package com.yiwise.dialogflow.engine.resource;

import com.yiwise.dialogflow.entity.bo.algorithm.IntentResultDetailBO;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/4
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IntentPredictCompleteResult implements Serializable {

    /**
     * 算法意图预测最终结果
     */
    IntentResultDetailBO intentResultDetail;

    /**
     * 最终候选集
     */
    List<PredictResult> candidatePredictResultList;
}
