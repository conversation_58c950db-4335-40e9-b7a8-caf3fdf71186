package com.yiwise.dialogflow.engine.service.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.common.MdcLogIdLifterTransformer;
import com.yiwise.dialogflow.engine.analysis.AlgorithmIntentLevelResult;
import com.yiwise.dialogflow.engine.service.IntentLevelPredictService;
import javaslang.Tuple;
import javaslang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class IntentLevelPredictServiceImpl implements IntentLevelPredictService {

    @Resource(name = "lowLatencyRestTemplate")
    private RestTemplate lowLatencyRestTemplate;

    @Resource
    private WebClient webClient;

    @Override
    public Optional<Tuple2<Integer, String>> predict(Map<String, Object> callDetailDTO, String predictServerUrl) {
        if (MapUtils.isEmpty(callDetailDTO)) {
            log.info("算法预测意向等级接口callDetailDTO为空");
            return Optional.empty();
        }
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("Content-Type", "application/json");
            HttpEntity<String> requestEntity = new HttpEntity<>(JsonUtils.object2String(callDetailDTO), httpHeaders);
            log.info("请求算法判断意向等级接口,url={},body={}", predictServerUrl, JSON.toJSONString(callDetailDTO));
            ResponseEntity<String> response = lowLatencyRestTemplate.postForEntity(predictServerUrl, requestEntity, String.class);
            log.info("请求算法判断意向等级接口结果为:response={}", response);
            if (!HttpStatus.OK.equals(response.getStatusCode())) {
                log.warn("[LogHub_Warn]调用算法判断意向等级接口失败, url={}, body={}, 失败原因={}", predictServerUrl, JsonUtils.object2String(callDetailDTO), response);
                return Optional.empty();
            }
            try {
                String responseBody = String.valueOf(response.getBody());
                AlgorithmIntentLevelResult result = JsonUtils.string2Object(responseBody, new TypeReference<AlgorithmIntentLevelResult>() {});
                return Optional.of(Tuple.of(result.getCode(), result.getResult()));
            } catch (Exception e) {
                log.warn("[LogHub_Warn]无法判断通话内容意向标签", e);
                return Optional.empty();
            }
        } catch (Exception e) {
            log.warn("[LogHub_Warn]调用算法判断意向等级接口失败, url={}, body={}, 失败原因={}", predictServerUrl, JsonUtils.object2String(callDetailDTO), e.getMessage());
            return Optional.empty();
        }
    }

    @Override
    public Mono<Tuple2<Integer, String>> predictAsync(Map<String, Object> callDetailDTO, String predictServerUrl) {
        if (MapUtils.isEmpty(callDetailDTO)) {
            log.info("算法预测意向等级接口callDetailDTO为空");
            return Mono.empty();
        }
        log.info("请求算法判断意向等级接口,url={},body={}", predictServerUrl, JSON.toJSONString(callDetailDTO));
        return webClient.method(HttpMethod.POST)
                .uri(predictServerUrl)
                .body(BodyInserters.fromValue(JsonUtils.object2String(callDetailDTO)))
                .retrieve()
                .bodyToMono(String.class)
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .doOnNext(response -> {
                    log.info("请求算法判断意向等级接口结果为:response={}", response);
                })
                .map(json -> {
                    AlgorithmIntentLevelResult algorithmResult = JsonUtils.string2Object(json, new TypeReference<AlgorithmIntentLevelResult>() {});
                    return Tuple.of(algorithmResult.getCode(), algorithmResult.getResult());
                })
                .onErrorResume(e -> {
                    log.warn("[LogHub_Warn]调用算法判断意向等级接口失败, url={}, body={}, 失败原因={}", predictServerUrl, JsonUtils.object2String(callDetailDTO), e.getMessage());
                    return Mono.empty();
                });
    }
}
