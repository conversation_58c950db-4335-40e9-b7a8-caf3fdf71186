package com.yiwise.dialogflow.engine.chatprocessor;

import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.helper.IntentPredictHelper;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.IntentPredictRequiredResource;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.entity.po.intent.IntentConfigPO;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

import java.util.Objects;

/**
 * 动态调整阈值的意图预测逻辑
 */
@Slf4j
public class DynamicThresholdIntentPredictPreProcessor extends IntentPredictPreProcessor {
    @Setter
    private Double upperThreshold;
    @Setter
    private Double lowerThreshold;
    public DynamicThresholdIntentPredictPreProcessor(RobotRuntimeResource resource) {
        super(resource);
    }

    @Override
    protected Mono<Void> doProcessAsync(SessionContext sessionContext, EventContext eventContext) {
        if (eventContext.isReusePreStepPredictResult()) {
            log.info("复用前面步骤的预测结果, candidatePredictResultList:{}, candidateIntentIdList:{}", eventContext.getCandidatePredictResultList(), eventContext.getCandidateIntentIdList());
            doAfterIntentPredict(eventContext.getCandidatePredictResultList(), eventContext, sessionContext);
            return Mono.empty();
        }

        // 这里每次都生成新的IntentPredictRequiredResource
        IntentPredictRequiredResource oldResource = resource.getIntentPredictRequiredResource();
        IntentConfigPO intentConfigPO = MyBeanUtils.copy(oldResource.getIntentConfig(), IntentConfigPO.class);
        if (Objects.nonNull(upperThreshold)) {
            intentConfigPO.setAlgorithmUpperThreshold(upperThreshold);
        }
        if (Objects.nonNull(lowerThreshold)) {
            intentConfigPO.setAlgorithmLowerThreshold(lowerThreshold);
        }
        Long intentModelTemplateBotId = resource.getBotId();
        if (V3BotTypeEnum.isMagic(resource.getType()) && Objects.nonNull(resource.getMagicTemplateId())) {
            intentModelTemplateBotId = resource.getMagicTemplateId();
        }
        IntentPredictRequiredResource intentPredictRequiredResource = IntentPredictRequiredResource.builder()
                .botId(resource.getBotId())
                .intentModelTemplateBotId(intentModelTemplateBotId)
                .usageTarget(resource.getUsageTarget())
                .enableCompositeIntent(true)
                .intentConfig(intentConfigPO)
                .keywordList(oldResource.getKeywordList())
                .pinyinKeywordList(oldResource.getPinyinKeywordList())
                .compositeIntentMap(oldResource.getCompositeIntentMap())
                .singIntentMap(oldResource.getSingIntentMap())
                .orderByRequireLengthKeywordList(oldResource.getOrderByRequireLengthKeywordList())
                .requireInputLengthIndex(oldResource.getRequireInputLengthIndex())
                .intentName2IdMap(resource.getIntentPredictRequiredResource().getIntentName2IdMap())
                .build();

        return IntentPredictHelper.predictAsync(eventContext.getUserInput(), intentPredictRequiredResource, SessionContextHelper.getLastAiSay(sessionContext, resource))
                .doOnSuccess(result -> {
                    eventContext.setAlgorithmPredictIntent(result.getIntentResultDetail());
                    doAfterIntentPredict(result.getCandidatePredictResultList(), eventContext, sessionContext);
                }).then();
    }
}
