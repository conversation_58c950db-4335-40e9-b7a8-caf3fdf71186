package com.yiwise.dialogflow.engine.chatmanager;

import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import com.yiwise.dialogflow.entity.enums.*;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Comparator;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/8/18
 */
@Data
@AllArgsConstructor
public class ConditionPriorityComparator implements Comparator<ConditionMatchResult> {

    private EventContext eventContext;

    private RobotRuntimeResource resource;

    private SessionContext sessionContext;

    @Override
    public int compare(ConditionMatchResult o1, ConditionMatchResult o2) {
        int p1 = o1.getCondition().getPriority();
        int p2 = o2.getCondition().getPriority();
        if (p1 != p2) {
            return p2 - p1;
        }

        PredictResult pr1 = o1.getPredictResult();
        PredictResult pr2 = o2.getPredictResult();

        if (Objects.isNull(pr2) && Objects.isNull(pr1)) {
            return 0;
        }
        if (Objects.isNull(pr1)) {
            return -1;
        }
        if (Objects.isNull(pr2)) {
            return 1;
        }

        IntentPriorityLevelEnum l1 = Optional.ofNullable(pr1.getIntentPriorityLevel()).orElse(IntentPriorityLevelEnum.DEFAULT);
        IntentPriorityLevelEnum l2 = Optional.ofNullable(pr2.getIntentPriorityLevel()).orElse(IntentPriorityLevelEnum.DEFAULT);
        if (!l1.equals(l2)) {
            return l1.getCode() - l2.getCode();
        }

        if (!pr1.getConfidence().equals(pr2.getConfidence())) {
            return pr1.getConfidence() - pr2.getConfidence() > 0 ? -1 : 1;
        }

        boolean isSameIntent = Objects.equals(pr1.getIntentId(), pr2.getIntentId());

        int c1 = convert(o1, isSameIntent);
        int c2 = convert(o2, isSameIntent);
        if (c1 != c2) {
            return c1 - c2;
        } else if (PredictTypeEnum.REGEX.equals(pr1.getPredictType())) {
            return pr2.getPattern().pattern().length() - pr1.getPattern().pattern().length();
        } else {
            return 0;
        }
    }

    private int convert(ConditionMatchResult cmr, boolean isSameIntent) {
        String matchIntentId = cmr.getMatchIntentId();
        IntentRefTypeEnum intentRefType = cmr.getIntentRefType();
        if (IntentRefTypeEnum.NODE.equals(intentRefType)) {
            return isSameIntent && SessionContextHelper.getCurrentNodeEnableIntentFirst(resource, sessionContext) ? 0 : 3;
        }
        if (IntentRefTypeEnum.KNOWLEDGE.equals(intentRefType)) {
            return KnowledgeCategoryEnum.BUSINESS.equals(resource.getIntent2KnowledgeMap().get(matchIntentId).getCategory()) ? 1 : 2;
        }
        if (IntentRefTypeEnum.STEP.equals(intentRefType)) {
            return StepCategoryEnum.BUSINESS.equals(resource.getIntent2StepMap().get(matchIntentId).getCategory()) ? 1 : 2;
        }
        return 2;
    }

}
