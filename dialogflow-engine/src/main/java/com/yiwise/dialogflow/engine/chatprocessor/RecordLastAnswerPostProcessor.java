package com.yiwise.dialogflow.engine.chatprocessor;

import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.context.*;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 记录最近回复的答案信息, 在重复上一句的时候回用到, 更新的是SessionContext中的lastAnswer状态
 * <AUTHOR>
 */
@Slf4j
public class RecordLastAnswerPostProcessor extends AbstractPostProcessor {

    private static final Set<AnswerSourceEnum> REPEATABLE_SOURCE_SET = new HashSet<>(Arrays.asList(AnswerSourceEnum.KNOWLEDGE, AnswerSourceEnum.STEP));

    @Override
    public void initContext(SessionContext sessionContext) {
    }

    @Override
    public String getName() {
        return "记录最后回复的答案";
    }

    @Override
    public Optional<ChatResponse> doProcess(SessionContext sessionContext, EventContext eventContext, ChatResponse chatResponse) {
        if (Objects.nonNull(chatResponse.getAnswer())) {
            sessionContext.setLastAnswerId(chatResponse.getAnswer().getId());
            AnswerResult answer = chatResponse.getAnswer();
            AnswerInterruptConfig interruptConfig = AnswerInterruptConfig.builder()
                    .customInterruptThreshold(answer.getCustomInterruptThreshold())
                    .uninterrupted(answer.getUninterrupted())
                    .needTryReplyOnUninterrupted(answer.getNeedTryReplyOnUninterrupted())
                    .build();
            sessionContext.setLastAnswerInterruptConfig(interruptConfig);
            // 更新ai重复可以重播的答案信息
            updateAiRepeatAnswer(sessionContext, eventContext, chatResponse.getAnswer());

            // 更新听不清可以重播的答案信息
            updateInaudibleRepeatAnswer(sessionContext, eventContext, chatResponse.getAnswer());
        }
        return Optional.of(chatResponse);
    }

    private void updateInaudibleRepeatAnswer(SessionContext sessionContext, EventContext eventContext, AnswerResult answer) {
        if (isInaudibleRepeatable(answer, eventContext.getPredictResult())) {
            AnswerLocateBO locate = answer.getLocate();
            if (Objects.nonNull(locate)) {
                InaudibleContext inaudibleContext = sessionContext.getInaudibleContext();
                if (Objects.nonNull(inaudibleContext)) {
                    inaudibleContext.setLastRepeatableAnswerId(answer.getId());
                }
            }
        }
    }

    private boolean isInaudibleRepeatable(AnswerResult answer, PredictResult predictResult) {
        Set<String> knowledgeNameSet = new HashSet<>(Arrays.asList("说慢一点", "快点说", "客户问AI是否听得到", "打断", "赞美AI", "调戏AI", "机器人"));
        Set<String> intentNameSet = new HashSet<>(Arrays.asList("说慢一点", "快点说", "客户问AI是否听得到", "打断", "赞美AI", "调戏AI", "听不清楚AI说话", "AI重复上句语音", "机器人"));
        Set<String> specialAnswerNameSet = new HashSet<>(Arrays.asList("客户无应答处理", "AI无法应答处理", "AI重复上句语音", "听不清楚AI说话"));
        if (Objects.nonNull(answer) && Objects.nonNull(answer.getLocate())) {
            AnswerLocateBO locate = answer.getLocate();
            switch (locate.getAnswerSource()) {
                case KNOWLEDGE:
                    if (knowledgeNameSet.contains(locate.getKnowledgeName())) {
                        return false;
                    }
                    break;
                case SPECIAL_ANSWER:
                    if (specialAnswerNameSet.contains(locate.getSpecialAnswerConfigName())) {
                        return false;
                    }
                    break;
                default:
                    break;
            }
        }
        if (Objects.nonNull(predictResult) && StringUtils.isNotBlank(predictResult.getIntentName())) {
            return !intentNameSet.contains(predictResult.getIntentName());
        }
        return true;
    }

    private void updateAiRepeatAnswer(SessionContext sessionContext, EventContext eventContext, AnswerResult answer) {
        if (isInaudibleRepeatable(answer, eventContext.getPredictResult())) {
            AnswerLocateBO locate = answer.getLocate();
            if (Objects.nonNull(locate)) {
                RepeatAnswerContext repeatAnswerContext = sessionContext.getRepeatAnswerContext();
                if (Objects.nonNull(repeatAnswerContext)) {
                    repeatAnswerContext.setPreAnswerId(answer.getId());
                    repeatAnswerContext.setLastRepeatableAnswerId(answer.getId());
                    if (ApplicationConstant.enableDebug) {
                        log.info("update repeatAnswerContext, locate={}, preAnswerId={}", locate, repeatAnswerContext.getPreAnswerId());
                    }
                } else {
                    log.info("未开启重复上一句, 无需处理");
                }
            }
        }
    }

    @Deprecated
    private boolean isRepeatable(AnswerResult answer) {
        return REPEATABLE_SOURCE_SET.contains(answer.getAnswerSource());
    }
}
