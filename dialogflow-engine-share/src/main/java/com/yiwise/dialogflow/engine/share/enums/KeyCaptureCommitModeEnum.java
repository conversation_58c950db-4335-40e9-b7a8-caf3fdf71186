package com.yiwise.dialogflow.engine.share.enums;

import com.yiwise.base.model.enums.CodeDescEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum KeyCaptureCommitModeEnum implements CodeDescEnum {

    /**
     * 按#号后提交
     */
    HASH(0, "按#号后提交"),

    /**
     * 按键后立即提交
     */
    ANY(1, "按键后立即提交"),
    ;

    private final Integer code;
    private final String desc;

    public static boolean isHash(KeyCaptureCommitModeEnum commitMode) {
        return HASH.equals(commitMode);
    }

    public static boolean isAny(KeyCaptureCommitModeEnum commitMode) {
        return ANY.equals(commitMode);
    }
}
