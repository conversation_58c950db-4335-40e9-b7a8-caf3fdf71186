package com.yiwise.dialogflow.engine.share;

import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.middleware.tts.enums.VarInterpretTypeEnum;
import com.yiwise.dialogflow.engine.share.model.BotAsrConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 返回给交互层使用的机器人数据, 主要就是音频相关的数据, 需要交互层提前处理
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
public class BotMetaData {

    Long botId;

    Long dialogFlowId;

    /**
     * bot类型, 旗舰版, 轻量版, 轻量版模板, 现在三个类型都支持在任务中进行通话
     */
    V3BotTypeEnum botType;

    /**
     * 意向等级标签id
     */
    Long intentLevelTagId;

    /**
     * 快照版本
     */
    Integer version;

    /**
     * 用途
     */
    RobotSnapshotUsageTargetEnum usageTarget;

    /**
     * 名称
     */
    String name;

    /**
     * 动态变量名称列表
     */
    Set<String> dynamicVariableNameSet;

    /**
     * 所有变量名称集合
     */
    Set<String> allVariableNameSet;

    /**
     * 所有答案列表
     */
    List<AnswerAudioMiddleResult> allAnswerList;

    /**
     * 变量名称和发音类型的映射
     */
    Map<String, VarInterpretTypeEnum> varNameInterpretTypeMap;

    /**
     * 机器人语音配置信息
     */
    BotAudioConfig botAudioConfig;

    Boolean enableBackground;

    String backgroundUrl;

    String backgroundFullUrl;

    Double userSilenceThreshold;

    Integer toneInterruptPercent;

    Boolean enableToneInterrupt;

    String nlpModelVersion;

    private Boolean enableInputMerge;

    private List<String> toneWordList;

    private String industryType;

    private String industrySubType;

    BotAsrConfig botAsrConfig;

    /**
     * 模板变量名称-默认值映射
     */
    Map<String, String> templateVarNameValueMap;

    /**
     * 是否开启LLM对话, 如果开启了的话, 音频播放等需要选择对应的 Manager
     */
    @Deprecated
    private boolean enableLLMChat;

    private boolean enableFragmentAudioPlayer;

    /**
     * 运营商在通话前 N 秒增加的提示音正则匹配列表
     */
    private List<String> operatorPromptAudioRegexList;

    private List<String> operatorPromptAudioPreRegexList;

    /**
     * 仅在前 N 秒才检测信息, 如果是 < 1 则不检测
     */
    private Integer checkPromptAudioBeforeSeconds;

    /**
     * 禁用运营商提示音检测
     */
    private Boolean disablePromptAudioCheck;
}
