package com.yiwise.dialogflow.engine.share.action;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.yiwise.dialogflow.engine.share.enums.ActionScopeEnum;
import com.yiwise.dialogflow.engine.share.enums.ActionTypeEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type", visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(value = JumpAction.class, name = ChatAction.JUMP),
        @JsonSubTypes.Type(value = WaitAction.class, name = ChatAction.WAIT),
        @JsonSubTypes.Type(value = HangupAction.class, name = ChatAction.HANGUP),
        @JsonSubTypes.Type(value = PausePlayAction.class, name = ChatAction.PAUSE_PLAY),
        @JsonSubTypes.Type(value = UninterruptedAction.class, name = ChatAction.UNINTERRUPTED),
        @JsonSubTypes.Type(value = IgnoreInputAction.class, name = ChatAction.IGNORE_INPUT),
        @JsonSubTypes.Type(value = AddWechatAction.class, name = ChatAction.ADD_WECHAT),
        @JsonSubTypes.Type(value = SwitchToHumanServiceAction.class, name = ChatAction.SWITCH_TO_HUMAN_SERVICE),
        @JsonSubTypes.Type(value = WaitUserSayFinishAction.class, name = ChatAction.WAIT_USER_SAY_FINISH),
        @JsonSubTypes.Type(value = HttpRequestAction.class, name = ChatAction.HTTP_REQUEST),
        @JsonSubTypes.Type(value = SendSmsAction.class, name = ChatAction.SEND_SMS),
        @JsonSubTypes.Type(value = LLMRequestAction.class, name = ChatAction.LLM_REQUEST),
        @JsonSubTypes.Type(value = HumanInterventionAction.class, name = ChatAction.HUMAN_INTERVENTION)
})
public class ChatAction {

    public static final String JUMP = "JUMP";
    public static final String WAIT = "WAIT";
    public static final String HANGUP = "HANGUP";
    public static final String PAUSE_PLAY = "PAUSE_PLAY";
    public static final String UNINTERRUPTED = "UNINTERRUPTED";
    public static final String IGNORE_INPUT = "IGNORE_INPUT";
    public static final String ADD_WECHAT = "ADD_WECHAT";
    public static final String SWITCH_TO_HUMAN_SERVICE = "SWITCH_TO_HUMAN_SERVICE";
    public static final String WAIT_USER_SAY_FINISH = "WAIT_USER_SAY_FINISH";
    public static final String HTTP_REQUEST = "HTTP_REQUEST";
    public static final String SEND_SMS = "SEND_SMS";
    public static final String LLM_REQUEST = "LLM_REQUEST";
    public static final String HUMAN_INTERVENTION = "HUMAN_INTERVENTION";

    ActionScopeEnum scope;

    ActionTypeEnum type;

    public ChatAction() {
    }

    public ChatAction(ActionTypeEnum type, ActionScopeEnum scope) {
        this.scope = scope;
        this.type = type;
    }
}
