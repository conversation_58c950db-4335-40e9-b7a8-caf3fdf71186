package com.yiwise.dialogflow.engine.share.action;

import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.enums.JumpTargetEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.yiwise.dialogflow.engine.share.enums.ActionScopeEnum;
import com.yiwise.dialogflow.engine.share.enums.ActionTypeEnum;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JumpAction extends ChatAction {

    public JumpAction(AnswerSourceEnum jumpSource, String jumpSourceId) {
        setType(ActionTypeEnum.JUMP);
        setScope(ActionScopeEnum.ENGINE);
        this.jumpSourceType = jumpSource;
        this.jumpSourceId = jumpSourceId;
    }

    AnswerSourceEnum jumpSourceType;

    String jumpSourceId;

    JumpTargetEnum jumpTarget;

    String jumpStepId;
}
