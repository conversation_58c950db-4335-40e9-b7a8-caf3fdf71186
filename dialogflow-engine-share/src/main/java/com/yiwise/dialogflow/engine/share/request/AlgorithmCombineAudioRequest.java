package com.yiwise.dialogflow.engine.share.request;

import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.model.AlgorithmCombineAudioRequestPart;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlgorithmCombineAudioRequest implements Serializable {

    Long botId;

    Integer version;

    RobotSnapshotUsageTargetEnum usageTarget;

    String voice;

    List<AlgorithmCombineAudioRequestPart> elementList;

    String logId;
}
