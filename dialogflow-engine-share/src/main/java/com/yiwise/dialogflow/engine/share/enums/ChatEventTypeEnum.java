package com.yiwise.dialogflow.engine.share.enums;

/**
 * 目前的事件设计是按照语音交互进行设计的, 比如语音交互有ai播放完成的事件,
 * 如果是在后面可能的面向纯文本的交互上是不存在ai播放完成的
 * 这时候就可以顺序处理两个事件, 然后合并响应就可以了
 * <AUTHOR>
 */
public enum ChatEventTypeEnum {
    /**
     * 进入
     */
    ENTER,
    /**
     * 用户输入
     */
    USER_SAY,
    /**
     * 用户输入完成
     */
    USER_SAY_FINISH,
    /**
     * 用户无应答
     */
    USER_SILENCE,
    /**
     * AI播放完成
     */
    AI_SAY_FINISH,

    /**
     * 快速挂断
     */
    FAST_HANGUP,

    /**
     * 传递http响应
     */
    DELIVER_HTTP_RESPONSE,

    /**
     * 按键采集成功
     */
    KEY_CAPTURE_SUCCESS,

    /**
     * 按键采集失败
     */
    KEY_CAPTURE_FAILED,

    /**
     * 大模型生成请求
     */
    LLM_REQUEST,
    ;
}
