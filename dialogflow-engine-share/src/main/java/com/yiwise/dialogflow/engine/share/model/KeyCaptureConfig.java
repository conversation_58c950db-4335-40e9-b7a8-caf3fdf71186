package com.yiwise.dialogflow.engine.share.model;

import com.yiwise.dialogflow.engine.share.enums.KeyCaptureCommitModeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KeyCaptureConfig implements Serializable {

    /**
     * 按键收集超时时间,单位秒
     */
    Integer timeout;

    /**
     * 按键提交方式
     */
    KeyCaptureCommitModeEnum commitMode;

    /**
     * 按键采集是否开启按*重播
     */
    Boolean enableReplay;
}
