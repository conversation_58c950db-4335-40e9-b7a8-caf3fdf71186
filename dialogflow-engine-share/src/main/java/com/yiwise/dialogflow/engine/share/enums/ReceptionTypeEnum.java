package com.yiwise.dialogflow.engine.share.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 */
public enum ReceptionTypeEnum implements CodeDescEnum {
    STEP(1, "主流程"),
    KNOWLEDGE(2, "问答知识"),
    AI_UNKNOWN(3, "ai无法应答"),
    AI_REPEAT(4,"重复上一句"),
    USER_SILENCE(5, "用户无应答");

    ReceptionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    Integer code;
    String desc;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
