package com.yiwise.dialogflow;

import com.yiwise.base.common.application.StartFailedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoReactiveAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;


@Slf4j
@EnableFeignClients(basePackages = "com.yiwise")
@EnableDiscoveryClient
@SpringBootApplication(exclude = {
        MongoReactiveAutoConfiguration.class
})
public class DialogFlowApiWebBootstrap {

    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplicationBuilder(DialogFlowApiWebBootstrap.class)
                .build();
        springApplication.addListeners(new StartFailedEvent());
        springApplication.run(args);
        log.info("=============================spring boot start successful !=============================");
    }

}
