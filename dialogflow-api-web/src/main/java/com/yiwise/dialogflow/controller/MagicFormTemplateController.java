package com.yiwise.dialogflow.controller;

import com.yiwise.dialogflow.api.MagicFormApi;
import com.yiwise.dialogflow.api.dto.response.activity.MagicFormTemplateDTO;
import com.yiwise.dialogflow.service.magic.MagicFormTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
public class MagicFormTemplateController implements MagicFormApi {

    @Resource
    private MagicFormTemplateService magicFormTemplateService;

    @Override
    public MagicFormTemplateDTO getLastPublishedTemplateByBotId(Long botId) {
        return magicFormTemplateService.getLastPublishedMagicFormTemplateDTO(botId);
    }
}
