package com.yiwise.dialogflow.controller;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.AudioApi;
import com.yiwise.dialogflow.api.dto.response.audio.AudioUploadResult;
import com.yiwise.dialogflow.api.dto.response.audio.BotTotalAudioDetail;
import com.yiwise.dialogflow.api.dto.response.audio.TtsVoiceItem;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.helper.AddOssPrefixSerializer;
import com.yiwise.dialogflow.service.AnswerAudioManagerService;
import com.yiwise.dialogflow.service.BotRefService;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.BotTotalAnswerAudioService;
import com.yiwise.middleware.tts.enums.TtsVoiceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Validated
@RestController
public class AudioApiController implements AudioApi {

    @Resource
    private BotTotalAnswerAudioService botTotalAnswerAudioService;

    @Resource
    private AnswerAudioManagerService answerAudioManagerService;

    @Resource
    private BotRefService botRefService;

    @Resource
    private BotService botService;

    @InnerOnly
    @NoLogin
    @Override
    public BotTotalAudioDetail getTotalAnswerAudioByDialogFlowId(Long dialogFlowId) {
        botService.checkBotExistAndThrowByDialogFlowId(dialogFlowId);
        return botTotalAnswerAudioService.getTotalAnswerAudioByDialogFlowId(dialogFlowId);
    }

    @Override
    public AudioUploadResult upload(Long tenantId, Long userId, Long dialogFlowId, String text, String ossKey) {
        if (!botRefService.checkHasPermissionByDialogFlowId(dialogFlowId, tenantId)) {
            throw new ComException(ComErrorCode.FORBIDDEN, "无权限");
        }
        return answerAudioManagerService.uploadAndUpdate(dialogFlowId, text, ossKey, userId);
    }

    @Override
    public List<TtsVoiceItem> getTianrunSupportTtsVoiceList() {
        if (StringUtils.isBlank(ApplicationConstant.TIANRUN_TTS_VOICE_LIST)) {
            return Collections.emptyList();
        }
        // key voice, value: auditionUrl
        List<TtsVoiceItem> originConfigList  = JsonUtils.string2ListObject(ApplicationConstant.TIANRUN_TTS_VOICE_LIST, TtsVoiceItem.class);
        return originConfigList.stream()
                .map(origin -> {
                    try {
                        TtsVoiceEnum voice = TtsVoiceEnum.valueOf(origin.getVoice());
                        origin.setAuditionUrl(AddOssPrefixSerializer.getAddOssPrefixUrl(origin.getAuditionUrl()));
                        return origin;
                    } catch (Exception e) {
                        return null;
                    }
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
