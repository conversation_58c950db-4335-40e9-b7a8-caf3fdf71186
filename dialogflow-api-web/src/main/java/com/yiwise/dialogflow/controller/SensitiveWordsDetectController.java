package com.yiwise.dialogflow.controller;

import com.yiwise.dialogflow.api.SensitiveWordsApi;
import com.yiwise.dialogflow.api.dto.request.SensitiveWordDetectRequest;
import com.yiwise.dialogflow.api.dto.response.SensitiveWordsDetectResultBO;
import com.yiwise.dialogflow.service.SensitiveWordsService;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Service
@RestController
public class SensitiveWordsDetectController implements SensitiveWordsApi {

    @Resource
    private SensitiveWordsService sensitiveWordsService;

    @Override
    public List<SensitiveWordsDetectResultBO> batchDetect(SensitiveWordDetectRequest request) {
        return sensitiveWordsService.batchDetect(request.getTextList());
    }

    @Override
    public SensitiveWordsDetectResultBO detect(String text) {
        return sensitiveWordsService.detect(text);
    }
}
