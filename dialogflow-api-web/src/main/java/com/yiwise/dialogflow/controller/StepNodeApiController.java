package com.yiwise.dialogflow.controller;

import com.yiwise.dialogflow.api.StepNodeApi;
import com.yiwise.dialogflow.service.StepNodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
public class StepNodeApiController implements StepNode<PERSON>pi {

    @Resource
    private StepNodeService stepNodeService;

    @Override
    public Boolean containsSwitchToHumanServiceNodeByDialogFlowId(Long dialogFlowId) {
        return stepNodeService.containsSwitchToHumanServiceNode(dialogFlowId);
    }
}
