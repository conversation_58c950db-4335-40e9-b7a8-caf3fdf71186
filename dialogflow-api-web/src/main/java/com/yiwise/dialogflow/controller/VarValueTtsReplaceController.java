package com.yiwise.dialogflow.controller;

import com.yiwise.dialogflow.api.VarValueTtsReplaceApi;
import com.yiwise.dialogflow.api.dto.response.VarValueTtsReplaceDTO;
import com.yiwise.dialogflow.service.VarValueTtsReplaceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
public class VarValueTtsReplaceController implements VarValueTtsReplaceApi {

    @Resource
    private VarValueTtsReplaceService varValueTtsReplaceService;

    @Override
    public List<VarValueTtsReplaceDTO> queryByTenantId(Long tenantId) {
        return varValueTtsReplaceService.queryAllByTenantId(tenantId);
    }
}
