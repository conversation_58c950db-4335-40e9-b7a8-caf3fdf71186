spring:
  codec:
    max-in-memory-size: 2MB
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: pre
  application:
    name: aicc-platform-dialogflow-api-web
  cloud:
    nacos:
      discovery:
        server-addr: mse-2cd4d9c0-nacos-ans.mse.aliyuncs.com:8848
        namespace: b87a6783-f6c4-4817-ae27-caa886af34ac
        heart-beat-timeout: 5000
        heart-beat-interval: 2000
server:
  port: 11031
eureka:
  client:
    # 注册地址
    service-url:
      defaultZone: http://localhost:10010/eureka/
    # 首次注册时间，默认40s
    initial-instance-info-replication-interval-seconds: 15
    # client拉取可用服务清单的间隔时间，默认30s
    registry-fetch-interval-seconds: 5
  instance:
    # 使用IP注册服务
    prefer-ip-address: true
    # 服务过期时间，默认90s，这边取了两个心跳间隔的时间
    lease-expiration-duration-in-seconds: 10
    # 服务刷新时间，每隔这个时间会主动心跳一次，默认30s
    lease-renewal-interval-in-seconds: 5

feign:
  hystrix:
    enabled: true

hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: false
        # 支持ThreadLocal参数绑定
        isolation:
          strategy: SEMAPHORE

app:
  # 使用的 Apollo 的项目（应用）编号
  id: aicc-platform-dialogflow
apollo:
  # Apollo Meta Server 地址
  meta: http://***************:8080
  cache-dir: /tmp/apollo-cache-dir
  bootstrap:
    # 是否开启 Apollo 配置预加载功能。默认为 false。
    enabled: "true"
    # 是否开启 Apollo 支持日志级别的加载时机。默认为 false。
    eagerLoad:
      enabled: "true"
    # 使用的 Apollo 的命名空间，默认为 application。
    namespaces: application

