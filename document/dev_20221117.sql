create table asr_language
(
    asr_language_id     bigint auto_increment,
    language_name       varchar(32)                         not null comment '语言类型',
    default_provider_id bigint                              not null comment '默认供应商ID',
    create_time         timestamp default current_timestamp not null,
    update_time         timestamp default current_timestamp not null on update CURRENT_TIMESTAMP,
    primary key (asr_language_id)
) comment 'asr语言类型';

create table asr_provider
(
    asr_provider_id bigint auto_increment,
    provider        varchar(20)                         null comment '供应商',
    provider_name   varchar(20)                         null comment '供应商名称',
    language        varchar(32)                         not null comment '语言类型',
    model_id        varchar(64)                         not null comment '模型id',
    name            varchar(63)                         not null comment '基础模型名称',
    description     varchar(64)                         null comment '模型描述',
    params          json                                null comment 'ASR参数',
    create_time     timestamp default current_timestamp not null,
    update_time     timestamp default current_timestamp not null on update CURRENT_TIMESTAMP,
    primary key (asr_provider_id)
) comment 'asr基础模型';

create table asr_vocab_detail
(
    asr_vocab_id   bigint auto_increment
        primary key,
    name           varchar(20)                          not null comment '热词组名称',
    description    varchar(200)                         null comment '描述',
    content        varchar(1024)                        null comment '热词组内容',
    status         tinyint(2) default 1                 not null comment '开启状态（0-停用；1-开启）',
    create_user_id bigint     default 0                 not null,
    update_user_id bigint     default 0                 not null,
    create_time    timestamp  default CURRENT_TIMESTAMP not null,
    update_time    timestamp  default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
) comment 'asr热词组信息表';

create table asr_vocab_provider_relation
(
    asr_vocab_provider_relation_id bigint auto_increment
        primary key,
    asr_vocab_id                   bigint                              not null comment '热词组id',
    provider                       tinyint                             not null comment '供应商名称',
    vocabulary_id                  varchar(64)                         not null comment '供应商侧热词组id',
    create_user_id                 bigint    default 0                 not null,
    update_user_id                 bigint    default 0                 not null,
    create_time                    timestamp default CURRENT_TIMESTAMP not null,
    update_time                    timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    constraint asr_vocab_provider_relation_asr_vocab_id_provider_uindex
        unique (asr_vocab_id, provider)
) comment '热词组与厂商关联表';

create table asr_self_learning_detail
(
    asr_self_learning_detail_id bigint auto_increment,
    name                        varchar(20)                          not null comment '模型名称',
    description                 varchar(200)                         null comment '描述',
    status                      tinyint(2) default 1                 not null comment '开启状态（0-停用；1-开启）',
    create_user_id              bigint     default 0                 not null,
    update_user_id              bigint     default 0                 not null,
    url                         varchar(512)                         not null comment '语料地址',
    create_time                 timestamp  default current_timestamp not null,
    update_time                 timestamp  default current_timestamp not null on update CURRENT_TIMESTAMP,
    constraint asr_self_learning_detail_pk
        primary key (asr_self_learning_detail_id)
) comment 'asr自学习模型信息表';

create table asr_self_learning_provider_relation
(
    asr_self_learning_provider_relation_id bigint auto_increment
        primary key,
    asr_self_learning_id                   bigint                              not null comment '自学习模型主键',
    provider                               tinyint                             not null comment '供应商',
    provider_model_id                      varchar(64)                         null comment '供应商侧模型id',
    train_status                           tinyint                             not null comment '训练状态（未训练-0；训练成功-1；训练失败-2；已上线-3；已下线-4）',
    create_user_id                         bigint    default 0                 not null,
    update_user_id                         bigint    default 0                 not null,
    create_time                            timestamp default CURRENT_TIMESTAMP not null,
    update_time                            timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    data_id                                varchar(64)                         null comment '厂商侧数据集id（腾讯无此参数）',
    constraint provider_relation_asr_self_learning_id_provider_uindex
        unique (asr_self_learning_id, provider)
) comment '自学习模型与供应商关联表';

alter table bot
    add column asr_language_id bigint default 1 not null comment 'asr语言ID',
    add column asr_provider_id bigint default 1 not null comment 'asr基础模型id',
    add column asr_vocab_id bigint null comment '热词组id',
    add column asr_self_learning_detail_id bigint null comment '自学习模型id',
    add column asr_error_correction_detail_id varchar(64) null comment '纠错模型id';

create index bot_asr_error_correction_detail_id_index
    on bot (asr_error_correction_detail_id);

create index bot_asr_self_learning_detail_id_index
    on bot (asr_self_learning_detail_id);

create index bot_asr_vocab_id_index
    on bot (asr_vocab_id);

INSERT INTO `asr_language` (`asr_language_id`, `language_name`, `default_provider_id`) VALUES (1, '普通话', 1);

INSERT INTO `aicc_platform_dialogflow`.`asr_provider` (`asr_provider_id`, `provider`, `provider_name`, `language`, `model_id`, `name`, `description`, `params`) VALUES (1, '0', '阿里', '普通话', '4QQmua3uHGKGwsYD', '阿里-普通话', NULL, NULL);
INSERT INTO `aicc_platform_dialogflow`.`asr_provider` (`asr_provider_id`, `provider`, `provider_name`, `language`, `model_id`, `name`, `description`, `params`) VALUES (2, '2', '腾讯', '普通话', '8k_zh', '腾讯-普通话', NULL, NULL);
